define("local_offermanager/app/app-lazy",["core/config","tool_lfxp/ajax","core/notification"],function(Vg,Rg,Fg){"use strict";function Lg(e){const t=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e){for(const s in e)if(s!=="default"){const i=Object.getOwnPropertyDescriptor(e,s);Object.defineProperty(t,s,i.get?i:{enumerable:!0,get:()=>e[s]})}}return t.default=e,Object.freeze(t)}const Ug=Lg(Vg);/**
* @vue/shared v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function rr(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const et={}.NODE_ENV!=="production"?Object.freeze({}):{},Dn={}.NODE_ENV!=="production"?Object.freeze([]):[],xt=()=>{},Bg=()=>!1,oo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ai=e=>e.startsWith("onUpdate:"),ht=Object.assign,Ya=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},$g=Object.prototype.hasOwnProperty,Ke=(e,t)=>$g.call(e,t),pe=Array.isArray,Yr=e=>io(e)==="[object Map]",Tn=e=>io(e)==="[object Set]",Mc=e=>io(e)==="[object Date]",xe=e=>typeof e=="function",ct=e=>typeof e=="string",Ts=e=>typeof e=="symbol",Ye=e=>e!==null&&typeof e=="object",Ja=e=>(Ye(e)||xe(e))&&xe(e.then)&&xe(e.catch),Pc=Object.prototype.toString,io=e=>Pc.call(e),Qa=e=>io(e).slice(8,-1),kc=e=>io(e)==="[object Object]",Xa=e=>ct(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,ao=rr(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),qg=rr("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),li=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},Hg=/-(\w)/g,Wt=li(e=>e.replace(Hg,(t,s)=>s?s.toUpperCase():"")),Wg=/\B([A-Z])/g,Sr=li(e=>e.replace(Wg,"-$1").toLowerCase()),Jr=li(e=>e.charAt(0).toUpperCase()+e.slice(1)),Qr=li(e=>e?`on${Jr(e)}`:""),Dr=(e,t)=>!Object.is(e,t),Nn=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},lo=(e,t,s,i=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:i,value:s})},ui=e=>{const t=parseFloat(e);return isNaN(t)?e:t},jg=e=>{const t=ct(e)?Number(e):NaN;return isNaN(t)?e:t};let Vc;const uo=()=>Vc||(Vc=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function is(e){if(pe(e)){const t={};for(let s=0;s<e.length;s++){const i=e[s],n=ct(i)?Zg(i):is(i);if(n)for(const a in n)t[a]=n[a]}return t}else if(ct(e)||Ye(e))return e}const zg=/;(?![^(]*\))/g,Gg=/:([^]+)/,Kg=/\/\*[^]*?\*\//g;function Zg(e){const t={};return e.replace(Kg,"").split(zg).forEach(s=>{if(s){const i=s.split(Gg);i.length>1&&(t[i[0].trim()]=i[1].trim())}}),t}function de(e){let t="";if(ct(e))t=e;else if(pe(e))for(let s=0;s<e.length;s++){const i=de(e[s]);i&&(t+=i+" ")}else if(Ye(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const Yg="html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot",Jg="svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view",Qg="annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics",Xg=rr(Yg),e_=rr(Jg),t_=rr(Qg),s_=rr("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function Rc(e){return!!e||e===""}function r_(e,t){if(e.length!==t.length)return!1;let s=!0;for(let i=0;s&&i<e.length;i++)s=co(e[i],t[i]);return s}function co(e,t){if(e===t)return!0;let s=Mc(e),i=Mc(t);if(s||i)return s&&i?e.getTime()===t.getTime():!1;if(s=Ts(e),i=Ts(t),s||i)return e===t;if(s=pe(e),i=pe(t),s||i)return s&&i?r_(e,t):!1;if(s=Ye(e),i=Ye(t),s||i){if(!s||!i)return!1;const n=Object.keys(e).length,a=Object.keys(t).length;if(n!==a)return!1;for(const u in e){const c=e.hasOwnProperty(u),h=t.hasOwnProperty(u);if(c&&!h||!c&&h||!co(e[u],t[u]))return!1}}return String(e)===String(t)}function el(e,t){return e.findIndex(s=>co(s,t))}const Fc=e=>!!(e&&e.__v_isRef===!0),q=e=>ct(e)?e:e==null?"":pe(e)||Ye(e)&&(e.toString===Pc||!xe(e.toString))?Fc(e)?q(e.value):JSON.stringify(e,Lc,2):String(e),Lc=(e,t)=>Fc(t)?Lc(e,t.value):Yr(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[i,n],a)=>(s[tl(i,a)+" =>"]=n,s),{})}:Tn(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>tl(s))}:Ts(t)?tl(t):Ye(t)&&!pe(t)&&!kc(t)?String(t):t,tl=(e,t="")=>{var s;return Ts(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function qs(e,...t){console.warn(`[Vue warn] ${e}`,...t)}let es;class Uc{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=es,!t&&es&&(this.index=(es.scopes||(es.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=es;try{return es=this,t()}finally{es=s}}else({}).NODE_ENV!=="production"&&qs("cannot run an inactive effect scope.")}on(){++this._on===1&&(this.prevScope=es,es=this)}off(){this._on>0&&--this._on===0&&(es=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,i;for(s=0,i=this.effects.length;s<i;s++)this.effects[s].stop();for(this.effects.length=0,s=0,i=this.cleanups.length;s<i;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,i=this.scopes.length;s<i;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function n_(e){return new Uc(e)}function o_(){return es}let tt;const sl=new WeakSet;class Bc{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,es&&es.active&&es.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,sl.has(this)&&(sl.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||qc(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Gc(this),Hc(this);const t=tt,s=Ns;tt=this,Ns=!0;try{return this.fn()}finally{({}).NODE_ENV!=="production"&&tt!==this&&qs("Active effect was not restored correctly - this is likely a Vue internal bug."),Wc(this),tt=t,Ns=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)il(t);this.deps=this.depsTail=void 0,Gc(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?sl.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){ol(this)&&this.run()}get dirty(){return ol(this)}}let $c=0,fo,ho;function qc(e,t=!1){if(e.flags|=8,t){e.next=ho,ho=e;return}e.next=fo,fo=e}function rl(){$c++}function nl(){if(--$c>0)return;if(ho){let t=ho;for(ho=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;fo;){let t=fo;for(fo=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(i){e||(e=i)}t=s}}if(e)throw e}function Hc(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Wc(e){let t,s=e.depsTail,i=s;for(;i;){const n=i.prevDep;i.version===-1?(i===s&&(s=n),il(i),i_(i)):t=i,i.dep.activeLink=i.prevActiveLink,i.prevActiveLink=void 0,i=n}e.deps=t,e.depsTail=s}function ol(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(jc(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function jc(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===po)||(e.globalVersion=po,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!ol(e))))return;e.flags|=2;const t=e.dep,s=tt,i=Ns;tt=e,Ns=!0;try{Hc(e);const n=e.fn(e._value);(t.version===0||Dr(n,e._value))&&(e.flags|=128,e._value=n,t.version++)}catch(n){throw t.version++,n}finally{tt=s,Ns=i,Wc(e),e.flags&=-3}}function il(e,t=!1){const{dep:s,prevSub:i,nextSub:n}=e;if(i&&(i.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=i,e.nextSub=void 0),{}.NODE_ENV!=="production"&&s.subsHead===e&&(s.subsHead=n),s.subs===e&&(s.subs=i,!i&&s.computed)){s.computed.flags&=-5;for(let a=s.computed.deps;a;a=a.nextDep)il(a,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function i_(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let Ns=!0;const zc=[];function Is(){zc.push(Ns),Ns=!1}function As(){const e=zc.pop();Ns=e===void 0?!0:e}function Gc(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=tt;tt=void 0;try{t()}finally{tt=s}}}let po=0;class a_{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class al{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0,{}.NODE_ENV!=="production"&&(this.subsHead=void 0)}track(t){if(!tt||!Ns||tt===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==tt)s=this.activeLink=new a_(tt,this),tt.deps?(s.prevDep=tt.depsTail,tt.depsTail.nextDep=s,tt.depsTail=s):tt.deps=tt.depsTail=s,Kc(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const i=s.nextDep;i.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=i),s.prevDep=tt.depsTail,s.nextDep=void 0,tt.depsTail.nextDep=s,tt.depsTail=s,tt.deps===s&&(tt.deps=i)}return{}.NODE_ENV!=="production"&&tt.onTrack&&tt.onTrack(ht({effect:tt},t)),s}trigger(t){this.version++,po++,this.notify(t)}notify(t){rl();try{if({}.NODE_ENV!=="production")for(let s=this.subsHead;s;s=s.nextSub)s.sub.onTrigger&&!(s.sub.flags&8)&&s.sub.onTrigger(ht({effect:s.sub},t));for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{nl()}}}function Kc(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let i=t.deps;i;i=i.nextDep)Kc(i)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),{}.NODE_ENV!=="production"&&e.dep.subsHead===void 0&&(e.dep.subsHead=e),e.dep.subs=e}}const ll=new WeakMap,Xr=Symbol({}.NODE_ENV!=="production"?"Object iterate":""),ul=Symbol({}.NODE_ENV!=="production"?"Map keys iterate":""),mo=Symbol({}.NODE_ENV!=="production"?"Array iterate":"");function St(e,t,s){if(Ns&&tt){let i=ll.get(e);i||ll.set(e,i=new Map);let n=i.get(s);n||(i.set(s,n=new al),n.map=i,n.key=s),{}.NODE_ENV!=="production"?n.track({target:e,type:t,key:s}):n.track()}}function Hs(e,t,s,i,n,a){const u=ll.get(e);if(!u){po++;return}const c=h=>{h&&({}.NODE_ENV!=="production"?h.trigger({target:e,type:t,key:s,newValue:i,oldValue:n,oldTarget:a}):h.trigger())};if(rl(),t==="clear")u.forEach(c);else{const h=pe(e),_=h&&Xa(s);if(h&&s==="length"){const p=Number(i);u.forEach((g,w)=>{(w==="length"||w===mo||!Ts(w)&&w>=p)&&c(g)})}else switch((s!==void 0||u.has(void 0))&&c(u.get(s)),_&&c(u.get(mo)),t){case"add":h?_&&c(u.get("length")):(c(u.get(Xr)),Yr(e)&&c(u.get(ul)));break;case"delete":h||(c(u.get(Xr)),Yr(e)&&c(u.get(ul)));break;case"set":Yr(e)&&c(u.get(Xr));break}}nl()}function In(e){const t=Ae(e);return t===e?t:(St(t,"iterate",mo),jt(e)?t:t.map(Ft))}function ci(e){return St(e=Ae(e),"iterate",mo),e}const l_={__proto__:null,[Symbol.iterator](){return cl(this,Symbol.iterator,Ft)},concat(...e){return In(this).concat(...e.map(t=>pe(t)?In(t):t))},entries(){return cl(this,"entries",e=>(e[1]=Ft(e[1]),e))},every(e,t){return nr(this,"every",e,t,void 0,arguments)},filter(e,t){return nr(this,"filter",e,t,s=>s.map(Ft),arguments)},find(e,t){return nr(this,"find",e,t,Ft,arguments)},findIndex(e,t){return nr(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return nr(this,"findLast",e,t,Ft,arguments)},findLastIndex(e,t){return nr(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return nr(this,"forEach",e,t,void 0,arguments)},includes(...e){return dl(this,"includes",e)},indexOf(...e){return dl(this,"indexOf",e)},join(e){return In(this).join(e)},lastIndexOf(...e){return dl(this,"lastIndexOf",e)},map(e,t){return nr(this,"map",e,t,void 0,arguments)},pop(){return go(this,"pop")},push(...e){return go(this,"push",e)},reduce(e,...t){return Zc(this,"reduce",e,t)},reduceRight(e,...t){return Zc(this,"reduceRight",e,t)},shift(){return go(this,"shift")},some(e,t){return nr(this,"some",e,t,void 0,arguments)},splice(...e){return go(this,"splice",e)},toReversed(){return In(this).toReversed()},toSorted(e){return In(this).toSorted(e)},toSpliced(...e){return In(this).toSpliced(...e)},unshift(...e){return go(this,"unshift",e)},values(){return cl(this,"values",Ft)}};function cl(e,t,s){const i=ci(e),n=i[t]();return i!==e&&!jt(e)&&(n._next=n.next,n.next=()=>{const a=n._next();return a.value&&(a.value=s(a.value)),a}),n}const u_=Array.prototype;function nr(e,t,s,i,n,a){const u=ci(e),c=u!==e&&!jt(e),h=u[t];if(h!==u_[t]){const g=h.apply(e,a);return c?Ft(g):g}let _=s;u!==e&&(c?_=function(g,w){return s.call(this,Ft(g),w,e)}:s.length>2&&(_=function(g,w){return s.call(this,g,w,e)}));const p=h.call(u,_,i);return c&&n?n(p):p}function Zc(e,t,s,i){const n=ci(e);let a=s;return n!==e&&(jt(e)?s.length>3&&(a=function(u,c,h){return s.call(this,u,c,h,e)}):a=function(u,c,h){return s.call(this,u,Ft(c),h,e)}),n[t](a,...i)}function dl(e,t,s){const i=Ae(e);St(i,"iterate",mo);const n=i[t](...s);return(n===-1||n===!1)&&gi(s[0])?(s[0]=Ae(s[0]),i[t](...s)):n}function go(e,t,s=[]){Is(),rl();const i=Ae(e)[t].apply(e,s);return nl(),As(),i}const c_=rr("__proto__,__v_isRef,__isVue"),Yc=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Ts));function d_(e){Ts(e)||(e=String(e));const t=Ae(this);return St(t,"has",e),t.hasOwnProperty(e)}class Jc{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,i){if(s==="__v_skip")return t.__v_skip;const n=this._isReadonly,a=this._isShallow;if(s==="__v_isReactive")return!n;if(s==="__v_isReadonly")return n;if(s==="__v_isShallow")return a;if(s==="__v_raw")return i===(n?a?nd:rd:a?sd:td).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(i)?t:void 0;const u=pe(t);if(!n){let h;if(u&&(h=l_[s]))return h;if(s==="hasOwnProperty")return d_}const c=Reflect.get(t,s,wt(t)?t:i);return(Ts(s)?Yc.has(s):c_(s))||(n||St(t,"get",s),a)?c:wt(c)?u&&Xa(s)?c:c.value:Ye(c)?n?id(c):pi(c):c}}class Qc extends Jc{constructor(t=!1){super(!1,t)}set(t,s,i,n){let a=t[s];if(!this._isShallow){const h=js(a);if(!jt(i)&&!js(i)&&(a=Ae(a),i=Ae(i)),!pe(t)&&wt(a)&&!wt(i))return h?!1:(a.value=i,!0)}const u=pe(t)&&Xa(s)?Number(s)<t.length:Ke(t,s),c=Reflect.set(t,s,i,wt(t)?t:n);return t===Ae(n)&&(u?Dr(i,a)&&Hs(t,"set",s,i,a):Hs(t,"add",s,i)),c}deleteProperty(t,s){const i=Ke(t,s),n=t[s],a=Reflect.deleteProperty(t,s);return a&&i&&Hs(t,"delete",s,void 0,n),a}has(t,s){const i=Reflect.has(t,s);return(!Ts(s)||!Yc.has(s))&&St(t,"has",s),i}ownKeys(t){return St(t,"iterate",pe(t)?"length":Xr),Reflect.ownKeys(t)}}class Xc extends Jc{constructor(t=!1){super(!0,t)}set(t,s){return{}.NODE_ENV!=="production"&&qs(`Set operation on key "${String(s)}" failed: target is readonly.`,t),!0}deleteProperty(t,s){return{}.NODE_ENV!=="production"&&qs(`Delete operation on key "${String(s)}" failed: target is readonly.`,t),!0}}const f_=new Qc,h_=new Xc,p_=new Qc(!0),m_=new Xc(!0),fl=e=>e,di=e=>Reflect.getPrototypeOf(e);function g_(e,t,s){return function(...i){const n=this.__v_raw,a=Ae(n),u=Yr(a),c=e==="entries"||e===Symbol.iterator&&u,h=e==="keys"&&u,_=n[e](...i),p=s?fl:t?_i:Ft;return!t&&St(a,"iterate",h?ul:Xr),{next(){const{value:g,done:w}=_.next();return w?{value:g,done:w}:{value:c?[p(g[0]),p(g[1])]:p(g),done:w}},[Symbol.iterator](){return this}}}}function fi(e){return function(...t){if({}.NODE_ENV!=="production"){const s=t[0]?`on key "${t[0]}" `:"";qs(`${Jr(e)} operation ${s}failed: target is readonly.`,Ae(this))}return e==="delete"?!1:e==="clear"?void 0:this}}function __(e,t){const s={get(n){const a=this.__v_raw,u=Ae(a),c=Ae(n);e||(Dr(n,c)&&St(u,"get",n),St(u,"get",c));const{has:h}=di(u),_=t?fl:e?_i:Ft;if(h.call(u,n))return _(a.get(n));if(h.call(u,c))return _(a.get(c));a!==u&&a.get(n)},get size(){const n=this.__v_raw;return!e&&St(Ae(n),"iterate",Xr),Reflect.get(n,"size",n)},has(n){const a=this.__v_raw,u=Ae(a),c=Ae(n);return e||(Dr(n,c)&&St(u,"has",n),St(u,"has",c)),n===c?a.has(n):a.has(n)||a.has(c)},forEach(n,a){const u=this,c=u.__v_raw,h=Ae(c),_=t?fl:e?_i:Ft;return!e&&St(h,"iterate",Xr),c.forEach((p,g)=>n.call(a,_(p),_(g),u))}};return ht(s,e?{add:fi("add"),set:fi("set"),delete:fi("delete"),clear:fi("clear")}:{add(n){!t&&!jt(n)&&!js(n)&&(n=Ae(n));const a=Ae(this);return di(a).has.call(a,n)||(a.add(n),Hs(a,"add",n,n)),this},set(n,a){!t&&!jt(a)&&!js(a)&&(a=Ae(a));const u=Ae(this),{has:c,get:h}=di(u);let _=c.call(u,n);_?{}.NODE_ENV!=="production"&&ed(u,c,n):(n=Ae(n),_=c.call(u,n));const p=h.call(u,n);return u.set(n,a),_?Dr(a,p)&&Hs(u,"set",n,a,p):Hs(u,"add",n,a),this},delete(n){const a=Ae(this),{has:u,get:c}=di(a);let h=u.call(a,n);h?{}.NODE_ENV!=="production"&&ed(a,u,n):(n=Ae(n),h=u.call(a,n));const _=c?c.call(a,n):void 0,p=a.delete(n);return h&&Hs(a,"delete",n,void 0,_),p},clear(){const n=Ae(this),a=n.size!==0,u={}.NODE_ENV!=="production"?Yr(n)?new Map(n):new Set(n):void 0,c=n.clear();return a&&Hs(n,"clear",void 0,void 0,u),c}}),["keys","values","entries",Symbol.iterator].forEach(n=>{s[n]=g_(n,e,t)}),s}function hi(e,t){const s=__(e,t);return(i,n,a)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?i:Reflect.get(Ke(s,n)&&n in i?s:i,n,a)}const v_={get:hi(!1,!1)},y_={get:hi(!1,!0)},b_={get:hi(!0,!1)},C_={get:hi(!0,!0)};function ed(e,t,s){const i=Ae(s);if(i!==s&&t.call(e,i)){const n=Qa(e);qs(`Reactive ${n} contains both the raw and reactive versions of the same object${n==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}const td=new WeakMap,sd=new WeakMap,rd=new WeakMap,nd=new WeakMap;function w_(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function E_(e){return e.__v_skip||!Object.isExtensible(e)?0:w_(Qa(e))}function pi(e){return js(e)?e:mi(e,!1,f_,v_,td)}function od(e){return mi(e,!1,p_,y_,sd)}function id(e){return mi(e,!0,h_,b_,rd)}function Ws(e){return mi(e,!0,m_,C_,nd)}function mi(e,t,s,i,n){if(!Ye(e))return{}.NODE_ENV!=="production"&&qs(`value cannot be made ${t?"readonly":"reactive"}: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const a=E_(e);if(a===0)return e;const u=n.get(e);if(u)return u;const c=new Proxy(e,a===2?i:s);return n.set(e,c),c}function en(e){return js(e)?en(e.__v_raw):!!(e&&e.__v_isReactive)}function js(e){return!!(e&&e.__v_isReadonly)}function jt(e){return!!(e&&e.__v_isShallow)}function gi(e){return e?!!e.__v_raw:!1}function Ae(e){const t=e&&e.__v_raw;return t?Ae(t):e}function hl(e){return!Ke(e,"__v_skip")&&Object.isExtensible(e)&&lo(e,"__v_skip",!0),e}const Ft=e=>Ye(e)?pi(e):e,_i=e=>Ye(e)?id(e):e;function wt(e){return e?e.__v_isRef===!0:!1}function ad(e){return ld(e,!1)}function O_(e){return ld(e,!0)}function ld(e,t){return wt(e)?e:new x_(e,t)}class x_{constructor(t,s){this.dep=new al,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:Ae(t),this._value=s?t:Ft(t),this.__v_isShallow=s}get value(){return{}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track(),this._value}set value(t){const s=this._rawValue,i=this.__v_isShallow||jt(t)||js(t);t=i?t:Ae(t),Dr(t,s)&&(this._rawValue=t,this._value=i?t:Ft(t),{}.NODE_ENV!=="production"?this.dep.trigger({target:this,type:"set",key:"value",newValue:t,oldValue:s}):this.dep.trigger())}}function Tr(e){return wt(e)?e.value:e}const S_={get:(e,t,s)=>t==="__v_raw"?e:Tr(Reflect.get(e,t,s)),set:(e,t,s,i)=>{const n=e[t];return wt(n)&&!wt(s)?(n.value=s,!0):Reflect.set(e,t,s,i)}};function ud(e){return en(e)?e:new Proxy(e,S_)}class D_{constructor(t,s,i){this.fn=t,this.setter=s,this._value=void 0,this.dep=new al(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=po-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=i}notify(){if(this.flags|=16,!(this.flags&8)&&tt!==this)return qc(this,!0),!0}get value(){const t={}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track();return jc(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter?this.setter(t):{}.NODE_ENV!=="production"&&qs("Write operation failed: computed value is readonly")}}function T_(e,t,s=!1){let i,n;xe(e)?i=e:(i=e.get,n=e.set);const a=new D_(i,n,s);return{}.NODE_ENV!=="production"&&t&&!s&&(a.onTrack=t.onTrack,a.onTrigger=t.onTrigger),a}const vi={},yi=new WeakMap;let tn;function N_(e,t=!1,s=tn){if(s){let i=yi.get(s);i||yi.set(s,i=[]),i.push(e)}else({}).NODE_ENV!=="production"&&!t&&qs("onWatcherCleanup() was called when there was no active watcher to associate with.")}function I_(e,t,s=et){const{immediate:i,deep:n,once:a,scheduler:u,augmentJob:c,call:h}=s,_=J=>{(s.onWarn||qs)("Invalid watch source: ",J,"A watch source can only be a getter/effect function, a ref, a reactive object, or an array of these types.")},p=J=>n?J:jt(J)||n===!1||n===0?or(J,1):or(J);let g,w,C,M,F=!1,se=!1;if(wt(e)?(w=()=>e.value,F=jt(e)):en(e)?(w=()=>p(e),F=!0):pe(e)?(se=!0,F=e.some(J=>en(J)||jt(J)),w=()=>e.map(J=>{if(wt(J))return J.value;if(en(J))return p(J);if(xe(J))return h?h(J,2):J();({}).NODE_ENV!=="production"&&_(J)})):xe(e)?t?w=h?()=>h(e,2):e:w=()=>{if(C){Is();try{C()}finally{As()}}const J=tn;tn=g;try{return h?h(e,3,[M]):e(M)}finally{tn=J}}:(w=xt,{}.NODE_ENV!=="production"&&_(e)),t&&n){const J=w,he=n===!0?1/0:n;w=()=>or(J(),he)}const Q=o_(),ne=()=>{g.stop(),Q&&Q.active&&Ya(Q.effects,g)};if(a&&t){const J=t;t=(...he)=>{J(...he),ne()}}let Y=se?new Array(e.length).fill(vi):vi;const be=J=>{if(!(!(g.flags&1)||!g.dirty&&!J))if(t){const he=g.run();if(n||F||(se?he.some((ve,Ie)=>Dr(ve,Y[Ie])):Dr(he,Y))){C&&C();const ve=tn;tn=g;try{const Ie=[he,Y===vi?void 0:se&&Y[0]===vi?[]:Y,M];Y=he,h?h(t,3,Ie):t(...Ie)}finally{tn=ve}}}else g.run()};return c&&c(be),g=new Bc(w),g.scheduler=u?()=>u(be,!1):be,M=J=>N_(J,!1,g),C=g.onStop=()=>{const J=yi.get(g);if(J){if(h)h(J,4);else for(const he of J)he();yi.delete(g)}},{}.NODE_ENV!=="production"&&(g.onTrack=s.onTrack,g.onTrigger=s.onTrigger),t?i?be(!0):Y=g.run():u?u(be.bind(null,!0),!0):g.run(),ne.pause=g.pause.bind(g),ne.resume=g.resume.bind(g),ne.stop=ne,ne}function or(e,t=1/0,s){if(t<=0||!Ye(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,wt(e))or(e.value,t,s);else if(pe(e))for(let i=0;i<e.length;i++)or(e[i],t,s);else if(Tn(e)||Yr(e))e.forEach(i=>{or(i,t,s)});else if(kc(e)){for(const i in e)or(e[i],t,s);for(const i of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,i)&&or(e[i],t,s)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const sn=[];function bi(e){sn.push(e)}function Ci(){sn.pop()}let pl=!1;function K(e,...t){if(pl)return;pl=!0,Is();const s=sn.length?sn[sn.length-1].component:null,i=s&&s.appContext.config.warnHandler,n=A_();if(i)An(i,s,11,[e+t.map(a=>{var u,c;return(c=(u=a.toString)==null?void 0:u.call(a))!=null?c:JSON.stringify(a)}).join(""),s&&s.proxy,n.map(({vnode:a})=>`at <${Bi(s,a.type)}>`).join(`
`),n]);else{const a=[`[Vue warn]: ${e}`,...t];n.length&&a.push(`
`,...M_(n)),console.warn(...a)}As(),pl=!1}function A_(){let e=sn[sn.length-1];if(!e)return[];const t=[];for(;e;){const s=t[0];s&&s.vnode===e?s.recurseCount++:t.push({vnode:e,recurseCount:0});const i=e.component&&e.component.parent;e=i&&i.vnode}return t}function M_(e){const t=[];return e.forEach((s,i)=>{t.push(...i===0?[]:[`
`],...P_(s))}),t}function P_({vnode:e,recurseCount:t}){const s=t>0?`... (${t} recursive calls)`:"",i=e.component?e.component.parent==null:!1,n=` at <${Bi(e.component,e.type,i)}`,a=">"+s;return e.props?[n,...k_(e.props),a]:[n+a]}function k_(e){const t=[],s=Object.keys(e);return s.slice(0,3).forEach(i=>{t.push(...cd(i,e[i]))}),s.length>3&&t.push(" ..."),t}function cd(e,t,s){return ct(t)?(t=JSON.stringify(t),s?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?s?t:[`${e}=${t}`]:wt(t)?(t=cd(e,Ae(t.value),!0),s?t:[`${e}=Ref<`,t,">"]):xe(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=Ae(t),s?t:[`${e}=`,t])}function V_(e,t){({}).NODE_ENV!=="production"&&e!==void 0&&(typeof e!="number"?K(`${t} is not a valid number - got ${JSON.stringify(e)}.`):isNaN(e)&&K(`${t} is NaN - the duration expression might be incorrect.`))}const ml={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush",15:"component update",16:"app unmount cleanup function"};function An(e,t,s,i){try{return i?e(...i):e()}catch(n){_o(n,t,s)}}function Ms(e,t,s,i){if(xe(e)){const n=An(e,t,s,i);return n&&Ja(n)&&n.catch(a=>{_o(a,t,s)}),n}if(pe(e)){const n=[];for(let a=0;a<e.length;a++)n.push(Ms(e[a],t,s,i));return n}else({}).NODE_ENV!=="production"&&K(`Invalid value type passed to callWithAsyncErrorHandling(): ${typeof e}`)}function _o(e,t,s,i=!0){const n=t?t.vnode:null,{errorHandler:a,throwUnhandledErrorInProduction:u}=t&&t.appContext.config||et;if(t){let c=t.parent;const h=t.proxy,_={}.NODE_ENV!=="production"?ml[s]:`https://vuejs.org/error-reference/#runtime-${s}`;for(;c;){const p=c.ec;if(p){for(let g=0;g<p.length;g++)if(p[g](e,h,_)===!1)return}c=c.parent}if(a){Is(),An(a,null,10,[e,h,_]),As();return}}R_(e,s,n,i,u)}function R_(e,t,s,i=!0,n=!1){if({}.NODE_ENV!=="production"){const a=ml[t];if(s&&bi(s),K(`Unhandled error${a?` during execution of ${a}`:""}`),s&&Ci(),i)throw e;console.error(e)}else{if(n)throw e;console.error(e)}}const zt=[];let zs=-1;const Mn=[];let Nr=null,Pn=0;const dd=Promise.resolve();let wi=null;const F_=100;function gl(e){const t=wi||dd;return e?t.then(this?e.bind(this):e):t}function L_(e){let t=zs+1,s=zt.length;for(;t<s;){const i=t+s>>>1,n=zt[i],a=vo(n);a<e||a===e&&n.flags&2?t=i+1:s=i}return t}function Ei(e){if(!(e.flags&1)){const t=vo(e),s=zt[zt.length-1];!s||!(e.flags&2)&&t>=vo(s)?zt.push(e):zt.splice(L_(t),0,e),e.flags|=1,fd()}}function fd(){wi||(wi=dd.then(gd))}function hd(e){pe(e)?Mn.push(...e):Nr&&e.id===-1?Nr.splice(Pn+1,0,e):e.flags&1||(Mn.push(e),e.flags|=1),fd()}function pd(e,t,s=zs+1){for({}.NODE_ENV!=="production"&&(t=t||new Map);s<zt.length;s++){const i=zt[s];if(i&&i.flags&2){if(e&&i.id!==e.uid||{}.NODE_ENV!=="production"&&_l(t,i))continue;zt.splice(s,1),s--,i.flags&4&&(i.flags&=-2),i(),i.flags&4||(i.flags&=-2)}}}function md(e){if(Mn.length){const t=[...new Set(Mn)].sort((s,i)=>vo(s)-vo(i));if(Mn.length=0,Nr){Nr.push(...t);return}for(Nr=t,{}.NODE_ENV!=="production"&&(e=e||new Map),Pn=0;Pn<Nr.length;Pn++){const s=Nr[Pn];({}).NODE_ENV!=="production"&&_l(e,s)||(s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2)}Nr=null,Pn=0}}const vo=e=>e.id==null?e.flags&2?-1:1/0:e.id;function gd(e){({}).NODE_ENV!=="production"&&(e=e||new Map);const t={}.NODE_ENV!=="production"?s=>_l(e,s):xt;try{for(zs=0;zs<zt.length;zs++){const s=zt[zs];if(s&&!(s.flags&8)){if({}.NODE_ENV!=="production"&&t(s))continue;s.flags&4&&(s.flags&=-2),An(s,s.i,s.i?15:14),s.flags&4||(s.flags&=-2)}}}finally{for(;zs<zt.length;zs++){const s=zt[zs];s&&(s.flags&=-2)}zs=-1,zt.length=0,md(e),wi=null,(zt.length||Mn.length)&&gd(e)}}function _l(e,t){const s=e.get(t)||0;if(s>F_){const i=t.i,n=i&&Wl(i.type);return _o(`Maximum recursive updates exceeded${n?` in component <${n}>`:""}. This means you have a reactive effect that is mutating its own dependencies and thus recursively triggering itself. Possible sources include component template, render function, updated hook or watcher source function.`,null,10),!0}return e.set(t,s+1),!1}let Ps=!1;const Oi=new Map;({}).NODE_ENV!=="production"&&(uo().__VUE_HMR_RUNTIME__={createRecord:vl(_d),rerender:vl($_),reload:vl(q_)});const rn=new Map;function U_(e){const t=e.type.__hmrId;let s=rn.get(t);s||(_d(t,e.type),s=rn.get(t)),s.instances.add(e)}function B_(e){rn.get(e.type.__hmrId).instances.delete(e)}function _d(e,t){return rn.has(e)?!1:(rn.set(e,{initialDef:xi(t),instances:new Set}),!0)}function xi(e){return Nf(e)?e.__vccOpts:e}function $_(e,t){const s=rn.get(e);s&&(s.initialDef.render=t,[...s.instances].forEach(i=>{t&&(i.render=t,xi(i.type).render=t),i.renderCache=[],Ps=!0,i.update(),Ps=!1}))}function q_(e,t){const s=rn.get(e);if(!s)return;t=xi(t),vd(s.initialDef,t);const i=[...s.instances];for(let n=0;n<i.length;n++){const a=i[n],u=xi(a.type);let c=Oi.get(u);c||(u!==s.initialDef&&vd(u,t),Oi.set(u,c=new Set)),c.add(a),a.appContext.propsCache.delete(a.type),a.appContext.emitsCache.delete(a.type),a.appContext.optionsCache.delete(a.type),a.ceReload?(c.add(a),a.ceReload(t.styles),c.delete(a)):a.parent?Ei(()=>{Ps=!0,a.parent.update(),Ps=!1,c.delete(a)}):a.appContext.reload?a.appContext.reload():typeof window<"u"?window.location.reload():console.warn("[HMR] Root or manually mounted instance modified. Full reload required."),a.root.ce&&a!==a.root&&a.root.ce._removeChildStyle(u)}hd(()=>{Oi.clear()})}function vd(e,t){ht(e,t);for(const s in e)s!=="__file"&&!(s in t)&&delete e[s]}function vl(e){return(t,s)=>{try{return e(t,s)}catch(i){console.error(i),console.warn("[HMR] Something went wrong during Vue component hot-reload. Full reload required.")}}}let Gs,yo=[],yl=!1;function bo(e,...t){Gs?Gs.emit(e,...t):yl||yo.push({event:e,args:t})}function yd(e,t){var s,i;Gs=e,Gs?(Gs.enabled=!0,yo.forEach(({event:n,args:a})=>Gs.emit(n,...a)),yo=[]):typeof window<"u"&&window.HTMLElement&&!((i=(s=window.navigator)==null?void 0:s.userAgent)!=null&&i.includes("jsdom"))?((t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(a=>{yd(a,t)}),setTimeout(()=>{Gs||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,yl=!0,yo=[])},3e3)):(yl=!0,yo=[])}function H_(e,t){bo("app:init",e,t,{Fragment:Ne,Text:Do,Comment:yt,Static:To})}function W_(e){bo("app:unmount",e)}const j_=bl("component:added"),bd=bl("component:updated"),z_=bl("component:removed"),G_=e=>{Gs&&typeof Gs.cleanupBuffer=="function"&&!Gs.cleanupBuffer(e)&&z_(e)};/*! #__NO_SIDE_EFFECTS__ */function bl(e){return t=>{bo(e,t.appContext.app,t.uid,t.parent?t.parent.uid:void 0,t)}}const K_=Cd("perf:start"),Z_=Cd("perf:end");function Cd(e){return(t,s,i)=>{bo(e,t.appContext.app,t.uid,t,s,i)}}function Y_(e,t,s){bo("component:emit",e.appContext.app,e,t,s)}let vt=null,wd=null;function Si(e){const t=vt;return vt=e,wd=e&&e.type.__scopeId||null,t}function Te(e,t=vt,s){if(!t||e._n)return e;const i=(...n)=>{i._d&&yf(-1);const a=Si(t);let u;try{u=e(...n)}finally{Si(a),i._d&&yf(1)}return{}.NODE_ENV!=="production"&&bd(t),u};return i._n=!0,i._c=!0,i._d=!0,i}function Ed(e){qg(e)&&K("Do not use built-in directive ids as custom directive id: "+e)}function lt(e,t){if(vt===null)return{}.NODE_ENV!=="production"&&K("withDirectives can only be used inside render functions."),e;const s=Ui(vt),i=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[a,u,c,h=et]=t[n];a&&(xe(a)&&(a={mounted:a,updated:a}),a.deep&&or(u),i.push({dir:a,instance:s,value:u,oldValue:void 0,arg:c,modifiers:h}))}return e}function nn(e,t,s,i){const n=e.dirs,a=t&&t.dirs;for(let u=0;u<n.length;u++){const c=n[u];a&&(c.oldValue=a[u].value);let h=c.dir[i];h&&(Is(),Ms(h,s,8,[e.el,c,e,t]),As())}}const Od=Symbol("_vte"),xd=e=>e.__isTeleport,on=e=>e&&(e.disabled||e.disabled===""),Sd=e=>e&&(e.defer||e.defer===""),Dd=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Td=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,Cl=(e,t)=>{const s=e&&e.to;if(ct(s))if(t){const i=t(s);return{}.NODE_ENV!=="production"&&!i&&!on(e)&&K(`Failed to locate Teleport target with selector "${s}". Note the target element must exist before the component is mounted - i.e. the target cannot be rendered by the component itself, and ideally should be outside of the entire Vue component tree.`),i}else return{}.NODE_ENV!=="production"&&K("Current renderer does not support string target for Teleports. (missing querySelector renderer option)"),null;else return{}.NODE_ENV!=="production"&&!s&&!on(e)&&K(`Invalid Teleport target: ${s}`),s},Nd={name:"Teleport",__isTeleport:!0,process(e,t,s,i,n,a,u,c,h,_){const{mc:p,pc:g,pbc:w,o:{insert:C,querySelector:M,createText:F,createComment:se}}=_,Q=on(t.props);let{shapeFlag:ne,children:Y,dynamicChildren:be}=t;if({}.NODE_ENV!=="production"&&Ps&&(h=!1,be=null),e==null){const J=t.el={}.NODE_ENV!=="production"?se("teleport start"):F(""),he=t.anchor={}.NODE_ENV!=="production"?se("teleport end"):F("");C(J,s,i),C(he,s,i);const ve=(ie,I)=>{ne&16&&(n&&n.isCE&&(n.ce._teleportTarget=ie),p(Y,ie,I,n,a,u,c,h))},Ie=()=>{const ie=t.target=Cl(t.props,M),I=Id(ie,t,F,C);ie?(u!=="svg"&&Dd(ie)?u="svg":u!=="mathml"&&Td(ie)&&(u="mathml"),Q||(ve(ie,I),Ti(t,!1))):{}.NODE_ENV!=="production"&&!Q&&K("Invalid Teleport target on mount:",ie,`(${typeof ie})`)};Q&&(ve(s,he),Ti(t,!0)),Sd(t.props)?(t.el.__isMounted=!1,Kt(()=>{Ie(),delete t.el.__isMounted},a)):Ie()}else{if(Sd(t.props)&&e.el.__isMounted===!1){Kt(()=>{Nd.process(e,t,s,i,n,a,u,c,h,_)},a);return}t.el=e.el,t.targetStart=e.targetStart;const J=t.anchor=e.anchor,he=t.target=e.target,ve=t.targetAnchor=e.targetAnchor,Ie=on(e.props),ie=Ie?s:he,I=Ie?J:ve;if(u==="svg"||Dd(he)?u="svg":(u==="mathml"||Td(he))&&(u="mathml"),be?(w(e.dynamicChildren,be,ie,n,a,u,c),So(e,t,{}.NODE_ENV==="production")):h||g(e,t,ie,I,n,a,u,c,!1),Q)Ie?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Di(t,s,J,_,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const Ce=t.target=Cl(t.props,M);Ce?Di(t,Ce,null,_,0):{}.NODE_ENV!=="production"&&K("Invalid Teleport target on update:",he,`(${typeof he})`)}else Ie&&Di(t,he,ve,_,1);Ti(t,Q)}},remove(e,t,s,{um:i,o:{remove:n}},a){const{shapeFlag:u,children:c,anchor:h,targetStart:_,targetAnchor:p,target:g,props:w}=e;if(g&&(n(_),n(p)),a&&n(h),u&16){const C=a||!on(w);for(let M=0;M<c.length;M++){const F=c[M];i(F,t,s,C,!!F.dynamicChildren)}}},move:Di,hydrate:J_};function Di(e,t,s,{o:{insert:i},m:n},a=2){a===0&&i(e.targetAnchor,t,s);const{el:u,anchor:c,shapeFlag:h,children:_,props:p}=e,g=a===2;if(g&&i(u,t,s),(!g||on(p))&&h&16)for(let w=0;w<_.length;w++)n(_[w],t,s,2);g&&i(c,t,s)}function J_(e,t,s,i,n,a,{o:{nextSibling:u,parentNode:c,querySelector:h,insert:_,createText:p}},g){const w=t.target=Cl(t.props,h);if(w){const C=on(t.props),M=w._lpa||w.firstChild;if(t.shapeFlag&16)if(C)t.anchor=g(u(e),t,c(e),s,i,n,a),t.targetStart=M,t.targetAnchor=M&&u(M);else{t.anchor=u(e);let F=M;for(;F;){if(F&&F.nodeType===8){if(F.data==="teleport start anchor")t.targetStart=F;else if(F.data==="teleport anchor"){t.targetAnchor=F,w._lpa=t.targetAnchor&&u(t.targetAnchor);break}}F=u(F)}t.targetAnchor||Id(w,t,p,_),g(M&&u(M),t,w,s,i,n,a)}Ti(t,C)}return t.anchor&&u(t.anchor)}const Q_=Nd;function Ti(e,t){const s=e.ctx;if(s&&s.ut){let i,n;for(t?(i=e.el,n=e.anchor):(i=e.targetStart,n=e.targetAnchor);i&&i!==n;)i.nodeType===1&&i.setAttribute("data-v-owner",s.uid),i=i.nextSibling;s.ut()}}function Id(e,t,s,i){const n=t.targetStart=s(""),a=t.targetAnchor=s("");return n[Od]=a,e&&(i(n,e),i(a,e)),a}const Ir=Symbol("_leaveCb"),Ni=Symbol("_enterCb");function X_(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Bd(()=>{e.isMounted=!0}),$d(()=>{e.isUnmounting=!0}),e}const gs=[Function,Array],Ad={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:gs,onEnter:gs,onAfterEnter:gs,onEnterCancelled:gs,onBeforeLeave:gs,onLeave:gs,onAfterLeave:gs,onLeaveCancelled:gs,onBeforeAppear:gs,onAppear:gs,onAfterAppear:gs,onAppearCancelled:gs},Md=e=>{const t=e.subTree;return t.component?Md(t.component):t},e1={name:"BaseTransition",props:Ad,setup(e,{slots:t}){const s=Fi(),i=X_();return()=>{const n=t.default&&Rd(t.default(),!0);if(!n||!n.length)return;const a=Pd(n),u=Ae(e),{mode:c}=u;if({}.NODE_ENV!=="production"&&c&&c!=="in-out"&&c!=="out-in"&&c!=="default"&&K(`invalid <transition> mode: ${c}`),i.isLeaving)return El(a);const h=Vd(a);if(!h)return El(a);let _=wl(h,u,i,s,g=>_=g);h.type!==yt&&Co(h,_);let p=s.subTree&&Vd(s.subTree);if(p&&p.type!==yt&&!cn(h,p)&&Md(s).type!==yt){let g=wl(p,u,i,s);if(Co(p,g),c==="out-in"&&h.type!==yt)return i.isLeaving=!0,g.afterLeave=()=>{i.isLeaving=!1,s.job.flags&8||s.update(),delete g.afterLeave,p=void 0},El(a);c==="in-out"&&h.type!==yt?g.delayLeave=(w,C,M)=>{const F=kd(i,p);F[String(p.key)]=p,w[Ir]=()=>{C(),w[Ir]=void 0,delete _.delayedLeave,p=void 0},_.delayedLeave=()=>{M(),delete _.delayedLeave,p=void 0}}:p=void 0}else p&&(p=void 0);return a}}};function Pd(e){let t=e[0];if(e.length>1){let s=!1;for(const i of e)if(i.type!==yt){if({}.NODE_ENV!=="production"&&s){K("<transition> can only be used on a single element or component. Use <transition-group> for lists.");break}if(t=i,s=!0,{}.NODE_ENV==="production")break}}return t}const t1=e1;function kd(e,t){const{leavingVNodes:s}=e;let i=s.get(t.type);return i||(i=Object.create(null),s.set(t.type,i)),i}function wl(e,t,s,i,n){const{appear:a,mode:u,persisted:c=!1,onBeforeEnter:h,onEnter:_,onAfterEnter:p,onEnterCancelled:g,onBeforeLeave:w,onLeave:C,onAfterLeave:M,onLeaveCancelled:F,onBeforeAppear:se,onAppear:Q,onAfterAppear:ne,onAppearCancelled:Y}=t,be=String(e.key),J=kd(s,e),he=(ie,I)=>{ie&&Ms(ie,i,9,I)},ve=(ie,I)=>{const Ce=I[1];he(ie,I),pe(ie)?ie.every(le=>le.length<=1)&&Ce():ie.length<=1&&Ce()},Ie={mode:u,persisted:c,beforeEnter(ie){let I=h;if(!s.isMounted)if(a)I=se||h;else return;ie[Ir]&&ie[Ir](!0);const Ce=J[be];Ce&&cn(e,Ce)&&Ce.el[Ir]&&Ce.el[Ir](),he(I,[ie])},enter(ie){let I=_,Ce=p,le=g;if(!s.isMounted)if(a)I=Q||_,Ce=ne||p,le=Y||g;else return;let ze=!1;const mt=ie[Ni]=ue=>{ze||(ze=!0,ue?he(le,[ie]):he(Ce,[ie]),Ie.delayedLeave&&Ie.delayedLeave(),ie[Ni]=void 0)};I?ve(I,[ie,mt]):mt()},leave(ie,I){const Ce=String(e.key);if(ie[Ni]&&ie[Ni](!0),s.isUnmounting)return I();he(w,[ie]);let le=!1;const ze=ie[Ir]=mt=>{le||(le=!0,I(),mt?he(F,[ie]):he(M,[ie]),ie[Ir]=void 0,J[Ce]===e&&delete J[Ce])};J[Ce]=e,C?ve(C,[ie,ze]):ze()},clone(ie){const I=wl(ie,t,s,i,n);return n&&n(I),I}};return Ie}function El(e){if(Eo(e))return e=Zs(e),e.children=null,e}function Vd(e){if(!Eo(e))return xd(e.type)&&e.children?Pd(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:s}=e;if(s){if(t&16)return s[0];if(t&32&&xe(s.default))return s.default()}}function Co(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Co(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Rd(e,t=!1,s){let i=[],n=0;for(let a=0;a<e.length;a++){let u=e[a];const c=s==null?u.key:String(s)+String(u.key!=null?u.key:a);u.type===Ne?(u.patchFlag&128&&n++,i=i.concat(Rd(u.children,t,c))):(t||u.type!==yt)&&i.push(c!=null?Zs(u,{key:c}):u)}if(n>1)for(let a=0;a<i.length;a++)i[a].patchFlag=-2;return i}/*! #__NO_SIDE_EFFECTS__ */function Fd(e,t){return xe(e)?(()=>ht({name:e.name},t,{setup:e}))():e}function Ld(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}const s1=new WeakSet;function wo(e,t,s,i,n=!1){if(pe(e)){e.forEach((M,F)=>wo(M,t&&(pe(t)?t[F]:t),s,i,n));return}if(kn(i)&&!n){i.shapeFlag&512&&i.type.__asyncResolved&&i.component.subTree.component&&wo(e,t,s,i.component.subTree);return}const a=i.shapeFlag&4?Ui(i.component):i.el,u=n?null:a,{i:c,r:h}=e;if({}.NODE_ENV!=="production"&&!c){K("Missing ref owner context. ref cannot be used on hoisted vnodes. A vnode with ref must be created inside the render function.");return}const _=t&&t.r,p=c.refs===et?c.refs={}:c.refs,g=c.setupState,w=Ae(g),C=g===et?()=>!1:M=>({}).NODE_ENV!=="production"&&(Ke(w,M)&&!wt(w[M])&&K(`Template ref "${M}" used on a non-ref value. It will not work in the production build.`),s1.has(w[M]))?!1:Ke(w,M);if(_!=null&&_!==h&&(ct(_)?(p[_]=null,C(_)&&(g[_]=null)):wt(_)&&(_.value=null)),xe(h))An(h,c,12,[u,p]);else{const M=ct(h),F=wt(h);if(M||F){const se=()=>{if(e.f){const Q=M?C(h)?g[h]:p[h]:h.value;n?pe(Q)&&Ya(Q,a):pe(Q)?Q.includes(a)||Q.push(a):M?(p[h]=[a],C(h)&&(g[h]=p[h])):(h.value=[a],e.k&&(p[e.k]=h.value))}else M?(p[h]=u,C(h)&&(g[h]=u)):F?(h.value=u,e.k&&(p[e.k]=u)):{}.NODE_ENV!=="production"&&K("Invalid template ref type:",h,`(${typeof h})`)};u?(se.id=-1,Kt(se,s)):se()}else({}).NODE_ENV!=="production"&&K("Invalid template ref type:",h,`(${typeof h})`)}}uo().requestIdleCallback,uo().cancelIdleCallback;const kn=e=>!!e.type.__asyncLoader,Eo=e=>e.type.__isKeepAlive;function r1(e,t){Ud(e,"a",t)}function n1(e,t){Ud(e,"da",t)}function Ud(e,t,s=Dt){const i=e.__wdc||(e.__wdc=()=>{let n=s;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(Ii(t,i,s),s){let n=s.parent;for(;n&&n.parent;)Eo(n.parent.vnode)&&o1(i,t,s,n),n=n.parent}}function o1(e,t,s,i){const n=Ii(t,e,i,!0);qd(()=>{Ya(i[t],n)},s)}function Ii(e,t,s=Dt,i=!1){if(s){const n=s[e]||(s[e]=[]),a=t.__weh||(t.__weh=(...u)=>{Is();const c=Ao(s),h=Ms(t,s,e,u);return c(),As(),h});return i?n.unshift(a):n.push(a),a}else if({}.NODE_ENV!=="production"){const n=Qr(ml[e].replace(/ hook$/,""));K(`${n} is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.`)}}const ir=e=>(t,s=Dt)=>{(!Mo||e==="sp")&&Ii(e,(...i)=>t(...i),s)},i1=ir("bm"),Bd=ir("m"),a1=ir("bu"),l1=ir("u"),$d=ir("bum"),qd=ir("um"),u1=ir("sp"),c1=ir("rtg"),d1=ir("rtc");function f1(e,t=Dt){Ii("ec",e,t)}const Ol="components",h1="directives";function z(e,t){return Hd(Ol,e,!0,t)||e}const p1=Symbol.for("v-ndc");function m1(e){return Hd(h1,e)}function Hd(e,t,s=!0,i=!1){const n=vt||Dt;if(n){const a=n.type;if(e===Ol){const c=Wl(a,!1);if(c&&(c===t||c===Wt(t)||c===Jr(Wt(t))))return a}const u=Wd(n[e]||a[e],t)||Wd(n.appContext[e],t);if(!u&&i)return a;if({}.NODE_ENV!=="production"&&s&&!u){const c=e===Ol?`
If this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement.`:"";K(`Failed to resolve ${e.slice(0,-1)}: ${t}${c}`)}return u}else({}).NODE_ENV!=="production"&&K(`resolve${Jr(e.slice(0,-1))} can only be used in render() or setup().`)}function Wd(e,t){return e&&(e[t]||e[Wt(t)]||e[Jr(Wt(t))])}function it(e,t,s,i){let n;const a=s&&s[i],u=pe(e);if(u||ct(e)){const c=u&&en(e);let h=!1,_=!1;c&&(h=!jt(e),_=js(e),e=ci(e)),n=new Array(e.length);for(let p=0,g=e.length;p<g;p++)n[p]=t(h?_?_i(Ft(e[p])):Ft(e[p]):e[p],p,void 0,a&&a[p])}else if(typeof e=="number"){({}).NODE_ENV!=="production"&&!Number.isInteger(e)&&K(`The v-for range expect an integer value but got ${e}.`),n=new Array(e);for(let c=0;c<e;c++)n[c]=t(c+1,c,void 0,a&&a[c])}else if(Ye(e))if(e[Symbol.iterator])n=Array.from(e,(c,h)=>t(c,h,void 0,a&&a[h]));else{const c=Object.keys(e);n=new Array(c.length);for(let h=0,_=c.length;h<_;h++){const p=c[h];n[h]=t(e[p],p,h,a&&a[h])}}else n=[];return s&&(s[i]=n),n}function Mt(e,t,s={},i,n){if(vt.ce||vt.parent&&kn(vt.parent)&&vt.parent.ce)return t!=="default"&&(s.name=t),x(),dt(Ne,null,[A("slot",s,i&&i())],64);let a=e[t];({}).NODE_ENV!=="production"&&a&&a.length>1&&(K("SSR-optimized slot function detected in a non-SSR-optimized render function. You need to mark this component with $dynamic-slots in the parent template."),a=()=>[]),a&&a._c&&(a._d=!1),x();const u=a&&jd(a(s)),c=s.key||u&&u.key,h=dt(Ne,{key:(c&&!Ts(c)?c:`_${t}`)+(!u&&i?"_fb":"")},u||(i?i():[]),u&&e._===1?64:-2);return!n&&h.scopeId&&(h.slotScopeIds=[h.scopeId+"-s"]),a&&a._c&&(a._d=!0),h}function jd(e){return e.some(t=>un(t)?!(t.type===yt||t.type===Ne&&!jd(t.children)):!0)?e:null}const xl=e=>e?xf(e)?Ui(e):xl(e.parent):null,an=ht(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>({}).NODE_ENV!=="production"?Ws(e.props):e.props,$attrs:e=>({}).NODE_ENV!=="production"?Ws(e.attrs):e.attrs,$slots:e=>({}).NODE_ENV!=="production"?Ws(e.slots):e.slots,$refs:e=>({}).NODE_ENV!=="production"?Ws(e.refs):e.refs,$parent:e=>xl(e.parent),$root:e=>xl(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Nl(e),$forceUpdate:e=>e.f||(e.f=()=>{Ei(e.update)}),$nextTick:e=>e.n||(e.n=gl.bind(e.proxy)),$watch:e=>K1.bind(e)}),Sl=e=>e==="_"||e==="$",Dl=(e,t)=>e!==et&&!e.__isScriptSetup&&Ke(e,t),zd={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:i,data:n,props:a,accessCache:u,type:c,appContext:h}=e;if({}.NODE_ENV!=="production"&&t==="__isVue")return!0;let _;if(t[0]!=="$"){const C=u[t];if(C!==void 0)switch(C){case 1:return i[t];case 2:return n[t];case 4:return s[t];case 3:return a[t]}else{if(Dl(i,t))return u[t]=1,i[t];if(n!==et&&Ke(n,t))return u[t]=2,n[t];if((_=e.propsOptions[0])&&Ke(_,t))return u[t]=3,a[t];if(s!==et&&Ke(s,t))return u[t]=4,s[t];Tl&&(u[t]=0)}}const p=an[t];let g,w;if(p)return t==="$attrs"?(St(e.attrs,"get",""),{}.NODE_ENV!=="production"&&Vi()):{}.NODE_ENV!=="production"&&t==="$slots"&&St(e,"get",t),p(e);if((g=c.__cssModules)&&(g=g[t]))return g;if(s!==et&&Ke(s,t))return u[t]=4,s[t];if(w=h.config.globalProperties,Ke(w,t))return w[t];({}).NODE_ENV!=="production"&&vt&&(!ct(t)||t.indexOf("__v")!==0)&&(n!==et&&Sl(t[0])&&Ke(n,t)?K(`Property ${JSON.stringify(t)} must be accessed via $data because it starts with a reserved character ("$" or "_") and is not proxied on the render context.`):e===vt&&K(`Property ${JSON.stringify(t)} was accessed during render but is not defined on instance.`))},set({_:e},t,s){const{data:i,setupState:n,ctx:a}=e;return Dl(n,t)?(n[t]=s,!0):{}.NODE_ENV!=="production"&&n.__isScriptSetup&&Ke(n,t)?(K(`Cannot mutate <script setup> binding "${t}" from Options API.`),!1):i!==et&&Ke(i,t)?(i[t]=s,!0):Ke(e.props,t)?({}.NODE_ENV!=="production"&&K(`Attempting to mutate prop "${t}". Props are readonly.`),!1):t[0]==="$"&&t.slice(1)in e?({}.NODE_ENV!=="production"&&K(`Attempting to mutate public property "${t}". Properties starting with $ are reserved and readonly.`),!1):({}.NODE_ENV!=="production"&&t in e.appContext.config.globalProperties?Object.defineProperty(a,t,{enumerable:!0,configurable:!0,value:s}):a[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:i,appContext:n,propsOptions:a}},u){let c;return!!s[u]||e!==et&&Ke(e,u)||Dl(t,u)||(c=a[0])&&Ke(c,u)||Ke(i,u)||Ke(an,u)||Ke(n.config.globalProperties,u)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:Ke(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};({}).NODE_ENV!=="production"&&(zd.ownKeys=e=>(K("Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead."),Reflect.ownKeys(e)));function g1(e){const t={};return Object.defineProperty(t,"_",{configurable:!0,enumerable:!1,get:()=>e}),Object.keys(an).forEach(s=>{Object.defineProperty(t,s,{configurable:!0,enumerable:!1,get:()=>an[s](e),set:xt})}),t}function _1(e){const{ctx:t,propsOptions:[s]}=e;s&&Object.keys(s).forEach(i=>{Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>e.props[i],set:xt})})}function v1(e){const{ctx:t,setupState:s}=e;Object.keys(Ae(s)).forEach(i=>{if(!s.__isScriptSetup){if(Sl(i[0])){K(`setup() return property ${JSON.stringify(i)} should not start with "$" or "_" which are reserved prefixes for Vue internals.`);return}Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>s[i],set:xt})}})}function Gd(e){return pe(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}function y1(){const e=Object.create(null);return(t,s)=>{e[s]?K(`${t} property "${s}" is already defined in ${e[s]}.`):e[s]=t}}let Tl=!0;function b1(e){const t=Nl(e),s=e.proxy,i=e.ctx;Tl=!1,t.beforeCreate&&Kd(t.beforeCreate,e,"bc");const{data:n,computed:a,methods:u,watch:c,provide:h,inject:_,created:p,beforeMount:g,mounted:w,beforeUpdate:C,updated:M,activated:F,deactivated:se,beforeDestroy:Q,beforeUnmount:ne,destroyed:Y,unmounted:be,render:J,renderTracked:he,renderTriggered:ve,errorCaptured:Ie,serverPrefetch:ie,expose:I,inheritAttrs:Ce,components:le,directives:ze,filters:mt}=t,ue={}.NODE_ENV!=="production"?y1():null;if({}.NODE_ENV!=="production"){const[ce]=e.propsOptions;if(ce)for(const we in ce)ue("Props",we)}if(_&&C1(_,i,ue),u)for(const ce in u){const we=u[ce];xe(we)?({}.NODE_ENV!=="production"?Object.defineProperty(i,ce,{value:we.bind(s),configurable:!0,enumerable:!0,writable:!0}):i[ce]=we.bind(s),{}.NODE_ENV!=="production"&&ue("Methods",ce)):{}.NODE_ENV!=="production"&&K(`Method "${ce}" has type "${typeof we}" in the component definition. Did you reference the function correctly?`)}if(n){({}).NODE_ENV!=="production"&&!xe(n)&&K("The data option must be a function. Plain object usage is no longer supported.");const ce=n.call(s,s);if({}.NODE_ENV!=="production"&&Ja(ce)&&K("data() returned a Promise - note data() cannot be async; If you intend to perform data fetching before component renders, use async setup() + <Suspense>."),!Ye(ce))({}).NODE_ENV!=="production"&&K("data() should return an object.");else if(e.data=pi(ce),{}.NODE_ENV!=="production")for(const we in ce)ue("Data",we),Sl(we[0])||Object.defineProperty(i,we,{configurable:!0,enumerable:!0,get:()=>ce[we],set:xt})}if(Tl=!0,a)for(const ce in a){const we=a[ce],Et=xe(we)?we.bind(s,s):xe(we.get)?we.get.bind(s,s):xt;({}).NODE_ENV!=="production"&&Et===xt&&K(`Computed property "${ce}" has no getter.`);const _s=!xe(we)&&xe(we.set)?we.set.bind(s):{}.NODE_ENV!=="production"?()=>{K(`Write operation failed: computed property "${ce}" is readonly.`)}:xt,Tt=Vs({get:Et,set:_s});Object.defineProperty(i,ce,{enumerable:!0,configurable:!0,get:()=>Tt.value,set:vs=>Tt.value=vs}),{}.NODE_ENV!=="production"&&ue("Computed",ce)}if(c)for(const ce in c)Zd(c[ce],i,s,ce);if(h){const ce=xe(h)?h.call(s):h;Reflect.ownKeys(ce).forEach(we=>{Mi(we,ce[we])})}p&&Kd(p,e,"c");function ot(ce,we){pe(we)?we.forEach(Et=>ce(Et.bind(s))):we&&ce(we.bind(s))}if(ot(i1,g),ot(Bd,w),ot(a1,C),ot(l1,M),ot(r1,F),ot(n1,se),ot(f1,Ie),ot(d1,he),ot(c1,ve),ot($d,ne),ot(qd,be),ot(u1,ie),pe(I))if(I.length){const ce=e.exposed||(e.exposed={});I.forEach(we=>{Object.defineProperty(ce,we,{get:()=>s[we],set:Et=>s[we]=Et})})}else e.exposed||(e.exposed={});J&&e.render===xt&&(e.render=J),Ce!=null&&(e.inheritAttrs=Ce),le&&(e.components=le),ze&&(e.directives=ze),ie&&Ld(e)}function C1(e,t,s=xt){pe(e)&&(e=Il(e));for(const i in e){const n=e[i];let a;Ye(n)?"default"in n?a=Ks(n.from||i,n.default,!0):a=Ks(n.from||i):a=Ks(n),wt(a)?Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>a.value,set:u=>a.value=u}):t[i]=a,{}.NODE_ENV!=="production"&&s("Inject",i)}}function Kd(e,t,s){Ms(pe(e)?e.map(i=>i.bind(t.proxy)):e.bind(t.proxy),t,s)}function Zd(e,t,s,i){let n=i.includes(".")?hf(s,i):()=>s[i];if(ct(e)){const a=t[e];xe(a)?Rn(n,a):{}.NODE_ENV!=="production"&&K(`Invalid watch handler specified by key "${e}"`,a)}else if(xe(e))Rn(n,e.bind(s));else if(Ye(e))if(pe(e))e.forEach(a=>Zd(a,t,s,i));else{const a=xe(e.handler)?e.handler.bind(s):t[e.handler];xe(a)?Rn(n,a,e):{}.NODE_ENV!=="production"&&K(`Invalid watch handler specified by key "${e.handler}"`,a)}else({}).NODE_ENV!=="production"&&K(`Invalid watch option: "${i}"`,e)}function Nl(e){const t=e.type,{mixins:s,extends:i}=t,{mixins:n,optionsCache:a,config:{optionMergeStrategies:u}}=e.appContext,c=a.get(t);let h;return c?h=c:!n.length&&!s&&!i?h=t:(h={},n.length&&n.forEach(_=>Ai(h,_,u,!0)),Ai(h,t,u)),Ye(t)&&a.set(t,h),h}function Ai(e,t,s,i=!1){const{mixins:n,extends:a}=t;a&&Ai(e,a,s,!0),n&&n.forEach(u=>Ai(e,u,s,!0));for(const u in t)if(i&&u==="expose")({}).NODE_ENV!=="production"&&K('"expose" option is ignored when declared in mixins or extends. It should only be declared in the base component itself.');else{const c=w1[u]||s&&s[u];e[u]=c?c(e[u],t[u]):t[u]}return e}const w1={data:Yd,props:Jd,emits:Jd,methods:Oo,computed:Oo,beforeCreate:Gt,created:Gt,beforeMount:Gt,mounted:Gt,beforeUpdate:Gt,updated:Gt,beforeDestroy:Gt,beforeUnmount:Gt,destroyed:Gt,unmounted:Gt,activated:Gt,deactivated:Gt,errorCaptured:Gt,serverPrefetch:Gt,components:Oo,directives:Oo,watch:O1,provide:Yd,inject:E1};function Yd(e,t){return t?e?function(){return ht(xe(e)?e.call(this,this):e,xe(t)?t.call(this,this):t)}:t:e}function E1(e,t){return Oo(Il(e),Il(t))}function Il(e){if(pe(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Gt(e,t){return e?[...new Set([].concat(e,t))]:t}function Oo(e,t){return e?ht(Object.create(null),e,t):t}function Jd(e,t){return e?pe(e)&&pe(t)?[...new Set([...e,...t])]:ht(Object.create(null),Gd(e),Gd(t??{})):t}function O1(e,t){if(!e)return t;if(!t)return e;const s=ht(Object.create(null),e);for(const i in t)s[i]=Gt(e[i],t[i]);return s}function Qd(){return{app:null,config:{isNativeTag:Bg,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let x1=0;function S1(e,t){return function(i,n=null){xe(i)||(i=ht({},i)),n!=null&&!Ye(n)&&({}.NODE_ENV!=="production"&&K("root props passed to app.mount() must be an object."),n=null);const a=Qd(),u=new WeakSet,c=[];let h=!1;const _=a.app={_uid:x1++,_component:i,_props:n,_container:null,_context:a,_instance:null,version:If,get config(){return a.config},set config(p){({}).NODE_ENV!=="production"&&K("app.config cannot be replaced. Modify individual options instead.")},use(p,...g){return u.has(p)?{}.NODE_ENV!=="production"&&K("Plugin has already been applied to target app."):p&&xe(p.install)?(u.add(p),p.install(_,...g)):xe(p)?(u.add(p),p(_,...g)):{}.NODE_ENV!=="production"&&K('A plugin must either be a function or an object with an "install" function.'),_},mixin(p){return a.mixins.includes(p)?{}.NODE_ENV!=="production"&&K("Mixin has already been applied to target app"+(p.name?`: ${p.name}`:"")):a.mixins.push(p),_},component(p,g){return{}.NODE_ENV!=="production"&&ql(p,a.config),g?({}.NODE_ENV!=="production"&&a.components[p]&&K(`Component "${p}" has already been registered in target app.`),a.components[p]=g,_):a.components[p]},directive(p,g){return{}.NODE_ENV!=="production"&&Ed(p),g?({}.NODE_ENV!=="production"&&a.directives[p]&&K(`Directive "${p}" has already been registered in target app.`),a.directives[p]=g,_):a.directives[p]},mount(p,g,w){if(h)({}).NODE_ENV!=="production"&&K("App has already been mounted.\nIf you want to remount the same app, move your app creation logic into a factory function and create fresh app instances for each mount - e.g. `const createMyApp = () => createApp(App)`");else{({}).NODE_ENV!=="production"&&p.__vue_app__&&K("There is already an app instance mounted on the host container.\n If you want to mount another app on the same host container, you need to unmount the previous app by calling `app.unmount()` first.");const C=_._ceVNode||A(i,n);return C.appContext=a,w===!0?w="svg":w===!1&&(w=void 0),{}.NODE_ENV!=="production"&&(a.reload=()=>{const M=Zs(C);M.el=null,e(M,p,w)}),g&&t?t(C,p):e(C,p,w),h=!0,_._container=p,p.__vue_app__=_,{}.NODE_ENV!=="production"&&(_._instance=C.component,H_(_,If)),Ui(C.component)}},onUnmount(p){({}).NODE_ENV!=="production"&&typeof p!="function"&&K(`Expected function as first argument to app.onUnmount(), but got ${typeof p}`),c.push(p)},unmount(){h?(Ms(c,_._instance,16),e(null,_._container),{}.NODE_ENV!=="production"&&(_._instance=null,W_(_)),delete _._container.__vue_app__):{}.NODE_ENV!=="production"&&K("Cannot unmount an app that is not mounted.")},provide(p,g){return{}.NODE_ENV!=="production"&&p in a.provides&&(Ke(a.provides,p)?K(`App already provides property with key "${String(p)}". It will be overwritten with the new value.`):K(`App already provides property with key "${String(p)}" inherited from its parent element. It will be overwritten with the new value.`)),a.provides[p]=g,_},runWithContext(p){const g=Vn;Vn=_;try{return p()}finally{Vn=g}}};return _}}let Vn=null;function Mi(e,t){if(!Dt)({}).NODE_ENV!=="production"&&K("provide() can only be used inside setup().");else{let s=Dt.provides;const i=Dt.parent&&Dt.parent.provides;i===s&&(s=Dt.provides=Object.create(i)),s[e]=t}}function Ks(e,t,s=!1){const i=Dt||vt;if(i||Vn){let n=Vn?Vn._context.provides:i?i.parent==null||i.ce?i.vnode.appContext&&i.vnode.appContext.provides:i.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return s&&xe(t)?t.call(i&&i.proxy):t;({}).NODE_ENV!=="production"&&K(`injection "${String(e)}" not found.`)}else({}).NODE_ENV!=="production"&&K("inject() can only be used inside setup() or functional components.")}const Xd={},ef=()=>Object.create(Xd),tf=e=>Object.getPrototypeOf(e)===Xd;function D1(e,t,s,i=!1){const n={},a=ef();e.propsDefaults=Object.create(null),sf(e,t,n,a);for(const u in e.propsOptions[0])u in n||(n[u]=void 0);({}).NODE_ENV!=="production"&&of(t||{},n,e),s?e.props=i?n:od(n):e.type.props?e.props=n:e.props=a,e.attrs=a}function T1(e){for(;e;){if(e.type.__hmrId)return!0;e=e.parent}}function N1(e,t,s,i){const{props:n,attrs:a,vnode:{patchFlag:u}}=e,c=Ae(n),[h]=e.propsOptions;let _=!1;if(!({}.NODE_ENV!=="production"&&T1(e))&&(i||u>0)&&!(u&16)){if(u&8){const p=e.vnode.dynamicProps;for(let g=0;g<p.length;g++){let w=p[g];if(ki(e.emitsOptions,w))continue;const C=t[w];if(h)if(Ke(a,w))C!==a[w]&&(a[w]=C,_=!0);else{const M=Wt(w);n[M]=Al(h,c,M,C,e,!1)}else C!==a[w]&&(a[w]=C,_=!0)}}}else{sf(e,t,n,a)&&(_=!0);let p;for(const g in c)(!t||!Ke(t,g)&&((p=Sr(g))===g||!Ke(t,p)))&&(h?s&&(s[g]!==void 0||s[p]!==void 0)&&(n[g]=Al(h,c,g,void 0,e,!0)):delete n[g]);if(a!==c)for(const g in a)(!t||!Ke(t,g))&&(delete a[g],_=!0)}_&&Hs(e.attrs,"set",""),{}.NODE_ENV!=="production"&&of(t||{},n,e)}function sf(e,t,s,i){const[n,a]=e.propsOptions;let u=!1,c;if(t)for(let h in t){if(ao(h))continue;const _=t[h];let p;n&&Ke(n,p=Wt(h))?!a||!a.includes(p)?s[p]=_:(c||(c={}))[p]=_:ki(e.emitsOptions,h)||(!(h in i)||_!==i[h])&&(i[h]=_,u=!0)}if(a){const h=Ae(s),_=c||et;for(let p=0;p<a.length;p++){const g=a[p];s[g]=Al(n,h,g,_[g],e,!Ke(_,g))}}return u}function Al(e,t,s,i,n,a){const u=e[s];if(u!=null){const c=Ke(u,"default");if(c&&i===void 0){const h=u.default;if(u.type!==Function&&!u.skipFactory&&xe(h)){const{propsDefaults:_}=n;if(s in _)i=_[s];else{const p=Ao(n);i=_[s]=h.call(null,t),p()}}else i=h;n.ce&&n.ce._setProp(s,i)}u[0]&&(a&&!c?i=!1:u[1]&&(i===""||i===Sr(s))&&(i=!0))}return i}const I1=new WeakMap;function rf(e,t,s=!1){const i=s?I1:t.propsCache,n=i.get(e);if(n)return n;const a=e.props,u={},c=[];let h=!1;if(!xe(e)){const p=g=>{h=!0;const[w,C]=rf(g,t,!0);ht(u,w),C&&c.push(...C)};!s&&t.mixins.length&&t.mixins.forEach(p),e.extends&&p(e.extends),e.mixins&&e.mixins.forEach(p)}if(!a&&!h)return Ye(e)&&i.set(e,Dn),Dn;if(pe(a))for(let p=0;p<a.length;p++){({}).NODE_ENV!=="production"&&!ct(a[p])&&K("props must be strings when using array syntax.",a[p]);const g=Wt(a[p]);nf(g)&&(u[g]=et)}else if(a){({}).NODE_ENV!=="production"&&!Ye(a)&&K("invalid props options",a);for(const p in a){const g=Wt(p);if(nf(g)){const w=a[p],C=u[g]=pe(w)||xe(w)?{type:w}:ht({},w),M=C.type;let F=!1,se=!0;if(pe(M))for(let Q=0;Q<M.length;++Q){const ne=M[Q],Y=xe(ne)&&ne.name;if(Y==="Boolean"){F=!0;break}else Y==="String"&&(se=!1)}else F=xe(M)&&M.name==="Boolean";C[0]=F,C[1]=se,(F||Ke(C,"default"))&&c.push(g)}}}const _=[u,c];return Ye(e)&&i.set(e,_),_}function nf(e){return e[0]!=="$"&&!ao(e)?!0:({}.NODE_ENV!=="production"&&K(`Invalid prop name: "${e}" is a reserved property.`),!1)}function A1(e){return e===null?"null":typeof e=="function"?e.name||"":typeof e=="object"&&e.constructor&&e.constructor.name||""}function of(e,t,s){const i=Ae(t),n=s.propsOptions[0],a=Object.keys(e).map(u=>Wt(u));for(const u in n){let c=n[u];c!=null&&M1(u,i[u],c,{}.NODE_ENV!=="production"?Ws(i):i,!a.includes(u))}}function M1(e,t,s,i,n){const{type:a,required:u,validator:c,skipCheck:h}=s;if(u&&n){K('Missing required prop: "'+e+'"');return}if(!(t==null&&!u)){if(a!=null&&a!==!0&&!h){let _=!1;const p=pe(a)?a:[a],g=[];for(let w=0;w<p.length&&!_;w++){const{valid:C,expectedType:M}=k1(t,p[w]);g.push(M||""),_=C}if(!_){K(V1(e,t,g));return}}c&&!c(t,i)&&K('Invalid prop: custom validator check failed for prop "'+e+'".')}}const P1=rr("String,Number,Boolean,Function,Symbol,BigInt");function k1(e,t){let s;const i=A1(t);if(i==="null")s=e===null;else if(P1(i)){const n=typeof e;s=n===i.toLowerCase(),!s&&n==="object"&&(s=e instanceof t)}else i==="Object"?s=Ye(e):i==="Array"?s=pe(e):s=e instanceof t;return{valid:s,expectedType:i}}function V1(e,t,s){if(s.length===0)return`Prop type [] for prop "${e}" won't match anything. Did you mean to use type Array instead?`;let i=`Invalid prop: type check failed for prop "${e}". Expected ${s.map(Jr).join(" | ")}`;const n=s[0],a=Qa(t),u=af(t,n),c=af(t,a);return s.length===1&&lf(n)&&!R1(n,a)&&(i+=` with value ${u}`),i+=`, got ${a} `,lf(a)&&(i+=`with value ${c}.`),i}function af(e,t){return t==="String"?`"${e}"`:t==="Number"?`${Number(e)}`:`${e}`}function lf(e){return["string","number","boolean"].some(s=>e.toLowerCase()===s)}function R1(...e){return e.some(t=>t.toLowerCase()==="boolean")}const Ml=e=>e[0]==="_"||e==="$stable",Pl=e=>pe(e)?e.map(ks):[ks(e)],F1=(e,t,s)=>{if(t._n)return t;const i=Te((...n)=>({}.NODE_ENV!=="production"&&Dt&&!(s===null&&vt)&&!(s&&s.root!==Dt.root)&&K(`Slot "${e}" invoked outside of the render function: this will not track dependencies used in the slot. Invoke the slot function inside the render function instead.`),Pl(t(...n))),s);return i._c=!1,i},uf=(e,t,s)=>{const i=e._ctx;for(const n in e){if(Ml(n))continue;const a=e[n];if(xe(a))t[n]=F1(n,a,i);else if(a!=null){({}).NODE_ENV!=="production"&&K(`Non-function value encountered for slot "${n}". Prefer function slots for better performance.`);const u=Pl(a);t[n]=()=>u}}},cf=(e,t)=>{({}).NODE_ENV!=="production"&&!Eo(e.vnode)&&K("Non-function value encountered for default slot. Prefer function slots for better performance.");const s=Pl(t);e.slots.default=()=>s},kl=(e,t,s)=>{for(const i in t)(s||!Ml(i))&&(e[i]=t[i])},L1=(e,t,s)=>{const i=e.slots=ef();if(e.vnode.shapeFlag&32){const n=t.__;n&&lo(i,"__",n,!0);const a=t._;a?(kl(i,t,s),s&&lo(i,"_",a,!0)):uf(t,i)}else t&&cf(e,t)},U1=(e,t,s)=>{const{vnode:i,slots:n}=e;let a=!0,u=et;if(i.shapeFlag&32){const c=t._;c?{}.NODE_ENV!=="production"&&Ps?(kl(n,t,s),Hs(e,"set","$slots")):s&&c===1?a=!1:kl(n,t,s):(a=!t.$stable,uf(t,n)),u=t}else t&&(cf(e,t),u={default:1});if(a)for(const c in n)!Ml(c)&&u[c]==null&&delete n[c]};let xo,Ar;function ar(e,t){e.appContext.config.performance&&Pi()&&Ar.mark(`vue-${t}-${e.uid}`),{}.NODE_ENV!=="production"&&K_(e,t,Pi()?Ar.now():Date.now())}function lr(e,t){if(e.appContext.config.performance&&Pi()){const s=`vue-${t}-${e.uid}`,i=s+":end";Ar.mark(i),Ar.measure(`<${Bi(e,e.type)}> ${t}`,s,i),Ar.clearMarks(s),Ar.clearMarks(i)}({}).NODE_ENV!=="production"&&Z_(e,t,Pi()?Ar.now():Date.now())}function Pi(){return xo!==void 0||(typeof window<"u"&&window.performance?(xo=!0,Ar=window.performance):xo=!1),xo}function B1(){const e=[];if({}.NODE_ENV!=="production"&&e.length){const t=e.length>1;console.warn(`Feature flag${t?"s":""} ${e.join(", ")} ${t?"are":"is"} not explicitly defined. You are running the esm-bundler build of Vue, which expects these compile-time feature flags to be globally injected via the bundler config in order to get better tree-shaking in the production bundle.

For more details, see https://link.vuejs.org/feature-flags.`)}}const Kt=tv;function $1(e){return q1(e)}function q1(e,t){B1();const s=uo();s.__VUE__=!0,{}.NODE_ENV!=="production"&&yd(s.__VUE_DEVTOOLS_GLOBAL_HOOK__,s);const{insert:i,remove:n,patchProp:a,createElement:u,createText:c,createComment:h,setText:_,setElementText:p,parentNode:g,nextSibling:w,setScopeId:C=xt,insertStaticContent:M}=e,F=(b,O,k,L=null,$=null,H=null,ee=void 0,G=null,X={}.NODE_ENV!=="production"&&Ps?!1:!!O.dynamicChildren)=>{if(b===O)return;b&&!cn(b,O)&&(L=oe(b),ts(b,$,H,!0),b=null),O.patchFlag===-2&&(X=!1,O.dynamicChildren=null);const{type:j,ref:ye,shapeFlag:te}=O;switch(j){case Do:se(b,O,k,L);break;case yt:Q(b,O,k,L);break;case To:b==null?ne(O,k,L,ee):{}.NODE_ENV!=="production"&&Y(b,O,k,ee);break;case Ne:ze(b,O,k,L,$,H,ee,G,X);break;default:te&1?he(b,O,k,L,$,H,ee,G,X):te&6?mt(b,O,k,L,$,H,ee,G,X):te&64||te&128?j.process(b,O,k,L,$,H,ee,G,X,ke):{}.NODE_ENV!=="production"&&K("Invalid VNode type:",j,`(${typeof j})`)}ye!=null&&$?wo(ye,b&&b.ref,H,O||b,!O):ye==null&&b&&b.ref!=null&&wo(b.ref,null,H,b,!0)},se=(b,O,k,L)=>{if(b==null)i(O.el=c(O.children),k,L);else{const $=O.el=b.el;O.children!==b.children&&_($,O.children)}},Q=(b,O,k,L)=>{b==null?i(O.el=h(O.children||""),k,L):O.el=b.el},ne=(b,O,k,L)=>{[b.el,b.anchor]=M(b.children,O,k,L,b.el,b.anchor)},Y=(b,O,k,L)=>{if(O.children!==b.children){const $=w(b.anchor);J(b),[O.el,O.anchor]=M(O.children,k,$,L)}else O.el=b.el,O.anchor=b.anchor},be=({el:b,anchor:O},k,L)=>{let $;for(;b&&b!==O;)$=w(b),i(b,k,L),b=$;i(O,k,L)},J=({el:b,anchor:O})=>{let k;for(;b&&b!==O;)k=w(b),n(b),b=k;n(O)},he=(b,O,k,L,$,H,ee,G,X)=>{O.type==="svg"?ee="svg":O.type==="math"&&(ee="mathml"),b==null?ve(O,k,L,$,H,ee,G,X):I(b,O,$,H,ee,G,X)},ve=(b,O,k,L,$,H,ee,G)=>{let X,j;const{props:ye,shapeFlag:te,transition:ge,dirs:Ee}=b;if(X=b.el=u(b.type,H,ye&&ye.is,ye),te&8?p(X,b.children):te&16&&ie(b.children,X,null,L,$,Vl(b,H),ee,G),Ee&&nn(b,null,L,"created"),Ie(X,b,b.scopeId,ee,L),ye){for(const Qe in ye)Qe!=="value"&&!ao(Qe)&&a(X,Qe,null,ye[Qe],H,L);"value"in ye&&a(X,"value",null,ye.value,H),(j=ye.onVnodeBeforeMount)&&Ys(j,L,b)}({}).NODE_ENV!=="production"&&(lo(X,"__vnode",b,!0),lo(X,"__vueParentComponent",L,!0)),Ee&&nn(b,null,L,"beforeMount");const Fe=H1($,ge);Fe&&ge.beforeEnter(X),i(X,O,k),((j=ye&&ye.onVnodeMounted)||Fe||Ee)&&Kt(()=>{j&&Ys(j,L,b),Fe&&ge.enter(X),Ee&&nn(b,null,L,"mounted")},$)},Ie=(b,O,k,L,$)=>{if(k&&C(b,k),L)for(let H=0;H<L.length;H++)C(b,L[H]);if($){let H=$.subTree;if({}.NODE_ENV!=="production"&&H.patchFlag>0&&H.patchFlag&2048&&(H=Ul(H.children)||H),O===H||vf(H.type)&&(H.ssContent===O||H.ssFallback===O)){const ee=$.vnode;Ie(b,ee,ee.scopeId,ee.slotScopeIds,$.parent)}}},ie=(b,O,k,L,$,H,ee,G,X=0)=>{for(let j=X;j<b.length;j++){const ye=b[j]=G?Mr(b[j]):ks(b[j]);F(null,ye,O,k,L,$,H,ee,G)}},I=(b,O,k,L,$,H,ee)=>{const G=O.el=b.el;({}).NODE_ENV!=="production"&&(G.__vnode=O);let{patchFlag:X,dynamicChildren:j,dirs:ye}=O;X|=b.patchFlag&16;const te=b.props||et,ge=O.props||et;let Ee;if(k&&ln(k,!1),(Ee=ge.onVnodeBeforeUpdate)&&Ys(Ee,k,O,b),ye&&nn(O,b,k,"beforeUpdate"),k&&ln(k,!0),{}.NODE_ENV!=="production"&&Ps&&(X=0,ee=!1,j=null),(te.innerHTML&&ge.innerHTML==null||te.textContent&&ge.textContent==null)&&p(G,""),j?(Ce(b.dynamicChildren,j,G,k,L,Vl(O,$),H),{}.NODE_ENV!=="production"&&So(b,O)):ee||Et(b,O,G,null,k,L,Vl(O,$),H,!1),X>0){if(X&16)le(G,te,ge,k,$);else if(X&2&&te.class!==ge.class&&a(G,"class",null,ge.class,$),X&4&&a(G,"style",te.style,ge.style,$),X&8){const Fe=O.dynamicProps;for(let Qe=0;Qe<Fe.length;Qe++){const Ze=Fe[Qe],Vt=te[Ze],Ot=ge[Ze];(Ot!==Vt||Ze==="value")&&a(G,Ze,Vt,Ot,$,k)}}X&1&&b.children!==O.children&&p(G,O.children)}else!ee&&j==null&&le(G,te,ge,k,$);((Ee=ge.onVnodeUpdated)||ye)&&Kt(()=>{Ee&&Ys(Ee,k,O,b),ye&&nn(O,b,k,"updated")},L)},Ce=(b,O,k,L,$,H,ee)=>{for(let G=0;G<O.length;G++){const X=b[G],j=O[G],ye=X.el&&(X.type===Ne||!cn(X,j)||X.shapeFlag&198)?g(X.el):k;F(X,j,ye,null,L,$,H,ee,!0)}},le=(b,O,k,L,$)=>{if(O!==k){if(O!==et)for(const H in O)!ao(H)&&!(H in k)&&a(b,H,O[H],null,$,L);for(const H in k){if(ao(H))continue;const ee=k[H],G=O[H];ee!==G&&H!=="value"&&a(b,H,G,ee,$,L)}"value"in k&&a(b,"value",O.value,k.value,$)}},ze=(b,O,k,L,$,H,ee,G,X)=>{const j=O.el=b?b.el:c(""),ye=O.anchor=b?b.anchor:c("");let{patchFlag:te,dynamicChildren:ge,slotScopeIds:Ee}=O;({}).NODE_ENV!=="production"&&(Ps||te&2048)&&(te=0,X=!1,ge=null),Ee&&(G=G?G.concat(Ee):Ee),b==null?(i(j,k,L),i(ye,k,L),ie(O.children||[],k,ye,$,H,ee,G,X)):te>0&&te&64&&ge&&b.dynamicChildren?(Ce(b.dynamicChildren,ge,k,$,H,ee,G),{}.NODE_ENV!=="production"?So(b,O):(O.key!=null||$&&O===$.subTree)&&So(b,O,!0)):Et(b,O,k,ye,$,H,ee,G,X)},mt=(b,O,k,L,$,H,ee,G,X)=>{O.slotScopeIds=G,b==null?O.shapeFlag&512?$.ctx.activate(O,k,L,ee,X):ue(O,k,L,$,H,ee,X):ot(b,O,X)},ue=(b,O,k,L,$,H,ee)=>{const G=b.component=uv(b,L,$);if({}.NODE_ENV!=="production"&&G.type.__hmrId&&U_(G),{}.NODE_ENV!=="production"&&(bi(b),ar(G,"mount")),Eo(b)&&(G.ctx.renderer=ke),{}.NODE_ENV!=="production"&&ar(G,"init"),dv(G,!1,ee),{}.NODE_ENV!=="production"&&lr(G,"init"),{}.NODE_ENV!=="production"&&Ps&&(b.el=null),G.asyncDep){if($&&$.registerDep(G,ce,ee),!b.el){const X=G.subTree=A(yt);Q(null,X,O,k)}}else ce(G,b,O,k,$,H,ee);({}).NODE_ENV!=="production"&&(Ci(),lr(G,"mount"))},ot=(b,O,k)=>{const L=O.component=b.component;if(X1(b,O,k))if(L.asyncDep&&!L.asyncResolved){({}).NODE_ENV!=="production"&&bi(O),we(L,O,k),{}.NODE_ENV!=="production"&&Ci();return}else L.next=O,L.update();else O.el=b.el,L.vnode=O},ce=(b,O,k,L,$,H,ee)=>{const G=()=>{if(b.isMounted){let{next:te,bu:ge,u:Ee,parent:Fe,vnode:Qe}=b;{const Lt=df(b);if(Lt){te&&(te.el=Qe.el,we(b,te,ee)),Lt.asyncDep.then(()=>{b.isUnmounted||G()});return}}let Ze=te,Vt;({}).NODE_ENV!=="production"&&bi(te||b.vnode),ln(b,!1),te?(te.el=Qe.el,we(b,te,ee)):te=Qe,ge&&Nn(ge),(Vt=te.props&&te.props.onVnodeBeforeUpdate)&&Ys(Vt,Fe,te,Qe),ln(b,!0),{}.NODE_ENV!=="production"&&ar(b,"render");const Ot=Ll(b);({}).NODE_ENV!=="production"&&lr(b,"render");const Yt=b.subTree;b.subTree=Ot,{}.NODE_ENV!=="production"&&ar(b,"patch"),F(Yt,Ot,g(Yt.el),oe(Yt),b,$,H),{}.NODE_ENV!=="production"&&lr(b,"patch"),te.el=Ot.el,Ze===null&&ev(b,Ot.el),Ee&&Kt(Ee,$),(Vt=te.props&&te.props.onVnodeUpdated)&&Kt(()=>Ys(Vt,Fe,te,Qe),$),{}.NODE_ENV!=="production"&&bd(b),{}.NODE_ENV!=="production"&&Ci()}else{let te;const{el:ge,props:Ee}=O,{bm:Fe,m:Qe,parent:Ze,root:Vt,type:Ot}=b,Yt=kn(O);if(ln(b,!1),Fe&&Nn(Fe),!Yt&&(te=Ee&&Ee.onVnodeBeforeMount)&&Ys(te,Ze,O),ln(b,!0),ge&&Ve){const Lt=()=>{({}).NODE_ENV!=="production"&&ar(b,"render"),b.subTree=Ll(b),{}.NODE_ENV!=="production"&&lr(b,"render"),{}.NODE_ENV!=="production"&&ar(b,"hydrate"),Ve(ge,b.subTree,b,$,null),{}.NODE_ENV!=="production"&&lr(b,"hydrate")};Yt&&Ot.__asyncHydrate?Ot.__asyncHydrate(ge,b,Lt):Lt()}else{Vt.ce&&Vt.ce._def.shadowRoot!==!1&&Vt.ce._injectChildStyle(Ot),{}.NODE_ENV!=="production"&&ar(b,"render");const Lt=b.subTree=Ll(b);({}).NODE_ENV!=="production"&&lr(b,"render"),{}.NODE_ENV!=="production"&&ar(b,"patch"),F(null,Lt,k,L,b,$,H),{}.NODE_ENV!=="production"&&lr(b,"patch"),O.el=Lt.el}if(Qe&&Kt(Qe,$),!Yt&&(te=Ee&&Ee.onVnodeMounted)){const Lt=O;Kt(()=>Ys(te,Ze,Lt),$)}(O.shapeFlag&256||Ze&&kn(Ze.vnode)&&Ze.vnode.shapeFlag&256)&&b.a&&Kt(b.a,$),b.isMounted=!0,{}.NODE_ENV!=="production"&&j_(b),O=k=L=null}};b.scope.on();const X=b.effect=new Bc(G);b.scope.off();const j=b.update=X.run.bind(X),ye=b.job=X.runIfDirty.bind(X);ye.i=b,ye.id=b.uid,X.scheduler=()=>Ei(ye),ln(b,!0),{}.NODE_ENV!=="production"&&(X.onTrack=b.rtc?te=>Nn(b.rtc,te):void 0,X.onTrigger=b.rtg?te=>Nn(b.rtg,te):void 0),j()},we=(b,O,k)=>{O.component=b;const L=b.vnode.props;b.vnode=O,b.next=null,N1(b,O.props,L,k),U1(b,O.children,k),Is(),pd(b),As()},Et=(b,O,k,L,$,H,ee,G,X=!1)=>{const j=b&&b.children,ye=b?b.shapeFlag:0,te=O.children,{patchFlag:ge,shapeFlag:Ee}=O;if(ge>0){if(ge&128){Tt(j,te,k,L,$,H,ee,G,X);return}else if(ge&256){_s(j,te,k,L,$,H,ee,G,X);return}}Ee&8?(ye&16&&V(j,$,H),te!==j&&p(k,te)):ye&16?Ee&16?Tt(j,te,k,L,$,H,ee,G,X):V(j,$,H,!0):(ye&8&&p(k,""),Ee&16&&ie(te,k,L,$,H,ee,G,X))},_s=(b,O,k,L,$,H,ee,G,X)=>{b=b||Dn,O=O||Dn;const j=b.length,ye=O.length,te=Math.min(j,ye);let ge;for(ge=0;ge<te;ge++){const Ee=O[ge]=X?Mr(O[ge]):ks(O[ge]);F(b[ge],Ee,k,null,$,H,ee,G,X)}j>ye?V(b,$,H,!0,!1,te):ie(O,k,L,$,H,ee,G,X,te)},Tt=(b,O,k,L,$,H,ee,G,X)=>{let j=0;const ye=O.length;let te=b.length-1,ge=ye-1;for(;j<=te&&j<=ge;){const Ee=b[j],Fe=O[j]=X?Mr(O[j]):ks(O[j]);if(cn(Ee,Fe))F(Ee,Fe,k,null,$,H,ee,G,X);else break;j++}for(;j<=te&&j<=ge;){const Ee=b[te],Fe=O[ge]=X?Mr(O[ge]):ks(O[ge]);if(cn(Ee,Fe))F(Ee,Fe,k,null,$,H,ee,G,X);else break;te--,ge--}if(j>te){if(j<=ge){const Ee=ge+1,Fe=Ee<ye?O[Ee].el:L;for(;j<=ge;)F(null,O[j]=X?Mr(O[j]):ks(O[j]),k,Fe,$,H,ee,G,X),j++}}else if(j>ge)for(;j<=te;)ts(b[j],$,H,!0),j++;else{const Ee=j,Fe=j,Qe=new Map;for(j=Fe;j<=ge;j++){const Nt=O[j]=X?Mr(O[j]):ks(O[j]);Nt.key!=null&&({}.NODE_ENV!=="production"&&Qe.has(Nt.key)&&K("Duplicate keys found during update:",JSON.stringify(Nt.key),"Make sure keys are unique."),Qe.set(Nt.key,j))}let Ze,Vt=0;const Ot=ge-Fe+1;let Yt=!1,Lt=0;const gr=new Array(Ot);for(j=0;j<Ot;j++)gr[j]=0;for(j=Ee;j<=te;j++){const Nt=b[j];if(Vt>=Ot){ts(Nt,$,H,!0);continue}let ys;if(Nt.key!=null)ys=Qe.get(Nt.key);else for(Ze=Fe;Ze<=ge;Ze++)if(gr[Ze-Fe]===0&&cn(Nt,O[Ze])){ys=Ze;break}ys===void 0?ts(Nt,$,H,!0):(gr[ys-Fe]=j+1,ys>=Lt?Lt=ys:Yt=!0,F(Nt,O[ys],k,null,$,H,ee,G,X),Vt++)}const Wn=Yt?W1(gr):Dn;for(Ze=Wn.length-1,j=Ot-1;j>=0;j--){const Nt=Fe+j,ys=O[Nt],aa=Nt+1<ye?O[Nt+1].el:L;gr[j]===0?F(null,ys,k,aa,$,H,ee,G,X):Yt&&(Ze<0||j!==Wn[Ze]?vs(ys,k,aa,2):Ze--)}}},vs=(b,O,k,L,$=null)=>{const{el:H,type:ee,transition:G,children:X,shapeFlag:j}=b;if(j&6){vs(b.component.subTree,O,k,L);return}if(j&128){b.suspense.move(O,k,L);return}if(j&64){ee.move(b,O,k,ke);return}if(ee===Ne){i(H,O,k);for(let te=0;te<X.length;te++)vs(X[te],O,k,L);i(b.anchor,O,k);return}if(ee===To){be(b,O,k);return}if(L!==2&&j&1&&G)if(L===0)G.beforeEnter(H),i(H,O,k),Kt(()=>G.enter(H),$);else{const{leave:te,delayLeave:ge,afterLeave:Ee}=G,Fe=()=>{b.ctx.isUnmounted?n(H):i(H,O,k)},Qe=()=>{te(H,()=>{Fe(),Ee&&Ee()})};ge?ge(H,Fe,Qe):Qe()}else i(H,O,k)},ts=(b,O,k,L=!1,$=!1)=>{const{type:H,props:ee,ref:G,children:X,dynamicChildren:j,shapeFlag:ye,patchFlag:te,dirs:ge,cacheIndex:Ee}=b;if(te===-2&&($=!1),G!=null&&(Is(),wo(G,null,k,b,!0),As()),Ee!=null&&(O.renderCache[Ee]=void 0),ye&256){O.ctx.deactivate(b);return}const Fe=ye&1&&ge,Qe=!kn(b);let Ze;if(Qe&&(Ze=ee&&ee.onVnodeBeforeUnmount)&&Ys(Ze,O,b),ye&6)us(b.component,k,L);else{if(ye&128){b.suspense.unmount(k,L);return}Fe&&nn(b,null,O,"beforeUnmount"),ye&64?b.type.remove(b,O,k,ke,L):j&&!j.hasOnce&&(H!==Ne||te>0&&te&64)?V(j,O,k,!1,!0):(H===Ne&&te&384||!$&&ye&16)&&V(X,O,k),L&&Xs(b)}(Qe&&(Ze=ee&&ee.onVnodeUnmounted)||Fe)&&Kt(()=>{Ze&&Ys(Ze,O,b),Fe&&nn(b,null,O,"unmounted")},k)},Xs=b=>{const{type:O,el:k,anchor:L,transition:$}=b;if(O===Ne){({}).NODE_ENV!=="production"&&b.patchFlag>0&&b.patchFlag&2048&&$&&!$.persisted?b.children.forEach(ee=>{ee.type===yt?n(ee.el):Xs(ee)}):Fs(k,L);return}if(O===To){J(b);return}const H=()=>{n(k),$&&!$.persisted&&$.afterLeave&&$.afterLeave()};if(b.shapeFlag&1&&$&&!$.persisted){const{leave:ee,delayLeave:G}=$,X=()=>ee(k,H);G?G(b.el,H,X):X()}else H()},Fs=(b,O)=>{let k;for(;b!==O;)k=w(b),n(b),b=k;n(O)},us=(b,O,k)=>{({}).NODE_ENV!=="production"&&b.type.__hmrId&&B_(b);const{bum:L,scope:$,job:H,subTree:ee,um:G,m:X,a:j,parent:ye,slots:{__:te}}=b;ff(X),ff(j),L&&Nn(L),ye&&pe(te)&&te.forEach(ge=>{ye.renderCache[ge]=void 0}),$.stop(),H&&(H.flags|=8,ts(ee,b,O,k)),G&&Kt(G,O),Kt(()=>{b.isUnmounted=!0},O),O&&O.pendingBranch&&!O.isUnmounted&&b.asyncDep&&!b.asyncResolved&&b.suspenseId===O.pendingId&&(O.deps--,O.deps===0&&O.resolve()),{}.NODE_ENV!=="production"&&G_(b)},V=(b,O,k,L=!1,$=!1,H=0)=>{for(let ee=H;ee<b.length;ee++)ts(b[ee],O,k,L,$)},oe=b=>{if(b.shapeFlag&6)return oe(b.component.subTree);if(b.shapeFlag&128)return b.suspense.next();const O=w(b.anchor||b.el),k=O&&O[Od];return k?w(k):O};let re=!1;const me=(b,O,k)=>{b==null?O._vnode&&ts(O._vnode,null,null,!0):F(O._vnode||null,b,O,null,null,null,k),O._vnode=b,re||(re=!0,pd(),md(),re=!1)},ke={p:F,um:ts,m:vs,r:Xs,mt:ue,mc:ie,pc:Et,pbc:Ce,n:oe,o:e};let rt,Ve;return t&&([rt,Ve]=t(ke)),{render:me,hydrate:rt,createApp:S1(me,rt)}}function Vl({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function ln({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function H1(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function So(e,t,s=!1){const i=e.children,n=t.children;if(pe(i)&&pe(n))for(let a=0;a<i.length;a++){const u=i[a];let c=n[a];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=n[a]=Mr(n[a]),c.el=u.el),!s&&c.patchFlag!==-2&&So(u,c)),c.type===Do&&(c.el=u.el),c.type===yt&&!c.el&&(c.el=u.el),{}.NODE_ENV!=="production"&&c.el&&(c.el.__vnode=c)}}function W1(e){const t=e.slice(),s=[0];let i,n,a,u,c;const h=e.length;for(i=0;i<h;i++){const _=e[i];if(_!==0){if(n=s[s.length-1],e[n]<_){t[i]=n,s.push(i);continue}for(a=0,u=s.length-1;a<u;)c=a+u>>1,e[s[c]]<_?a=c+1:u=c;_<e[s[a]]&&(a>0&&(t[i]=s[a-1]),s[a]=i)}}for(a=s.length,u=s[a-1];a-- >0;)s[a]=u,u=t[u];return s}function df(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:df(t)}function ff(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const j1=Symbol.for("v-scx"),z1=()=>{{const e=Ks(j1);return e||{}.NODE_ENV!=="production"&&K("Server rendering context not provided. Make sure to only call useSSRContext() conditionally in the server build."),e}};function G1(e,t){return Rl(e,null,t)}function Rn(e,t,s){return{}.NODE_ENV!=="production"&&!xe(t)&&K("`watch(fn, options?)` signature has been moved to a separate API. Use `watchEffect(fn, options?)` instead. `watch` now only supports `watch(source, cb, options?) signature."),Rl(e,t,s)}function Rl(e,t,s=et){const{immediate:i,deep:n,flush:a,once:u}=s;({}).NODE_ENV!=="production"&&!t&&(i!==void 0&&K('watch() "immediate" option is only respected when using the watch(source, callback, options?) signature.'),n!==void 0&&K('watch() "deep" option is only respected when using the watch(source, callback, options?) signature.'),u!==void 0&&K('watch() "once" option is only respected when using the watch(source, callback, options?) signature.'));const c=ht({},s);({}).NODE_ENV!=="production"&&(c.onWarn=K);const h=t&&i||!t&&a!=="post";let _;if(Mo){if(a==="sync"){const C=z1();_=C.__watcherHandles||(C.__watcherHandles=[])}else if(!h){const C=()=>{};return C.stop=xt,C.resume=xt,C.pause=xt,C}}const p=Dt;c.call=(C,M,F)=>Ms(C,p,M,F);let g=!1;a==="post"?c.scheduler=C=>{Kt(C,p&&p.suspense)}:a!=="sync"&&(g=!0,c.scheduler=(C,M)=>{M?C():Ei(C)}),c.augmentJob=C=>{t&&(C.flags|=4),g&&(C.flags|=2,p&&(C.id=p.uid,C.i=p))};const w=I_(e,t,c);return Mo&&(_?_.push(w):h&&w()),w}function K1(e,t,s){const i=this.proxy,n=ct(e)?e.includes(".")?hf(i,e):()=>i[e]:e.bind(i,i);let a;xe(t)?a=t:(a=t.handler,s=t);const u=Ao(this),c=Rl(n,a.bind(i),s);return u(),c}function hf(e,t){const s=t.split(".");return()=>{let i=e;for(let n=0;n<s.length&&i;n++)i=i[s[n]];return i}}const Z1=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Wt(t)}Modifiers`]||e[`${Sr(t)}Modifiers`];function Y1(e,t,...s){if(e.isUnmounted)return;const i=e.vnode.props||et;if({}.NODE_ENV!=="production"){const{emitsOptions:p,propsOptions:[g]}=e;if(p)if(!(t in p))(!g||!(Qr(Wt(t))in g))&&K(`Component emitted event "${t}" but it is neither declared in the emits option nor as an "${Qr(Wt(t))}" prop.`);else{const w=p[t];xe(w)&&(w(...s)||K(`Invalid event arguments: event validation failed for event "${t}".`))}}let n=s;const a=t.startsWith("update:"),u=a&&Z1(i,t.slice(7));if(u&&(u.trim&&(n=s.map(p=>ct(p)?p.trim():p)),u.number&&(n=s.map(ui))),{}.NODE_ENV!=="production"&&Y_(e,t,n),{}.NODE_ENV!=="production"){const p=t.toLowerCase();p!==t&&i[Qr(p)]&&K(`Event "${p}" is emitted in component ${Bi(e,e.type)} but the handler is registered for "${t}". Note that HTML attributes are case-insensitive and you cannot use v-on to listen to camelCase events when using in-DOM templates. You should probably use "${Sr(t)}" instead of "${t}".`)}let c,h=i[c=Qr(t)]||i[c=Qr(Wt(t))];!h&&a&&(h=i[c=Qr(Sr(t))]),h&&Ms(h,e,6,n);const _=i[c+"Once"];if(_){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,Ms(_,e,6,n)}}function pf(e,t,s=!1){const i=t.emitsCache,n=i.get(e);if(n!==void 0)return n;const a=e.emits;let u={},c=!1;if(!xe(e)){const h=_=>{const p=pf(_,t,!0);p&&(c=!0,ht(u,p))};!s&&t.mixins.length&&t.mixins.forEach(h),e.extends&&h(e.extends),e.mixins&&e.mixins.forEach(h)}return!a&&!c?(Ye(e)&&i.set(e,null),null):(pe(a)?a.forEach(h=>u[h]=null):ht(u,a),Ye(e)&&i.set(e,u),u)}function ki(e,t){return!e||!oo(t)?!1:(t=t.slice(2).replace(/Once$/,""),Ke(e,t[0].toLowerCase()+t.slice(1))||Ke(e,Sr(t))||Ke(e,t))}let Fl=!1;function Vi(){Fl=!0}function Ll(e){const{type:t,vnode:s,proxy:i,withProxy:n,propsOptions:[a],slots:u,attrs:c,emit:h,render:_,renderCache:p,props:g,data:w,setupState:C,ctx:M,inheritAttrs:F}=e,se=Si(e);let Q,ne;({}).NODE_ENV!=="production"&&(Fl=!1);try{if(s.shapeFlag&4){const J=n||i,he={}.NODE_ENV!=="production"&&C.__isScriptSetup?new Proxy(J,{get(ve,Ie,ie){return K(`Property '${String(Ie)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(ve,Ie,ie)}}):J;Q=ks(_.call(he,J,p,{}.NODE_ENV!=="production"?Ws(g):g,C,w,M)),ne=c}else{const J=t;({}).NODE_ENV!=="production"&&c===g&&Vi(),Q=ks(J.length>1?J({}.NODE_ENV!=="production"?Ws(g):g,{}.NODE_ENV!=="production"?{get attrs(){return Vi(),Ws(c)},slots:u,emit:h}:{attrs:c,slots:u,emit:h}):J({}.NODE_ENV!=="production"?Ws(g):g,null)),ne=t.props?c:J1(c)}}catch(J){No.length=0,_o(J,e,1),Q=A(yt)}let Y=Q,be;if({}.NODE_ENV!=="production"&&Q.patchFlag>0&&Q.patchFlag&2048&&([Y,be]=mf(Q)),ne&&F!==!1){const J=Object.keys(ne),{shapeFlag:he}=Y;if(J.length){if(he&7)a&&J.some(ai)&&(ne=Q1(ne,a)),Y=Zs(Y,ne,!1,!0);else if({}.NODE_ENV!=="production"&&!Fl&&Y.type!==yt){const ve=Object.keys(c),Ie=[],ie=[];for(let I=0,Ce=ve.length;I<Ce;I++){const le=ve[I];oo(le)?ai(le)||Ie.push(le[2].toLowerCase()+le.slice(3)):ie.push(le)}ie.length&&K(`Extraneous non-props attributes (${ie.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text or teleport root nodes.`),Ie.length&&K(`Extraneous non-emits event listeners (${Ie.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text root nodes. If the listener is intended to be a component custom event listener only, declare it using the "emits" option.`)}}}return s.dirs&&({}.NODE_ENV!=="production"&&!gf(Y)&&K("Runtime directive used on component with non-element root node. The directives will not function as intended."),Y=Zs(Y,null,!1,!0),Y.dirs=Y.dirs?Y.dirs.concat(s.dirs):s.dirs),s.transition&&({}.NODE_ENV!=="production"&&!gf(Y)&&K("Component inside <Transition> renders non-element root node that cannot be animated."),Co(Y,s.transition)),{}.NODE_ENV!=="production"&&be?be(Y):Q=Y,Si(se),Q}const mf=e=>{const t=e.children,s=e.dynamicChildren,i=Ul(t,!1);if(i){if({}.NODE_ENV!=="production"&&i.patchFlag>0&&i.patchFlag&2048)return mf(i)}else return[e,void 0];const n=t.indexOf(i),a=s?s.indexOf(i):-1,u=c=>{t[n]=c,s&&(a>-1?s[a]=c:c.patchFlag>0&&(e.dynamicChildren=[...s,c]))};return[ks(i),u]};function Ul(e,t=!0){let s;for(let i=0;i<e.length;i++){const n=e[i];if(un(n)){if(n.type!==yt||n.children==="v-if"){if(s)return;if(s=n,{}.NODE_ENV!=="production"&&t&&s.patchFlag>0&&s.patchFlag&2048)return Ul(s.children)}}else return}return s}const J1=e=>{let t;for(const s in e)(s==="class"||s==="style"||oo(s))&&((t||(t={}))[s]=e[s]);return t},Q1=(e,t)=>{const s={};for(const i in e)(!ai(i)||!(i.slice(9)in t))&&(s[i]=e[i]);return s},gf=e=>e.shapeFlag&7||e.type===yt;function X1(e,t,s){const{props:i,children:n,component:a}=e,{props:u,children:c,patchFlag:h}=t,_=a.emitsOptions;if({}.NODE_ENV!=="production"&&(n||c)&&Ps||t.dirs||t.transition)return!0;if(s&&h>=0){if(h&1024)return!0;if(h&16)return i?_f(i,u,_):!!u;if(h&8){const p=t.dynamicProps;for(let g=0;g<p.length;g++){const w=p[g];if(u[w]!==i[w]&&!ki(_,w))return!0}}}else return(n||c)&&(!c||!c.$stable)?!0:i===u?!1:i?u?_f(i,u,_):!0:!!u;return!1}function _f(e,t,s){const i=Object.keys(t);if(i.length!==Object.keys(e).length)return!0;for(let n=0;n<i.length;n++){const a=i[n];if(t[a]!==e[a]&&!ki(s,a))return!0}return!1}function ev({vnode:e,parent:t},s){for(;t;){const i=t.subTree;if(i.suspense&&i.suspense.activeBranch===e&&(i.el=e.el),i===e)(e=t.vnode).el=s,t=t.parent;else break}}const vf=e=>e.__isSuspense;function tv(e,t){t&&t.pendingBranch?pe(e)?t.effects.push(...e):t.effects.push(e):hd(e)}const Ne=Symbol.for("v-fgt"),Do=Symbol.for("v-txt"),yt=Symbol.for("v-cmt"),To=Symbol.for("v-stc"),No=[];let as=null;function x(e=!1){No.push(as=e?null:[])}function sv(){No.pop(),as=No[No.length-1]||null}let Io=1;function yf(e,t=!1){Io+=e,e<0&&as&&t&&(as.hasOnce=!0)}function bf(e){return e.dynamicChildren=Io>0?as||Dn:null,sv(),Io>0&&as&&as.push(e),e}function D(e,t,s,i,n,a){return bf(f(e,t,s,i,n,a,!0))}function dt(e,t,s,i,n){return bf(A(e,t,s,i,n,!0))}function un(e){return e?e.__v_isVNode===!0:!1}function cn(e,t){if({}.NODE_ENV!=="production"&&t.shapeFlag&6&&e.component){const s=Oi.get(t.type);if(s&&s.has(e.component))return e.shapeFlag&=-257,t.shapeFlag&=-513,!1}return e.type===t.type&&e.key===t.key}const rv=(...e)=>wf(...e),Cf=({key:e})=>e??null,Ri=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?ct(e)||wt(e)||xe(e)?{i:vt,r:e,k:t,f:!!s}:e:null);function f(e,t=null,s=null,i=0,n=null,a=e===Ne?0:1,u=!1,c=!1){const h={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Cf(t),ref:t&&Ri(t),scopeId:wd,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:a,patchFlag:i,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:vt};return c?(Bl(h,s),a&128&&e.normalize(h)):s&&(h.shapeFlag|=ct(s)?8:16),{}.NODE_ENV!=="production"&&h.key!==h.key&&K("VNode created with invalid key (NaN). VNode type:",h.type),Io>0&&!u&&as&&(h.patchFlag>0||a&6)&&h.patchFlag!==32&&as.push(h),h}const A={}.NODE_ENV!=="production"?rv:wf;function wf(e,t=null,s=null,i=0,n=null,a=!1){if((!e||e===p1)&&({}.NODE_ENV!=="production"&&!e&&K(`Invalid vnode type when creating vnode: ${e}.`),e=yt),un(e)){const c=Zs(e,t,!0);return s&&Bl(c,s),Io>0&&!a&&as&&(c.shapeFlag&6?as[as.indexOf(e)]=c:as.push(c)),c.patchFlag=-2,c}if(Nf(e)&&(e=e.__vccOpts),t){t=nv(t);let{class:c,style:h}=t;c&&!ct(c)&&(t.class=de(c)),Ye(h)&&(gi(h)&&!pe(h)&&(h=ht({},h)),t.style=is(h))}const u=ct(e)?1:vf(e)?128:xd(e)?64:Ye(e)?4:xe(e)?2:0;return{}.NODE_ENV!=="production"&&u&4&&gi(e)&&(e=Ae(e),K("Vue received a Component that was made a reactive object. This can lead to unnecessary performance overhead and should be avoided by marking the component with `markRaw` or using `shallowRef` instead of `ref`.",`
Component that was made reactive: `,e)),f(e,t,s,i,n,u,a,!0)}function nv(e){return e?gi(e)||tf(e)?ht({},e):e:null}function Zs(e,t,s=!1,i=!1){const{props:n,ref:a,patchFlag:u,children:c,transition:h}=e,_=t?iv(n||{},t):n,p={__v_isVNode:!0,__v_skip:!0,type:e.type,props:_,key:_&&Cf(_),ref:t&&t.ref?s&&a?pe(a)?a.concat(Ri(t)):[a,Ri(t)]:Ri(t):a,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:{}.NODE_ENV!=="production"&&u===-1&&pe(c)?c.map(Ef):c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ne?u===-1?16:u|16:u,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:h,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Zs(e.ssContent),ssFallback:e.ssFallback&&Zs(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return h&&i&&Co(p,h.clone(p)),p}function Ef(e){const t=Zs(e);return pe(e.children)&&(t.children=e.children.map(Ef)),t}function We(e=" ",t=0){return A(Do,null,e,t)}function ov(e,t){const s=A(To,null,e);return s.staticCount=t,s}function Z(e="",t=!1){return t?(x(),dt(yt,null,e)):A(yt,null,e)}function ks(e){return e==null||typeof e=="boolean"?A(yt):pe(e)?A(Ne,null,e.slice()):un(e)?Mr(e):A(Do,null,String(e))}function Mr(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Zs(e)}function Bl(e,t){let s=0;const{shapeFlag:i}=e;if(t==null)t=null;else if(pe(t))s=16;else if(typeof t=="object")if(i&65){const n=t.default;n&&(n._c&&(n._d=!1),Bl(e,n()),n._c&&(n._d=!0));return}else{s=32;const n=t._;!n&&!tf(t)?t._ctx=vt:n===3&&vt&&(vt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else xe(t)?(t={default:t,_ctx:vt},s=32):(t=String(t),i&64?(s=16,t=[We(t)]):s=8);e.children=t,e.shapeFlag|=s}function iv(...e){const t={};for(let s=0;s<e.length;s++){const i=e[s];for(const n in i)if(n==="class")t.class!==i.class&&(t.class=de([t.class,i.class]));else if(n==="style")t.style=is([t.style,i.style]);else if(oo(n)){const a=t[n],u=i[n];u&&a!==u&&!(pe(a)&&a.includes(u))&&(t[n]=a?[].concat(a,u):u)}else n!==""&&(t[n]=i[n])}return t}function Ys(e,t,s,i=null){Ms(e,t,7,[s,i])}const av=Qd();let lv=0;function uv(e,t,s){const i=e.type,n=(t?t.appContext:e.appContext)||av,a={uid:lv++,vnode:e,type:i,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Uc(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:rf(i,n),emitsOptions:pf(i,n),emit:null,emitted:null,propsDefaults:et,inheritAttrs:i.inheritAttrs,ctx:et,data:et,props:et,attrs:et,slots:et,refs:et,setupState:et,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return{}.NODE_ENV!=="production"?a.ctx=g1(a):a.ctx={_:a},a.root=t?t.root:a,a.emit=Y1.bind(null,a),e.ce&&e.ce(a),a}let Dt=null;const Fi=()=>Dt||vt;let Li,$l;{const e=uo(),t=(s,i)=>{let n;return(n=e[s])||(n=e[s]=[]),n.push(i),a=>{n.length>1?n.forEach(u=>u(a)):n[0](a)}};Li=t("__VUE_INSTANCE_SETTERS__",s=>Dt=s),$l=t("__VUE_SSR_SETTERS__",s=>Mo=s)}const Ao=e=>{const t=Dt;return Li(e),e.scope.on(),()=>{e.scope.off(),Li(t)}},Of=()=>{Dt&&Dt.scope.off(),Li(null)},cv=rr("slot,component");function ql(e,{isNativeTag:t}){(cv(e)||t(e))&&K("Do not use built-in or reserved HTML elements as component id: "+e)}function xf(e){return e.vnode.shapeFlag&4}let Mo=!1;function dv(e,t=!1,s=!1){t&&$l(t);const{props:i,children:n}=e.vnode,a=xf(e);D1(e,i,a,t),L1(e,n,s||t);const u=a?fv(e,t):void 0;return t&&$l(!1),u}function fv(e,t){var s;const i=e.type;if({}.NODE_ENV!=="production"){if(i.name&&ql(i.name,e.appContext.config),i.components){const a=Object.keys(i.components);for(let u=0;u<a.length;u++)ql(a[u],e.appContext.config)}if(i.directives){const a=Object.keys(i.directives);for(let u=0;u<a.length;u++)Ed(a[u])}i.compilerOptions&&hv()&&K('"compilerOptions" is only supported when using a build of Vue that includes the runtime compiler. Since you are using a runtime-only build, the options should be passed via your build tool config instead.')}e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,zd),{}.NODE_ENV!=="production"&&_1(e);const{setup:n}=i;if(n){Is();const a=e.setupContext=n.length>1?mv(e):null,u=Ao(e),c=An(n,e,0,[{}.NODE_ENV!=="production"?Ws(e.props):e.props,a]),h=Ja(c);if(As(),u(),(h||e.sp)&&!kn(e)&&Ld(e),h){if(c.then(Of,Of),t)return c.then(_=>{Sf(e,_,t)}).catch(_=>{_o(_,e,0)});if(e.asyncDep=c,{}.NODE_ENV!=="production"&&!e.suspense){const _=(s=i.name)!=null?s:"Anonymous";K(`Component <${_}>: setup function returned a promise, but no <Suspense> boundary was found in the parent component tree. A component with async setup() must be nested in a <Suspense> in order to be rendered.`)}}else Sf(e,c,t)}else Df(e,t)}function Sf(e,t,s){xe(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Ye(t)?({}.NODE_ENV!=="production"&&un(t)&&K("setup() should not return VNodes directly - return a render function instead."),{}.NODE_ENV!=="production"&&(e.devtoolsRawSetupState=t),e.setupState=ud(t),{}.NODE_ENV!=="production"&&v1(e)):{}.NODE_ENV!=="production"&&t!==void 0&&K(`setup() should return an object. Received: ${t===null?"null":typeof t}`),Df(e,s)}let Hl;const hv=()=>!Hl;function Df(e,t,s){const i=e.type;if(!e.render){if(!t&&Hl&&!i.render){const n=i.template||Nl(e).template;if(n){({}).NODE_ENV!=="production"&&ar(e,"compile");const{isCustomElement:a,compilerOptions:u}=e.appContext.config,{delimiters:c,compilerOptions:h}=i,_=ht(ht({isCustomElement:a,delimiters:c},u),h);i.render=Hl(n,_),{}.NODE_ENV!=="production"&&lr(e,"compile")}}e.render=i.render||xt}{const n=Ao(e);Is();try{b1(e)}finally{As(),n()}}({}).NODE_ENV!=="production"&&!i.render&&e.render===xt&&!t&&(i.template?K('Component provided template option but runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".'):K("Component is missing template or render function: ",i))}const Tf={}.NODE_ENV!=="production"?{get(e,t){return Vi(),St(e,"get",""),e[t]},set(){return K("setupContext.attrs is readonly."),!1},deleteProperty(){return K("setupContext.attrs is readonly."),!1}}:{get(e,t){return St(e,"get",""),e[t]}};function pv(e){return new Proxy(e.slots,{get(t,s){return St(e,"get","$slots"),t[s]}})}function mv(e){const t=s=>{if({}.NODE_ENV!=="production"&&(e.exposed&&K("expose() should be called only once per setup()."),s!=null)){let i=typeof s;i==="object"&&(pe(s)?i="array":wt(s)&&(i="ref")),i!=="object"&&K(`expose() should be passed a plain object, received ${i}.`)}e.exposed=s||{}};if({}.NODE_ENV!=="production"){let s,i;return Object.freeze({get attrs(){return s||(s=new Proxy(e.attrs,Tf))},get slots(){return i||(i=pv(e))},get emit(){return(n,...a)=>e.emit(n,...a)},expose:t})}else return{attrs:new Proxy(e.attrs,Tf),slots:e.slots,emit:e.emit,expose:t}}function Ui(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(ud(hl(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in an)return an[s](e)},has(t,s){return s in t||s in an}})):e.proxy}const gv=/(?:^|[-_])(\w)/g,_v=e=>e.replace(gv,t=>t.toUpperCase()).replace(/[-_]/g,"");function Wl(e,t=!0){return xe(e)?e.displayName||e.name:e.name||t&&e.__name}function Bi(e,t,s=!1){let i=Wl(t);if(!i&&t.__file){const n=t.__file.match(/([^/\\]+)\.\w+$/);n&&(i=n[1])}if(!i&&e&&e.parent){const n=a=>{for(const u in a)if(a[u]===t)return u};i=n(e.components||e.parent.type.components)||n(e.appContext.components)}return i?_v(i):s?"App":"Anonymous"}function Nf(e){return xe(e)&&"__vccOpts"in e}const Vs=(e,t)=>{const s=T_(e,t,Mo);if({}.NODE_ENV!=="production"){const i=Fi();i&&i.appContext.config.warnRecursiveComputed&&(s._warnRecursive=!0)}return s};function jl(e,t,s){const i=arguments.length;return i===2?Ye(t)&&!pe(t)?un(t)?A(e,null,[t]):A(e,t):A(e,null,t):(i>3?s=Array.prototype.slice.call(arguments,2):i===3&&un(s)&&(s=[s]),A(e,t,s))}function vv(){if({}.NODE_ENV==="production"||typeof window>"u")return;const e={style:"color:#3ba776"},t={style:"color:#1677ff"},s={style:"color:#f5222d"},i={style:"color:#eb2f96"},n={__vue_custom_formatter:!0,header(g){if(!Ye(g))return null;if(g.__isVue)return["div",e,"VueInstance"];if(wt(g)){Is();const w=g.value;return As(),["div",{},["span",e,p(g)],"<",c(w),">"]}else{if(en(g))return["div",{},["span",e,jt(g)?"ShallowReactive":"Reactive"],"<",c(g),`>${js(g)?" (readonly)":""}`];if(js(g))return["div",{},["span",e,jt(g)?"ShallowReadonly":"Readonly"],"<",c(g),">"]}return null},hasBody(g){return g&&g.__isVue},body(g){if(g&&g.__isVue)return["div",{},...a(g.$)]}};function a(g){const w=[];g.type.props&&g.props&&w.push(u("props",Ae(g.props))),g.setupState!==et&&w.push(u("setup",g.setupState)),g.data!==et&&w.push(u("data",Ae(g.data)));const C=h(g,"computed");C&&w.push(u("computed",C));const M=h(g,"inject");return M&&w.push(u("injected",M)),w.push(["div",{},["span",{style:i.style+";opacity:0.66"},"$ (internal): "],["object",{object:g}]]),w}function u(g,w){return w=ht({},w),Object.keys(w).length?["div",{style:"line-height:1.25em;margin-bottom:0.6em"},["div",{style:"color:#476582"},g],["div",{style:"padding-left:1.25em"},...Object.keys(w).map(C=>["div",{},["span",i,C+": "],c(w[C],!1)])]]:["span",{}]}function c(g,w=!0){return typeof g=="number"?["span",t,g]:typeof g=="string"?["span",s,JSON.stringify(g)]:typeof g=="boolean"?["span",i,g]:Ye(g)?["object",{object:w?Ae(g):g}]:["span",s,String(g)]}function h(g,w){const C=g.type;if(xe(C))return;const M={};for(const F in g.ctx)_(C,F,w)&&(M[F]=g.ctx[F]);return M}function _(g,w,C){const M=g[C];if(pe(M)&&M.includes(w)||Ye(M)&&w in M||g.extends&&_(g.extends,w,C)||g.mixins&&g.mixins.some(F=>_(F,w,C)))return!0}function p(g){return jt(g)?"ShallowRef":g.effect?"ComputedRef":"Ref"}window.devtoolsFormatters?window.devtoolsFormatters.push(n):window.devtoolsFormatters=[n]}const If="3.5.17",Js={}.NODE_ENV!=="production"?K:xt;/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let zl;const Af=typeof window<"u"&&window.trustedTypes;if(Af)try{zl=Af.createPolicy("vue",{createHTML:e=>e})}catch(e){({}).NODE_ENV!=="production"&&Js(`Error creating trusted types policy: ${e}`)}const Mf=zl?e=>zl.createHTML(e):e=>e,yv="http://www.w3.org/2000/svg",bv="http://www.w3.org/1998/Math/MathML",ur=typeof document<"u"?document:null,Pf=ur&&ur.createElement("template"),Cv={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,i)=>{const n=t==="svg"?ur.createElementNS(yv,e):t==="mathml"?ur.createElementNS(bv,e):s?ur.createElement(e,{is:s}):ur.createElement(e);return e==="select"&&i&&i.multiple!=null&&n.setAttribute("multiple",i.multiple),n},createText:e=>ur.createTextNode(e),createComment:e=>ur.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ur.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,i,n,a){const u=s?s.previousSibling:t.lastChild;if(n&&(n===a||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),s),!(n===a||!(n=n.nextSibling)););else{Pf.innerHTML=Mf(i==="svg"?`<svg>${e}</svg>`:i==="mathml"?`<math>${e}</math>`:e);const c=Pf.content;if(i==="svg"||i==="mathml"){const h=c.firstChild;for(;h.firstChild;)c.appendChild(h.firstChild);c.removeChild(h)}t.insertBefore(c,s)}return[u?u.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},Pr="transition",Po="animation",ko=Symbol("_vtc"),kf={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},wv=ht({},Ad,kf),Vf=(e=>(e.displayName="Transition",e.props=wv,e))((e,{slots:t})=>jl(t1,Ev(e),t)),dn=(e,t=[])=>{pe(e)?e.forEach(s=>s(...t)):e&&e(...t)},Rf=e=>e?pe(e)?e.some(t=>t.length>1):e.length>1:!1;function Ev(e){const t={};for(const le in e)le in kf||(t[le]=e[le]);if(e.css===!1)return t;const{name:s="v",type:i,duration:n,enterFromClass:a=`${s}-enter-from`,enterActiveClass:u=`${s}-enter-active`,enterToClass:c=`${s}-enter-to`,appearFromClass:h=a,appearActiveClass:_=u,appearToClass:p=c,leaveFromClass:g=`${s}-leave-from`,leaveActiveClass:w=`${s}-leave-active`,leaveToClass:C=`${s}-leave-to`}=e,M=Ov(n),F=M&&M[0],se=M&&M[1],{onBeforeEnter:Q,onEnter:ne,onEnterCancelled:Y,onLeave:be,onLeaveCancelled:J,onBeforeAppear:he=Q,onAppear:ve=ne,onAppearCancelled:Ie=Y}=t,ie=(le,ze,mt,ue)=>{le._enterCancelled=ue,fn(le,ze?p:c),fn(le,ze?_:u),mt&&mt()},I=(le,ze)=>{le._isLeaving=!1,fn(le,g),fn(le,C),fn(le,w),ze&&ze()},Ce=le=>(ze,mt)=>{const ue=le?ve:ne,ot=()=>ie(ze,le,mt);dn(ue,[ze,ot]),Ff(()=>{fn(ze,le?h:a),cr(ze,le?p:c),Rf(ue)||Lf(ze,i,F,ot)})};return ht(t,{onBeforeEnter(le){dn(Q,[le]),cr(le,a),cr(le,u)},onBeforeAppear(le){dn(he,[le]),cr(le,h),cr(le,_)},onEnter:Ce(!1),onAppear:Ce(!0),onLeave(le,ze){le._isLeaving=!0;const mt=()=>I(le,ze);cr(le,g),le._enterCancelled?(cr(le,w),$f()):($f(),cr(le,w)),Ff(()=>{le._isLeaving&&(fn(le,g),cr(le,C),Rf(be)||Lf(le,i,se,mt))}),dn(be,[le,mt])},onEnterCancelled(le){ie(le,!1,void 0,!0),dn(Y,[le])},onAppearCancelled(le){ie(le,!0,void 0,!0),dn(Ie,[le])},onLeaveCancelled(le){I(le),dn(J,[le])}})}function Ov(e){if(e==null)return null;if(Ye(e))return[Gl(e.enter),Gl(e.leave)];{const t=Gl(e);return[t,t]}}function Gl(e){const t=jg(e);return{}.NODE_ENV!=="production"&&V_(t,"<transition> explicit duration"),t}function cr(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.add(s)),(e[ko]||(e[ko]=new Set)).add(t)}function fn(e,t){t.split(/\s+/).forEach(i=>i&&e.classList.remove(i));const s=e[ko];s&&(s.delete(t),s.size||(e[ko]=void 0))}function Ff(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let xv=0;function Lf(e,t,s,i){const n=e._endId=++xv,a=()=>{n===e._endId&&i()};if(s!=null)return setTimeout(a,s);const{type:u,timeout:c,propCount:h}=Sv(e,t);if(!u)return i();const _=u+"end";let p=0;const g=()=>{e.removeEventListener(_,w),a()},w=C=>{C.target===e&&++p>=h&&g()};setTimeout(()=>{p<h&&g()},c+1),e.addEventListener(_,w)}function Sv(e,t){const s=window.getComputedStyle(e),i=M=>(s[M]||"").split(", "),n=i(`${Pr}Delay`),a=i(`${Pr}Duration`),u=Uf(n,a),c=i(`${Po}Delay`),h=i(`${Po}Duration`),_=Uf(c,h);let p=null,g=0,w=0;t===Pr?u>0&&(p=Pr,g=u,w=a.length):t===Po?_>0&&(p=Po,g=_,w=h.length):(g=Math.max(u,_),p=g>0?u>_?Pr:Po:null,w=p?p===Pr?a.length:h.length:0);const C=p===Pr&&/\b(transform|all)(,|$)/.test(i(`${Pr}Property`).toString());return{type:p,timeout:g,propCount:w,hasTransform:C}}function Uf(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((s,i)=>Bf(s)+Bf(e[i])))}function Bf(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function $f(){return document.body.offsetHeight}function Dv(e,t,s){const i=e[ko];i&&(t=(t?[t,...i]:[...i]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const $i=Symbol("_vod"),qf=Symbol("_vsh"),Kl={beforeMount(e,{value:t},{transition:s}){e[$i]=e.style.display==="none"?"":e.style.display,s&&t?s.beforeEnter(e):Vo(e,t)},mounted(e,{value:t},{transition:s}){s&&t&&s.enter(e)},updated(e,{value:t,oldValue:s},{transition:i}){!t!=!s&&(i?t?(i.beforeEnter(e),Vo(e,!0),i.enter(e)):i.leave(e,()=>{Vo(e,!1)}):Vo(e,t))},beforeUnmount(e,{value:t}){Vo(e,t)}};({}).NODE_ENV!=="production"&&(Kl.name="show");function Vo(e,t){e.style.display=t?e[$i]:"none",e[qf]=!t}const Tv=Symbol({}.NODE_ENV!=="production"?"CSS_VAR_TEXT":""),Nv=/(^|;)\s*display\s*:/;function Iv(e,t,s){const i=e.style,n=ct(s);let a=!1;if(s&&!n){if(t)if(ct(t))for(const u of t.split(";")){const c=u.slice(0,u.indexOf(":")).trim();s[c]==null&&qi(i,c,"")}else for(const u in t)s[u]==null&&qi(i,u,"");for(const u in s)u==="display"&&(a=!0),qi(i,u,s[u])}else if(n){if(t!==s){const u=i[Tv];u&&(s+=";"+u),i.cssText=s,a=Nv.test(s)}}else t&&e.removeAttribute("style");$i in e&&(e[$i]=a?i.display:"",e[qf]&&(i.display="none"))}const Av=/[^\\];\s*$/,Hf=/\s*!important$/;function qi(e,t,s){if(pe(s))s.forEach(i=>qi(e,t,i));else if(s==null&&(s=""),{}.NODE_ENV!=="production"&&Av.test(s)&&Js(`Unexpected semicolon at the end of '${t}' style value: '${s}'`),t.startsWith("--"))e.setProperty(t,s);else{const i=Mv(e,t);Hf.test(s)?e.setProperty(Sr(i),s.replace(Hf,""),"important"):e[i]=s}}const Wf=["Webkit","Moz","ms"],Zl={};function Mv(e,t){const s=Zl[t];if(s)return s;let i=Wt(t);if(i!=="filter"&&i in e)return Zl[t]=i;i=Jr(i);for(let n=0;n<Wf.length;n++){const a=Wf[n]+i;if(a in e)return Zl[t]=a}return t}const jf="http://www.w3.org/1999/xlink";function zf(e,t,s,i,n,a=s_(t)){i&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(jf,t.slice(6,t.length)):e.setAttributeNS(jf,t,s):s==null||a&&!Rc(s)?e.removeAttribute(t):e.setAttribute(t,a?"":Ts(s)?String(s):s)}function Gf(e,t,s,i,n){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?Mf(s):s);return}const a=e.tagName;if(t==="value"&&a!=="PROGRESS"&&!a.includes("-")){const c=a==="OPTION"?e.getAttribute("value")||"":e.value,h=s==null?e.type==="checkbox"?"on":"":String(s);(c!==h||!("_value"in e))&&(e.value=h),s==null&&e.removeAttribute(t),e._value=s;return}let u=!1;if(s===""||s==null){const c=typeof e[t];c==="boolean"?s=Rc(s):s==null&&c==="string"?(s="",u=!0):c==="number"&&(s=0,u=!0)}try{e[t]=s}catch(c){({}).NODE_ENV!=="production"&&!u&&Js(`Failed setting prop "${t}" on <${a.toLowerCase()}>: value ${s} is invalid.`,c)}u&&e.removeAttribute(n||t)}function kr(e,t,s,i){e.addEventListener(t,s,i)}function Pv(e,t,s,i){e.removeEventListener(t,s,i)}const Kf=Symbol("_vei");function kv(e,t,s,i,n=null){const a=e[Kf]||(e[Kf]={}),u=a[t];if(i&&u)u.value={}.NODE_ENV!=="production"?Yf(i,t):i;else{const[c,h]=Vv(t);if(i){const _=a[t]=Lv({}.NODE_ENV!=="production"?Yf(i,t):i,n);kr(e,c,_,h)}else u&&(Pv(e,c,u,h),a[t]=void 0)}}const Zf=/(?:Once|Passive|Capture)$/;function Vv(e){let t;if(Zf.test(e)){t={};let i;for(;i=e.match(Zf);)e=e.slice(0,e.length-i[0].length),t[i[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Sr(e.slice(2)),t]}let Yl=0;const Rv=Promise.resolve(),Fv=()=>Yl||(Rv.then(()=>Yl=0),Yl=Date.now());function Lv(e,t){const s=i=>{if(!i._vts)i._vts=Date.now();else if(i._vts<=s.attached)return;Ms(Uv(i,s.value),t,5,[i])};return s.value=e,s.attached=Fv(),s}function Yf(e,t){return xe(e)||pe(e)?e:(Js(`Wrong type passed as event handler to ${t} - did you forget @ or : in front of your prop?
Expected function or array of functions, received type ${typeof e}.`),xt)}function Uv(e,t){if(pe(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(i=>n=>!n._stopped&&i&&i(n))}else return t}const Jf=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Bv=(e,t,s,i,n,a)=>{const u=n==="svg";t==="class"?Dv(e,i,u):t==="style"?Iv(e,s,i):oo(t)?ai(t)||kv(e,t,s,i,a):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):$v(e,t,i,u))?(Gf(e,t,i),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&zf(e,t,i,u,a,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ct(i))?Gf(e,Wt(t),i,a,t):(t==="true-value"?e._trueValue=i:t==="false-value"&&(e._falseValue=i),zf(e,t,i,u))};function $v(e,t,s,i){if(i)return!!(t==="innerHTML"||t==="textContent"||t in e&&Jf(t)&&xe(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return Jf(t)&&ct(s)?!1:t in e}const Fn=e=>{const t=e.props["onUpdate:modelValue"]||!1;return pe(t)?s=>Nn(t,s):t};function qv(e){e.target.composing=!0}function Qf(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const dr=Symbol("_assign"),Zt={created(e,{modifiers:{lazy:t,trim:s,number:i}},n){e[dr]=Fn(n);const a=i||n.props&&n.props.type==="number";kr(e,t?"change":"input",u=>{if(u.target.composing)return;let c=e.value;s&&(c=c.trim()),a&&(c=ui(c)),e[dr](c)}),s&&kr(e,"change",()=>{e.value=e.value.trim()}),t||(kr(e,"compositionstart",qv),kr(e,"compositionend",Qf),kr(e,"change",Qf))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:i,trim:n,number:a}},u){if(e[dr]=Fn(u),e.composing)return;const c=(a||e.type==="number")&&!/^0\d/.test(e.value)?ui(e.value):e.value,h=t??"";c!==h&&(document.activeElement===e&&e.type!=="range"&&(i&&t===s||n&&e.value.trim()===h)||(e.value=h))}},Hi={deep:!0,created(e,t,s){e[dr]=Fn(s),kr(e,"change",()=>{const i=e._modelValue,n=Ro(e),a=e.checked,u=e[dr];if(pe(i)){const c=el(i,n),h=c!==-1;if(a&&!h)u(i.concat(n));else if(!a&&h){const _=[...i];_.splice(c,1),u(_)}}else if(Tn(i)){const c=new Set(i);a?c.add(n):c.delete(n),u(c)}else u(th(e,a))})},mounted:Xf,beforeUpdate(e,t,s){e[dr]=Fn(s),Xf(e,t,s)}};function Xf(e,{value:t,oldValue:s},i){e._modelValue=t;let n;if(pe(t))n=el(t,i.props.value)>-1;else if(Tn(t))n=t.has(i.props.value);else{if(t===s)return;n=co(t,th(e,!0))}e.checked!==n&&(e.checked=n)}const Jl={deep:!0,created(e,{value:t,modifiers:{number:s}},i){const n=Tn(t);kr(e,"change",()=>{const a=Array.prototype.filter.call(e.options,u=>u.selected).map(u=>s?ui(Ro(u)):Ro(u));e[dr](e.multiple?n?new Set(a):a:a[0]),e._assigning=!0,gl(()=>{e._assigning=!1})}),e[dr]=Fn(i)},mounted(e,{value:t}){eh(e,t)},beforeUpdate(e,t,s){e[dr]=Fn(s)},updated(e,{value:t}){e._assigning||eh(e,t)}};function eh(e,t){const s=e.multiple,i=pe(t);if(s&&!i&&!Tn(t)){({}).NODE_ENV!=="production"&&Js(`<select multiple v-model> expects an Array or Set value for its binding, but got ${Object.prototype.toString.call(t).slice(8,-1)}.`);return}for(let n=0,a=e.options.length;n<a;n++){const u=e.options[n],c=Ro(u);if(s)if(i){const h=typeof c;h==="string"||h==="number"?u.selected=t.some(_=>String(_)===String(c)):u.selected=el(t,c)>-1}else u.selected=t.has(c);else if(co(Ro(u),t)){e.selectedIndex!==n&&(e.selectedIndex=n);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}function Ro(e){return"_value"in e?e._value:e.value}function th(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const Hv=["ctrl","shift","alt","meta"],Wv={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Hv.some(s=>e[`${s}Key`]&&!t.includes(s))},Pt=(e,t)=>{const s=e._withMods||(e._withMods={}),i=t.join(".");return s[i]||(s[i]=(n,...a)=>{for(let u=0;u<t.length;u++){const c=Wv[t[u]];if(c&&c(n,t))return}return e(n,...a)})},jv=ht({patchProp:Bv},Cv);let sh;function zv(){return sh||(sh=$1(jv))}const Gv=(...e)=>{const t=zv().createApp(...e);({}).NODE_ENV!=="production"&&(Zv(t),Yv(t));const{mount:s}=t;return t.mount=i=>{const n=Jv(i);if(!n)return;const a=t._component;!xe(a)&&!a.render&&!a.template&&(a.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const u=s(n,!1,Kv(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),u},t};function Kv(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Zv(e){Object.defineProperty(e.config,"isNativeTag",{value:t=>Xg(t)||e_(t)||t_(t),writable:!1})}function Yv(e){{const t=e.config.isCustomElement;Object.defineProperty(e.config,"isCustomElement",{get(){return t},set(){Js("The `isCustomElement` config option is deprecated. Use `compilerOptions.isCustomElement` instead.")}});const s=e.config.compilerOptions,i='The `compilerOptions` config option is only respected when using a build of Vue.js that includes the runtime compiler (aka "full build"). Since you are using the runtime-only build, `compilerOptions` must be passed to `@vue/compiler-dom` in the build setup instead.\n- For vue-loader: pass it via vue-loader\'s `compilerOptions` loader option.\n- For vue-cli: see https://cli.vuejs.org/guide/webpack.html#modifying-options-of-a-loader\n- For vite: pass it via @vitejs/plugin-vue options. See https://github.com/vitejs/vite-plugin-vue/tree/main/packages/plugin-vue#example-for-passing-options-to-vuecompiler-sfc';Object.defineProperty(e.config,"compilerOptions",{get(){return Js(i),s},set(){Js(i)}})}}function Jv(e){if(ct(e)){const t=document.querySelector(e);return{}.NODE_ENV!=="production"&&!t&&Js(`Failed to mount app: mount target selector "${e}" returned null.`),t}return{}.NODE_ENV!=="production"&&window.ShadowRoot&&e instanceof window.ShadowRoot&&e.mode==="closed"&&Js('mounting on a ShadowRoot with `{mode: "closed"}` may lead to unpredictable bugs'),e}/**
* vue v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Qv(){vv()}({}).NODE_ENV!=="production"&&Qv();var Xv=!1;function e0(){return rh().__VUE_DEVTOOLS_GLOBAL_HOOK__}function rh(){return typeof navigator<"u"&&typeof window<"u"?window:typeof globalThis<"u"?globalThis:{}}const t0=typeof Proxy=="function",s0="devtools-plugin:setup",r0="plugin:settings:set";let Ln,Ql;function n0(){var e;return Ln!==void 0||(typeof window<"u"&&window.performance?(Ln=!0,Ql=window.performance):typeof globalThis<"u"&&(!((e=globalThis.perf_hooks)===null||e===void 0)&&e.performance)?(Ln=!0,Ql=globalThis.perf_hooks.performance):Ln=!1),Ln}function o0(){return n0()?Ql.now():Date.now()}class i0{constructor(t,s){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=t,this.hook=s;const i={};if(t.settings)for(const u in t.settings){const c=t.settings[u];i[u]=c.defaultValue}const n=`__vue-devtools-plugin-settings__${t.id}`;let a=Object.assign({},i);try{const u=localStorage.getItem(n),c=JSON.parse(u);Object.assign(a,c)}catch{}this.fallbacks={getSettings(){return a},setSettings(u){try{localStorage.setItem(n,JSON.stringify(u))}catch{}a=u},now(){return o0()}},s&&s.on(r0,(u,c)=>{u===this.plugin.id&&this.fallbacks.setSettings(c)}),this.proxiedOn=new Proxy({},{get:(u,c)=>this.target?this.target.on[c]:(...h)=>{this.onQueue.push({method:c,args:h})}}),this.proxiedTarget=new Proxy({},{get:(u,c)=>this.target?this.target[c]:c==="on"?this.proxiedOn:Object.keys(this.fallbacks).includes(c)?(...h)=>(this.targetQueue.push({method:c,args:h,resolve:()=>{}}),this.fallbacks[c](...h)):(...h)=>new Promise(_=>{this.targetQueue.push({method:c,args:h,resolve:_})})})}async setRealTarget(t){this.target=t;for(const s of this.onQueue)this.target.on[s.method](...s.args);for(const s of this.targetQueue)s.resolve(await this.target[s.method](...s.args))}}function Xl(e,t){const s=e,i=rh(),n=e0(),a=t0&&s.enableEarlyProxy;if(n&&(i.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__||!a))n.emit(s0,e,t);else{const u=a?new i0(s,n):null;(i.__VUE_DEVTOOLS_PLUGINS__=i.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:s,setupFn:t,proxy:u}),u&&t(u.proxiedTarget)}}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const a0={}.NODE_ENV!=="production"?Symbol("pinia"):Symbol();var hn;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(hn||(hn={}));const eu=typeof window<"u",nh=(()=>typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof global=="object"&&global.global===global?global:typeof globalThis=="object"?globalThis:{HTMLElement:null})();function l0(e,{autoBom:t=!1}={}){return t&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e}function tu(e,t,s){const i=new XMLHttpRequest;i.open("GET",e),i.responseType="blob",i.onload=function(){ah(i.response,t,s)},i.onerror=function(){console.error("could not download file")},i.send()}function oh(e){const t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch{}return t.status>=200&&t.status<=299}function Wi(e){try{e.dispatchEvent(new MouseEvent("click"))}catch{const s=document.createEvent("MouseEvents");s.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(s)}}const ji=typeof navigator=="object"?navigator:{userAgent:""},ih=(()=>/Macintosh/.test(ji.userAgent)&&/AppleWebKit/.test(ji.userAgent)&&!/Safari/.test(ji.userAgent))(),ah=eu?typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype&&!ih?u0:"msSaveOrOpenBlob"in ji?c0:d0:()=>{};function u0(e,t="download",s){const i=document.createElement("a");i.download=t,i.rel="noopener",typeof e=="string"?(i.href=e,i.origin!==location.origin?oh(i.href)?tu(e,t,s):(i.target="_blank",Wi(i)):Wi(i)):(i.href=URL.createObjectURL(e),setTimeout(function(){URL.revokeObjectURL(i.href)},4e4),setTimeout(function(){Wi(i)},0))}function c0(e,t="download",s){if(typeof e=="string")if(oh(e))tu(e,t,s);else{const i=document.createElement("a");i.href=e,i.target="_blank",setTimeout(function(){Wi(i)})}else navigator.msSaveOrOpenBlob(l0(e,s),t)}function d0(e,t,s,i){if(i=i||open("","_blank"),i&&(i.document.title=i.document.body.innerText="downloading..."),typeof e=="string")return tu(e,t,s);const n=e.type==="application/octet-stream",a=/constructor/i.test(String(nh.HTMLElement))||"safari"in nh,u=/CriOS\/[\d]+/.test(navigator.userAgent);if((u||n&&a||ih)&&typeof FileReader<"u"){const c=new FileReader;c.onloadend=function(){let h=c.result;if(typeof h!="string")throw i=null,new Error("Wrong reader.result type");h=u?h:h.replace(/^data:[^;]*;/,"data:attachment/file;"),i?i.location.href=h:location.assign(h),i=null},c.readAsDataURL(e)}else{const c=URL.createObjectURL(e);i?i.location.assign(c):location.href=c,i=null,setTimeout(function(){URL.revokeObjectURL(c)},4e4)}}function kt(e,t){const s="🍍 "+e;typeof __VUE_DEVTOOLS_TOAST__=="function"?__VUE_DEVTOOLS_TOAST__(s,t):t==="error"?console.error(s):t==="warn"?console.warn(s):console.log(s)}function su(e){return"_a"in e&&"install"in e}function lh(){if(!("clipboard"in navigator))return kt("Your browser doesn't support the Clipboard API","error"),!0}function uh(e){return e instanceof Error&&e.message.toLowerCase().includes("document is not focused")?(kt('You need to activate the "Emulate a focused page" setting in the "Rendering" panel of devtools.',"warn"),!0):!1}async function f0(e){if(!lh())try{await navigator.clipboard.writeText(JSON.stringify(e.state.value)),kt("Global state copied to clipboard.")}catch(t){if(uh(t))return;kt("Failed to serialize the state. Check the console for more details.","error"),console.error(t)}}async function h0(e){if(!lh())try{ch(e,JSON.parse(await navigator.clipboard.readText())),kt("Global state pasted from clipboard.")}catch(t){if(uh(t))return;kt("Failed to deserialize the state from clipboard. Check the console for more details.","error"),console.error(t)}}async function p0(e){try{ah(new Blob([JSON.stringify(e.state.value)],{type:"text/plain;charset=utf-8"}),"pinia-state.json")}catch(t){kt("Failed to export the state as JSON. Check the console for more details.","error"),console.error(t)}}let fr;function m0(){fr||(fr=document.createElement("input"),fr.type="file",fr.accept=".json");function e(){return new Promise((t,s)=>{fr.onchange=async()=>{const i=fr.files;if(!i)return t(null);const n=i.item(0);return t(n?{text:await n.text(),file:n}:null)},fr.oncancel=()=>t(null),fr.onerror=s,fr.click()})}return e}async function g0(e){try{const s=await m0()();if(!s)return;const{text:i,file:n}=s;ch(e,JSON.parse(i)),kt(`Global state imported from "${n.name}".`)}catch(t){kt("Failed to import the state from JSON. Check the console for more details.","error"),console.error(t)}}function ch(e,t){for(const s in t){const i=e.state.value[s];i?Object.assign(i,t[s]):e.state.value[s]=t[s]}}function Rs(e){return{_custom:{display:e}}}const dh="🍍 Pinia (root)",zi="_root";function _0(e){return su(e)?{id:zi,label:dh}:{id:e.$id,label:e.$id}}function v0(e){if(su(e)){const s=Array.from(e._s.keys()),i=e._s;return{state:s.map(a=>({editable:!0,key:a,value:e.state.value[a]})),getters:s.filter(a=>i.get(a)._getters).map(a=>{const u=i.get(a);return{editable:!1,key:a,value:u._getters.reduce((c,h)=>(c[h]=u[h],c),{})}})}}const t={state:Object.keys(e.$state).map(s=>({editable:!0,key:s,value:e.$state[s]}))};return e._getters&&e._getters.length&&(t.getters=e._getters.map(s=>({editable:!1,key:s,value:e[s]}))),e._customProperties.size&&(t.customProperties=Array.from(e._customProperties).map(s=>({editable:!0,key:s,value:e[s]}))),t}function y0(e){return e?Array.isArray(e)?e.reduce((t,s)=>(t.keys.push(s.key),t.operations.push(s.type),t.oldValue[s.key]=s.oldValue,t.newValue[s.key]=s.newValue,t),{oldValue:{},keys:[],operations:[],newValue:{}}):{operation:Rs(e.type),key:Rs(e.key),oldValue:e.oldValue,newValue:e.newValue}:{}}function b0(e){switch(e){case hn.direct:return"mutation";case hn.patchFunction:return"$patch";case hn.patchObject:return"$patch";default:return"unknown"}}let Un=!0;const Gi=[],pn="pinia:mutations",$t="pinia",{assign:C0}=Object,Ki=e=>"🍍 "+e;function w0(e,t){Xl({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:Gi,app:e},s=>{typeof s.now!="function"&&kt("You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),s.addTimelineLayer({id:pn,label:"Pinia 🍍",color:15064968}),s.addInspector({id:$t,label:"Pinia 🍍",icon:"storage",treeFilterPlaceholder:"Search stores",actions:[{icon:"content_copy",action:()=>{f0(t)},tooltip:"Serialize and copy the state"},{icon:"content_paste",action:async()=>{await h0(t),s.sendInspectorTree($t),s.sendInspectorState($t)},tooltip:"Replace the state with the content of your clipboard"},{icon:"save",action:()=>{p0(t)},tooltip:"Save the state as a JSON file"},{icon:"folder_open",action:async()=>{await g0(t),s.sendInspectorTree($t),s.sendInspectorState($t)},tooltip:"Import the state from a JSON file"}],nodeActions:[{icon:"restore",tooltip:'Reset the state (with "$reset")',action:i=>{const n=t._s.get(i);n?typeof n.$reset!="function"?kt(`Cannot reset "${i}" store because it doesn't have a "$reset" method implemented.`,"warn"):(n.$reset(),kt(`Store "${i}" reset.`)):kt(`Cannot reset "${i}" store because it wasn't found.`,"warn")}}]}),s.on.inspectComponent((i,n)=>{const a=i.componentInstance&&i.componentInstance.proxy;if(a&&a._pStores){const u=i.componentInstance.proxy._pStores;Object.values(u).forEach(c=>{i.instanceData.state.push({type:Ki(c.$id),key:"state",editable:!0,value:c._isOptionsAPI?{_custom:{value:Ae(c.$state),actions:[{icon:"restore",tooltip:"Reset the state of this store",action:()=>c.$reset()}]}}:Object.keys(c.$state).reduce((h,_)=>(h[_]=c.$state[_],h),{})}),c._getters&&c._getters.length&&i.instanceData.state.push({type:Ki(c.$id),key:"getters",editable:!1,value:c._getters.reduce((h,_)=>{try{h[_]=c[_]}catch(p){h[_]=p}return h},{})})})}}),s.on.getInspectorTree(i=>{if(i.app===e&&i.inspectorId===$t){let n=[t];n=n.concat(Array.from(t._s.values())),i.rootNodes=(i.filter?n.filter(a=>"$id"in a?a.$id.toLowerCase().includes(i.filter.toLowerCase()):dh.toLowerCase().includes(i.filter.toLowerCase())):n).map(_0)}}),globalThis.$pinia=t,s.on.getInspectorState(i=>{if(i.app===e&&i.inspectorId===$t){const n=i.nodeId===zi?t:t._s.get(i.nodeId);if(!n)return;n&&(i.nodeId!==zi&&(globalThis.$store=Ae(n)),i.state=v0(n))}}),s.on.editInspectorState((i,n)=>{if(i.app===e&&i.inspectorId===$t){const a=i.nodeId===zi?t:t._s.get(i.nodeId);if(!a)return kt(`store "${i.nodeId}" not found`,"error");const{path:u}=i;su(a)?u.unshift("state"):(u.length!==1||!a._customProperties.has(u[0])||u[0]in a.$state)&&u.unshift("$state"),Un=!1,i.set(a,u,i.state.value),Un=!0}}),s.on.editComponentState(i=>{if(i.type.startsWith("🍍")){const n=i.type.replace(/^🍍\s*/,""),a=t._s.get(n);if(!a)return kt(`store "${n}" not found`,"error");const{path:u}=i;if(u[0]!=="state")return kt(`Invalid path for store "${n}":
${u}
Only state can be modified.`);u[0]="$state",Un=!1,i.set(a,u,i.state.value),Un=!0}})})}function E0(e,t){Gi.includes(Ki(t.$id))||Gi.push(Ki(t.$id)),Xl({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:Gi,app:e,settings:{logStoreChanges:{label:"Notify about new/deleted stores",type:"boolean",defaultValue:!0}}},s=>{const i=typeof s.now=="function"?s.now.bind(s):Date.now;t.$onAction(({after:u,onError:c,name:h,args:_})=>{const p=fh++;s.addTimelineEvent({layerId:pn,event:{time:i(),title:"🛫 "+h,subtitle:"start",data:{store:Rs(t.$id),action:Rs(h),args:_},groupId:p}}),u(g=>{Vr=void 0,s.addTimelineEvent({layerId:pn,event:{time:i(),title:"🛬 "+h,subtitle:"end",data:{store:Rs(t.$id),action:Rs(h),args:_,result:g},groupId:p}})}),c(g=>{Vr=void 0,s.addTimelineEvent({layerId:pn,event:{time:i(),logType:"error",title:"💥 "+h,subtitle:"end",data:{store:Rs(t.$id),action:Rs(h),args:_,error:g},groupId:p}})})},!0),t._customProperties.forEach(u=>{Rn(()=>Tr(t[u]),(c,h)=>{s.notifyComponentUpdate(),s.sendInspectorState($t),Un&&s.addTimelineEvent({layerId:pn,event:{time:i(),title:"Change",subtitle:u,data:{newValue:c,oldValue:h},groupId:Vr}})},{deep:!0})}),t.$subscribe(({events:u,type:c},h)=>{if(s.notifyComponentUpdate(),s.sendInspectorState($t),!Un)return;const _={time:i(),title:b0(c),data:C0({store:Rs(t.$id)},y0(u)),groupId:Vr};c===hn.patchFunction?_.subtitle="⤵️":c===hn.patchObject?_.subtitle="🧩":u&&!Array.isArray(u)&&(_.subtitle=u.type),u&&(_.data["rawEvent(s)"]={_custom:{display:"DebuggerEvent",type:"object",tooltip:"raw DebuggerEvent[]",value:u}}),s.addTimelineEvent({layerId:pn,event:_})},{detached:!0,flush:"sync"});const n=t._hotUpdate;t._hotUpdate=hl(u=>{n(u),s.addTimelineEvent({layerId:pn,event:{time:i(),title:"🔥 "+t.$id,subtitle:"HMR update",data:{store:Rs(t.$id),info:Rs("HMR update")}}}),s.notifyComponentUpdate(),s.sendInspectorTree($t),s.sendInspectorState($t)});const{$dispose:a}=t;t.$dispose=()=>{a(),s.notifyComponentUpdate(),s.sendInspectorTree($t),s.sendInspectorState($t),s.getSettings().logStoreChanges&&kt(`Disposed "${t.$id}" store 🗑`)},s.notifyComponentUpdate(),s.sendInspectorTree($t),s.sendInspectorState($t),s.getSettings().logStoreChanges&&kt(`"${t.$id}" store installed 🆕`)})}let fh=0,Vr;function hh(e,t,s){const i=t.reduce((n,a)=>(n[a]=Ae(e)[a],n),{});for(const n in i)e[n]=function(){const a=fh,u=s?new Proxy(e,{get(...h){return Vr=a,Reflect.get(...h)},set(...h){return Vr=a,Reflect.set(...h)}}):e;Vr=a;const c=i[n].apply(u,arguments);return Vr=void 0,c}}function O0({app:e,store:t,options:s}){if(!t.$id.startsWith("__hot:")){if(t._isOptionsAPI=!!s.state,!t._p._testing){hh(t,Object.keys(s.actions),t._isOptionsAPI);const i=t._hotUpdate;Ae(t)._hotUpdate=function(n){i.apply(this,arguments),hh(t,Object.keys(n._hmrPayload.actions),!!t._isOptionsAPI)}}E0(e,t)}}function x0(){const e=n_(!0),t=e.run(()=>ad({}));let s=[],i=[];const n=hl({install(a){n._a=a,a.provide(a0,n),a.config.globalProperties.$pinia=n,{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&eu&&w0(a,n),i.forEach(u=>s.push(u)),i=[]},use(a){return!this._a&&!Xv?i.push(a):s.push(a),this},_p:s,_a:null,_e:e,_s:new Map,state:t});return{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&eu&&typeof Proxy<"u"&&n.use(O0),n}const z6="",Pe=(e,t)=>{const s=e.__vccOpts||e;for(const[i,n]of t)s[i]=n;return s},S0={name:"App",mounted(){}},D0={id:"app"};function T0(e,t,s,i,n,a){const u=z("router-view");return x(),D("div",D0,[A(u)])}const N0=Pe(S0,[["render",T0]]);/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const hr=typeof document<"u";function ph(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function I0(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&ph(e.default)}const Je=Object.assign;function ru(e,t){const s={};for(const i in t){const n=t[i];s[i]=ls(n)?n.map(e):e(n)}return s}const Fo=()=>{},ls=Array.isArray;function $e(e){const t=Array.from(arguments).slice(1);console.warn.apply(console,["[Vue Router warn]: "+e].concat(t))}const mh=/#/g,A0=/&/g,M0=/\//g,P0=/=/g,k0=/\?/g,gh=/\+/g,V0=/%5B/g,R0=/%5D/g,_h=/%5E/g,F0=/%60/g,vh=/%7B/g,L0=/%7C/g,yh=/%7D/g,U0=/%20/g;function nu(e){return encodeURI(""+e).replace(L0,"|").replace(V0,"[").replace(R0,"]")}function B0(e){return nu(e).replace(vh,"{").replace(yh,"}").replace(_h,"^")}function ou(e){return nu(e).replace(gh,"%2B").replace(U0,"+").replace(mh,"%23").replace(A0,"%26").replace(F0,"`").replace(vh,"{").replace(yh,"}").replace(_h,"^")}function $0(e){return ou(e).replace(P0,"%3D")}function q0(e){return nu(e).replace(mh,"%23").replace(k0,"%3F")}function H0(e){return e==null?"":q0(e).replace(M0,"%2F")}function Bn(e){try{return decodeURIComponent(""+e)}catch{({}).NODE_ENV!=="production"&&$e(`Error decoding "${e}". Using original value`)}return""+e}const W0=/\/$/,j0=e=>e.replace(W0,"");function iu(e,t,s="/"){let i,n={},a="",u="";const c=t.indexOf("#");let h=t.indexOf("?");return c<h&&c>=0&&(h=-1),h>-1&&(i=t.slice(0,h),a=t.slice(h+1,c>-1?c:t.length),n=e(a)),c>-1&&(i=i||t.slice(0,c),u=t.slice(c,t.length)),i=K0(i??t,s),{fullPath:i+(a&&"?")+a+u,path:i,query:n,hash:Bn(u)}}function z0(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}function bh(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Ch(e,t,s){const i=t.matched.length-1,n=s.matched.length-1;return i>-1&&i===n&&Rr(t.matched[i],s.matched[n])&&wh(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}function Rr(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function wh(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!G0(e[s],t[s]))return!1;return!0}function G0(e,t){return ls(e)?Eh(e,t):ls(t)?Eh(t,e):e===t}function Eh(e,t){return ls(t)?e.length===t.length&&e.every((s,i)=>s===t[i]):e.length===1&&e[0]===t}function K0(e,t){if(e.startsWith("/"))return e;if({}.NODE_ENV!=="production"&&!t.startsWith("/"))return $e(`Cannot resolve a relative location without an absolute path. Trying to resolve "${e}" from "${t}". It should look like "/${t}".`),e;if(!e)return t;const s=t.split("/"),i=e.split("/"),n=i[i.length-1];(n===".."||n===".")&&i.push("");let a=s.length-1,u,c;for(u=0;u<i.length;u++)if(c=i[u],c!==".")if(c==="..")a>1&&a--;else break;return s.slice(0,a).join("/")+"/"+i.slice(u).join("/")}const Fr={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Lo;(function(e){e.pop="pop",e.push="push"})(Lo||(Lo={}));var Uo;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Uo||(Uo={}));function Z0(e){if(!e)if(hr){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),j0(e)}const Y0=/^[^#]+#/;function J0(e,t){return e.replace(Y0,"#")+t}function Q0(e,t){const s=document.documentElement.getBoundingClientRect(),i=e.getBoundingClientRect();return{behavior:t.behavior,left:i.left-s.left-(t.left||0),top:i.top-s.top-(t.top||0)}}const Zi=()=>({left:window.scrollX,top:window.scrollY});function X0(e){let t;if("el"in e){const s=e.el,i=typeof s=="string"&&s.startsWith("#");if({}.NODE_ENV!=="production"&&typeof e.el=="string"&&(!i||!document.getElementById(e.el.slice(1))))try{const a=document.querySelector(e.el);if(i&&a){$e(`The selector "${e.el}" should be passed as "el: document.querySelector('${e.el}')" because it starts with "#".`);return}}catch{$e(`The selector "${e.el}" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`);return}const n=typeof s=="string"?i?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!n){({}).NODE_ENV!=="production"&&$e(`Couldn't find element using selector "${e.el}" returned by scrollBehavior.`);return}t=Q0(n,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Oh(e,t){return(history.state?history.state.position-t:-1)+e}const au=new Map;function ey(e,t){au.set(e,t)}function ty(e){const t=au.get(e);return au.delete(e),t}let sy=()=>location.protocol+"//"+location.host;function xh(e,t){const{pathname:s,search:i,hash:n}=t,a=e.indexOf("#");if(a>-1){let c=n.includes(e.slice(a))?e.slice(a).length:1,h=n.slice(c);return h[0]!=="/"&&(h="/"+h),bh(h,"")}return bh(s,e)+i+n}function ry(e,t,s,i){let n=[],a=[],u=null;const c=({state:w})=>{const C=xh(e,location),M=s.value,F=t.value;let se=0;if(w){if(s.value=C,t.value=w,u&&u===M){u=null;return}se=F?w.position-F.position:0}else i(C);n.forEach(Q=>{Q(s.value,M,{delta:se,type:Lo.pop,direction:se?se>0?Uo.forward:Uo.back:Uo.unknown})})};function h(){u=s.value}function _(w){n.push(w);const C=()=>{const M=n.indexOf(w);M>-1&&n.splice(M,1)};return a.push(C),C}function p(){const{history:w}=window;w.state&&w.replaceState(Je({},w.state,{scroll:Zi()}),"")}function g(){for(const w of a)w();a=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",p)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",p,{passive:!0}),{pauseListeners:h,listen:_,destroy:g}}function Sh(e,t,s,i=!1,n=!1){return{back:e,current:t,forward:s,replaced:i,position:window.history.length,scroll:n?Zi():null}}function ny(e){const{history:t,location:s}=window,i={value:xh(e,s)},n={value:t.state};n.value||a(i.value,{back:null,current:i.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function a(h,_,p){const g=e.indexOf("#"),w=g>-1?(s.host&&document.querySelector("base")?e:e.slice(g))+h:sy()+e+h;try{t[p?"replaceState":"pushState"](_,"",w),n.value=_}catch(C){({}).NODE_ENV!=="production"?$e("Error with push/replace State",C):console.error(C),s[p?"replace":"assign"](w)}}function u(h,_){const p=Je({},t.state,Sh(n.value.back,h,n.value.forward,!0),_,{position:n.value.position});a(h,p,!0),i.value=h}function c(h,_){const p=Je({},n.value,t.state,{forward:h,scroll:Zi()});({}).NODE_ENV!=="production"&&!t.state&&$e(`history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:

history.replaceState(history.state, '', url)

You can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state`),a(p.current,p,!0);const g=Je({},Sh(i.value,h,null),{position:p.position+1},_);a(h,g,!1),i.value=h}return{location:i,state:n,push:c,replace:u}}function oy(e){e=Z0(e);const t=ny(e),s=ry(e,t.state,t.location,t.replace);function i(a,u=!0){u||s.pauseListeners(),history.go(a)}const n=Je({location:"",base:e,go:i,createHref:J0.bind(null,e)},t,s);return Object.defineProperty(n,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(n,"state",{enumerable:!0,get:()=>t.state.value}),n}function Yi(e){return typeof e=="string"||e&&typeof e=="object"}function Dh(e){return typeof e=="string"||typeof e=="symbol"}const lu=Symbol({}.NODE_ENV!=="production"?"navigation failure":"");var Th;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Th||(Th={}));const iy={1({location:e,currentLocation:t}){return`No match for
 ${JSON.stringify(e)}${t?`
while being at
`+JSON.stringify(t):""}`},2({from:e,to:t}){return`Redirected from "${e.fullPath}" to "${ly(t)}" via a navigation guard.`},4({from:e,to:t}){return`Navigation aborted from "${e.fullPath}" to "${t.fullPath}" via a navigation guard.`},8({from:e,to:t}){return`Navigation cancelled from "${e.fullPath}" to "${t.fullPath}" with a new navigation.`},16({from:e,to:t}){return`Avoided redundant navigation to current location: "${e.fullPath}".`}};function $n(e,t){return{}.NODE_ENV!=="production"?Je(new Error(iy[e](t)),{type:e,[lu]:!0},t):Je(new Error,{type:e,[lu]:!0},t)}function pr(e,t){return e instanceof Error&&lu in e&&(t==null||!!(e.type&t))}const ay=["params","query","hash"];function ly(e){if(typeof e=="string")return e;if(e.path!=null)return e.path;const t={};for(const s of ay)s in e&&(t[s]=e[s]);return JSON.stringify(t,null,2)}const Nh="[^/]+?",uy={sensitive:!1,strict:!1,start:!0,end:!0},cy=/[.+*?^${}()[\]/\\]/g;function dy(e,t){const s=Je({},uy,t),i=[];let n=s.start?"^":"";const a=[];for(const _ of e){const p=_.length?[]:[90];s.strict&&!_.length&&(n+="/");for(let g=0;g<_.length;g++){const w=_[g];let C=40+(s.sensitive?.25:0);if(w.type===0)g||(n+="/"),n+=w.value.replace(cy,"\\$&"),C+=40;else if(w.type===1){const{value:M,repeatable:F,optional:se,regexp:Q}=w;a.push({name:M,repeatable:F,optional:se});const ne=Q||Nh;if(ne!==Nh){C+=10;try{new RegExp(`(${ne})`)}catch(be){throw new Error(`Invalid custom RegExp for param "${M}" (${ne}): `+be.message)}}let Y=F?`((?:${ne})(?:/(?:${ne}))*)`:`(${ne})`;g||(Y=se&&_.length<2?`(?:/${Y})`:"/"+Y),se&&(Y+="?"),n+=Y,C+=20,se&&(C+=-8),F&&(C+=-20),ne===".*"&&(C+=-50)}p.push(C)}i.push(p)}if(s.strict&&s.end){const _=i.length-1;i[_][i[_].length-1]+=.7000000000000001}s.strict||(n+="/?"),s.end?n+="$":s.strict&&!n.endsWith("/")&&(n+="(?:/|$)");const u=new RegExp(n,s.sensitive?"":"i");function c(_){const p=_.match(u),g={};if(!p)return null;for(let w=1;w<p.length;w++){const C=p[w]||"",M=a[w-1];g[M.name]=C&&M.repeatable?C.split("/"):C}return g}function h(_){let p="",g=!1;for(const w of e){(!g||!p.endsWith("/"))&&(p+="/"),g=!1;for(const C of w)if(C.type===0)p+=C.value;else if(C.type===1){const{value:M,repeatable:F,optional:se}=C,Q=M in _?_[M]:"";if(ls(Q)&&!F)throw new Error(`Provided param "${M}" is an array but it is not repeatable (* or + modifiers)`);const ne=ls(Q)?Q.join("/"):Q;if(!ne)if(se)w.length<2&&(p.endsWith("/")?p=p.slice(0,-1):g=!0);else throw new Error(`Missing required param "${M}"`);p+=ne}}return p||"/"}return{re:u,score:i,keys:a,parse:c,stringify:h}}function fy(e,t){let s=0;for(;s<e.length&&s<t.length;){const i=t[s]-e[s];if(i)return i;s++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function Ih(e,t){let s=0;const i=e.score,n=t.score;for(;s<i.length&&s<n.length;){const a=fy(i[s],n[s]);if(a)return a;s++}if(Math.abs(n.length-i.length)===1){if(Ah(i))return 1;if(Ah(n))return-1}return n.length-i.length}function Ah(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const hy={type:0,value:""},py=/[a-zA-Z0-9_]/;function my(e){if(!e)return[[]];if(e==="/")return[[hy]];if(!e.startsWith("/"))throw new Error({}.NODE_ENV!=="production"?`Route paths should start with a "/": "${e}" should be "/${e}".`:`Invalid path "${e}"`);function t(C){throw new Error(`ERR (${s})/"${_}": ${C}`)}let s=0,i=s;const n=[];let a;function u(){a&&n.push(a),a=[]}let c=0,h,_="",p="";function g(){_&&(s===0?a.push({type:0,value:_}):s===1||s===2||s===3?(a.length>1&&(h==="*"||h==="+")&&t(`A repeatable param (${_}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:_,regexp:p,repeatable:h==="*"||h==="+",optional:h==="*"||h==="?"})):t("Invalid state to consume buffer"),_="")}function w(){_+=h}for(;c<e.length;){if(h=e[c++],h==="\\"&&s!==2){i=s,s=4;continue}switch(s){case 0:h==="/"?(_&&g(),u()):h===":"?(g(),s=1):w();break;case 4:w(),s=i;break;case 1:h==="("?s=2:py.test(h)?w():(g(),s=0,h!=="*"&&h!=="?"&&h!=="+"&&c--);break;case 2:h===")"?p[p.length-1]=="\\"?p=p.slice(0,-1)+h:s=3:p+=h;break;case 3:g(),s=0,h!=="*"&&h!=="?"&&h!=="+"&&c--,p="";break;default:t("Unknown state");break}}return s===2&&t(`Unfinished custom RegExp for param "${_}"`),g(),u(),n}function gy(e,t,s){const i=dy(my(e.path),s);if({}.NODE_ENV!=="production"){const a=new Set;for(const u of i.keys)a.has(u.name)&&$e(`Found duplicated params with name "${u.name}" for path "${e.path}". Only the last one will be available on "$route.params".`),a.add(u.name)}const n=Je(i,{record:e,parent:t,children:[],alias:[]});return t&&!n.record.aliasOf==!t.record.aliasOf&&t.children.push(n),n}function _y(e,t){const s=[],i=new Map;t=Vh({strict:!1,end:!0,sensitive:!1},t);function n(g){return i.get(g)}function a(g,w,C){const M=!C,F=Ph(g);({}).NODE_ENV!=="production"&&Cy(F,w),F.aliasOf=C&&C.record;const se=Vh(t,g),Q=[F];if("alias"in g){const be=typeof g.alias=="string"?[g.alias]:g.alias;for(const J of be)Q.push(Ph(Je({},F,{components:C?C.record.components:F.components,path:J,aliasOf:C?C.record:F})))}let ne,Y;for(const be of Q){const{path:J}=be;if(w&&J[0]!=="/"){const he=w.record.path,ve=he[he.length-1]==="/"?"":"/";be.path=w.record.path+(J&&ve+J)}if({}.NODE_ENV!=="production"&&be.path==="*")throw new Error(`Catch all routes ("*") must now be defined using a param with a custom regexp.
See more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.`);if(ne=gy(be,w,se),{}.NODE_ENV!=="production"&&w&&J[0]==="/"&&Ey(ne,w),C?(C.alias.push(ne),{}.NODE_ENV!=="production"&&by(C,ne)):(Y=Y||ne,Y!==ne&&Y.alias.push(ne),M&&g.name&&!kh(ne)&&({}.NODE_ENV!=="production"&&wy(g,w),u(g.name))),Rh(ne)&&h(ne),F.children){const he=F.children;for(let ve=0;ve<he.length;ve++)a(he[ve],ne,C&&C.children[ve])}C=C||ne}return Y?()=>{u(Y)}:Fo}function u(g){if(Dh(g)){const w=i.get(g);w&&(i.delete(g),s.splice(s.indexOf(w),1),w.children.forEach(u),w.alias.forEach(u))}else{const w=s.indexOf(g);w>-1&&(s.splice(w,1),g.record.name&&i.delete(g.record.name),g.children.forEach(u),g.alias.forEach(u))}}function c(){return s}function h(g){const w=Oy(g,s);s.splice(w,0,g),g.record.name&&!kh(g)&&i.set(g.record.name,g)}function _(g,w){let C,M={},F,se;if("name"in g&&g.name){if(C=i.get(g.name),!C)throw $n(1,{location:g});if({}.NODE_ENV!=="production"){const Y=Object.keys(g.params||{}).filter(be=>!C.keys.find(J=>J.name===be));Y.length&&$e(`Discarded invalid param(s) "${Y.join('", "')}" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`)}se=C.record.name,M=Je(Mh(w.params,C.keys.filter(Y=>!Y.optional).concat(C.parent?C.parent.keys.filter(Y=>Y.optional):[]).map(Y=>Y.name)),g.params&&Mh(g.params,C.keys.map(Y=>Y.name))),F=C.stringify(M)}else if(g.path!=null)F=g.path,{}.NODE_ENV!=="production"&&!F.startsWith("/")&&$e(`The Matcher cannot resolve relative paths but received "${F}". Unless you directly called \`matcher.resolve("${F}")\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`),C=s.find(Y=>Y.re.test(F)),C&&(M=C.parse(F),se=C.record.name);else{if(C=w.name?i.get(w.name):s.find(Y=>Y.re.test(w.path)),!C)throw $n(1,{location:g,currentLocation:w});se=C.record.name,M=Je({},w.params,g.params),F=C.stringify(M)}const Q=[];let ne=C;for(;ne;)Q.unshift(ne.record),ne=ne.parent;return{name:se,path:F,params:M,matched:Q,meta:yy(Q)}}e.forEach(g=>a(g));function p(){s.length=0,i.clear()}return{addRoute:a,resolve:_,removeRoute:u,clearRoutes:p,getRoutes:c,getRecordMatcher:n}}function Mh(e,t){const s={};for(const i of t)i in e&&(s[i]=e[i]);return s}function Ph(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:vy(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function vy(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const i in e.components)t[i]=typeof s=="object"?s[i]:s;return t}function kh(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function yy(e){return e.reduce((t,s)=>Je(t,s.meta),{})}function Vh(e,t){const s={};for(const i in e)s[i]=i in t?t[i]:e[i];return s}function uu(e,t){return e.name===t.name&&e.optional===t.optional&&e.repeatable===t.repeatable}function by(e,t){for(const s of e.keys)if(!s.optional&&!t.keys.find(uu.bind(null,s)))return $e(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`);for(const s of t.keys)if(!s.optional&&!e.keys.find(uu.bind(null,s)))return $e(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`)}function Cy(e,t){t&&t.record.name&&!e.name&&!e.path&&$e(`The route named "${String(t.record.name)}" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`)}function wy(e,t){for(let s=t;s;s=s.parent)if(s.record.name===e.name)throw new Error(`A route named "${String(e.name)}" has been added as a ${t===s?"child":"descendant"} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`)}function Ey(e,t){for(const s of t.keys)if(!e.keys.find(uu.bind(null,s)))return $e(`Absolute path "${e.record.path}" must have the exact same param named "${s.name}" as its parent "${t.record.path}".`)}function Oy(e,t){let s=0,i=t.length;for(;s!==i;){const a=s+i>>1;Ih(e,t[a])<0?i=a:s=a+1}const n=xy(e);return n&&(i=t.lastIndexOf(n,i-1),{}.NODE_ENV!=="production"&&i<0&&$e(`Finding ancestor route "${n.record.path}" failed for "${e.record.path}"`)),i}function xy(e){let t=e;for(;t=t.parent;)if(Rh(t)&&Ih(e,t)===0)return t}function Rh({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Sy(e){const t={};if(e===""||e==="?")return t;const i=(e[0]==="?"?e.slice(1):e).split("&");for(let n=0;n<i.length;++n){const a=i[n].replace(gh," "),u=a.indexOf("="),c=Bn(u<0?a:a.slice(0,u)),h=u<0?null:Bn(a.slice(u+1));if(c in t){let _=t[c];ls(_)||(_=t[c]=[_]),_.push(h)}else t[c]=h}return t}function Fh(e){let t="";for(let s in e){const i=e[s];if(s=$0(s),i==null){i!==void 0&&(t+=(t.length?"&":"")+s);continue}(ls(i)?i.map(a=>a&&ou(a)):[i&&ou(i)]).forEach(a=>{a!==void 0&&(t+=(t.length?"&":"")+s,a!=null&&(t+="="+a))})}return t}function Dy(e){const t={};for(const s in e){const i=e[s];i!==void 0&&(t[s]=ls(i)?i.map(n=>n==null?null:""+n):i==null?i:""+i)}return t}const Ty=Symbol({}.NODE_ENV!=="production"?"router view location matched":""),Lh=Symbol({}.NODE_ENV!=="production"?"router view depth":""),Ji=Symbol({}.NODE_ENV!=="production"?"router":""),Uh=Symbol({}.NODE_ENV!=="production"?"route location":""),cu=Symbol({}.NODE_ENV!=="production"?"router view location":"");function Bo(){let e=[];function t(i){return e.push(i),()=>{const n=e.indexOf(i);n>-1&&e.splice(n,1)}}function s(){e=[]}return{add:t,list:()=>e.slice(),reset:s}}function Lr(e,t,s,i,n,a=u=>u()){const u=i&&(i.enterCallbacks[n]=i.enterCallbacks[n]||[]);return()=>new Promise((c,h)=>{const _=w=>{w===!1?h($n(4,{from:s,to:t})):w instanceof Error?h(w):Yi(w)?h($n(2,{from:t,to:w})):(u&&i.enterCallbacks[n]===u&&typeof w=="function"&&u.push(w),c())},p=a(()=>e.call(i&&i.instances[n],t,s,{}.NODE_ENV!=="production"?Ny(_,t,s):_));let g=Promise.resolve(p);if(e.length<3&&(g=g.then(_)),{}.NODE_ENV!=="production"&&e.length>2){const w=`The "next" callback was never called inside of ${e.name?'"'+e.name+'"':""}:
${e.toString()}
. If you are returning a value instead of calling "next", make sure to remove the "next" parameter from your function.`;if(typeof p=="object"&&"then"in p)g=g.then(C=>_._called?C:($e(w),Promise.reject(new Error("Invalid navigation guard"))));else if(p!==void 0&&!_._called){$e(w),h(new Error("Invalid navigation guard"));return}}g.catch(w=>h(w))})}function Ny(e,t,s){let i=0;return function(){i++===1&&$e(`The "next" callback was called more than once in one navigation guard when going from "${s.fullPath}" to "${t.fullPath}". It should be called exactly one time in each navigation guard. This will fail in production.`),e._called=!0,i===1&&e.apply(null,arguments)}}function du(e,t,s,i,n=a=>a()){const a=[];for(const u of e){({}).NODE_ENV!=="production"&&!u.components&&!u.children.length&&$e(`Record with path "${u.path}" is either missing a "component(s)" or "children" property.`);for(const c in u.components){let h=u.components[c];if({}.NODE_ENV!=="production"){if(!h||typeof h!="object"&&typeof h!="function")throw $e(`Component "${c}" in record with path "${u.path}" is not a valid component. Received "${String(h)}".`),new Error("Invalid route component");if("then"in h){$e(`Component "${c}" in record with path "${u.path}" is a Promise instead of a function that returns a Promise. Did you write "import('./MyPage.vue')" instead of "() => import('./MyPage.vue')" ? This will break in production if not fixed.`);const _=h;h=()=>_}else h.__asyncLoader&&!h.__warnedDefineAsync&&(h.__warnedDefineAsync=!0,$e(`Component "${c}" in record with path "${u.path}" is defined using "defineAsyncComponent()". Write "() => import('./MyPage.vue')" instead of "defineAsyncComponent(() => import('./MyPage.vue'))".`))}if(!(t!=="beforeRouteEnter"&&!u.instances[c]))if(ph(h)){const p=(h.__vccOpts||h)[t];p&&a.push(Lr(p,s,i,u,c,n))}else{let _=h();({}).NODE_ENV!=="production"&&!("catch"in _)&&($e(`Component "${c}" in record with path "${u.path}" is a function that does not return a Promise. If you were passing a functional component, make sure to add a "displayName" to the component. This will break in production if not fixed.`),_=Promise.resolve(_)),a.push(()=>_.then(p=>{if(!p)throw new Error(`Couldn't resolve component "${c}" at "${u.path}"`);const g=I0(p)?p.default:p;u.mods[c]=p,u.components[c]=g;const C=(g.__vccOpts||g)[t];return C&&Lr(C,s,i,u,c,n)()}))}}}return a}function Bh(e){const t=Ks(Ji),s=Ks(Uh);let i=!1,n=null;const a=Vs(()=>{const p=Tr(e.to);return{}.NODE_ENV!=="production"&&(!i||p!==n)&&(Yi(p)||(i?$e(`Invalid value for prop "to" in useLink()
- to:`,p,`
- previous to:`,n,`
- props:`,e):$e(`Invalid value for prop "to" in useLink()
- to:`,p,`
- props:`,e)),n=p,i=!0),t.resolve(p)}),u=Vs(()=>{const{matched:p}=a.value,{length:g}=p,w=p[g-1],C=s.matched;if(!w||!C.length)return-1;const M=C.findIndex(Rr.bind(null,w));if(M>-1)return M;const F=$h(p[g-2]);return g>1&&$h(w)===F&&C[C.length-1].path!==F?C.findIndex(Rr.bind(null,p[g-2])):M}),c=Vs(()=>u.value>-1&&Py(s.params,a.value.params)),h=Vs(()=>u.value>-1&&u.value===s.matched.length-1&&wh(s.params,a.value.params));function _(p={}){if(My(p)){const g=t[Tr(e.replace)?"replace":"push"](Tr(e.to)).catch(Fo);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>g),g}return Promise.resolve()}if({}.NODE_ENV!=="production"&&hr){const p=Fi();if(p){const g={route:a.value,isActive:c.value,isExactActive:h.value,error:null};p.__vrl_devtools=p.__vrl_devtools||[],p.__vrl_devtools.push(g),G1(()=>{g.route=a.value,g.isActive=c.value,g.isExactActive=h.value,g.error=Yi(Tr(e.to))?null:'Invalid "to" value'},{flush:"post"})}}return{route:a,href:Vs(()=>a.value.href),isActive:c,isExactActive:h,navigate:_}}function Iy(e){return e.length===1?e[0]:e}const Ay=Fd({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Bh,setup(e,{slots:t}){const s=pi(Bh(e)),{options:i}=Ks(Ji),n=Vs(()=>({[qh(e.activeClass,i.linkActiveClass,"router-link-active")]:s.isActive,[qh(e.exactActiveClass,i.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const a=t.default&&Iy(t.default(s));return e.custom?a:jl("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:n.value},a)}}});function My(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Py(e,t){for(const s in t){const i=t[s],n=e[s];if(typeof i=="string"){if(i!==n)return!1}else if(!ls(n)||n.length!==i.length||i.some((a,u)=>a!==n[u]))return!1}return!0}function $h(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const qh=(e,t,s)=>e??t??s,ky=Fd({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){({}).NODE_ENV!=="production"&&Ry();const i=Ks(cu),n=Vs(()=>e.route||i.value),a=Ks(Lh,0),u=Vs(()=>{let _=Tr(a);const{matched:p}=n.value;let g;for(;(g=p[_])&&!g.components;)_++;return _}),c=Vs(()=>n.value.matched[u.value]);Mi(Lh,Vs(()=>u.value+1)),Mi(Ty,c),Mi(cu,n);const h=ad();return Rn(()=>[h.value,c.value,e.name],([_,p,g],[w,C,M])=>{p&&(p.instances[g]=_,C&&C!==p&&_&&_===w&&(p.leaveGuards.size||(p.leaveGuards=C.leaveGuards),p.updateGuards.size||(p.updateGuards=C.updateGuards))),_&&p&&(!C||!Rr(p,C)||!w)&&(p.enterCallbacks[g]||[]).forEach(F=>F(_))},{flush:"post"}),()=>{const _=n.value,p=e.name,g=c.value,w=g&&g.components[p];if(!w)return Hh(s.default,{Component:w,route:_});const C=g.props[p],M=C?C===!0?_.params:typeof C=="function"?C(_):C:null,se=jl(w,Je({},M,t,{onVnodeUnmounted:Q=>{Q.component.isUnmounted&&(g.instances[p]=null)},ref:h}));if({}.NODE_ENV!=="production"&&hr&&se.ref){const Q={depth:u.value,name:g.name,path:g.path,meta:g.meta};(ls(se.ref)?se.ref.map(Y=>Y.i):[se.ref.i]).forEach(Y=>{Y.__vrv_devtools=Q})}return Hh(s.default,{Component:se,route:_})||se}}});function Hh(e,t){if(!e)return null;const s=e(t);return s.length===1?s[0]:s}const Vy=ky;function Ry(){const e=Fi(),t=e.parent&&e.parent.type.name,s=e.parent&&e.parent.subTree&&e.parent.subTree.type;if(t&&(t==="KeepAlive"||t.includes("Transition"))&&typeof s=="object"&&s.name==="RouterView"){const i=t==="KeepAlive"?"keep-alive":"transition";$e(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.
Use slot props instead:

<router-view v-slot="{ Component }">
  <${i}>
    <component :is="Component" />
  </${i}>
</router-view>`)}}function $o(e,t){const s=Je({},e,{matched:e.matched.map(i=>Gy(i,["instances","children","aliasOf"]))});return{_custom:{type:null,readOnly:!0,display:e.fullPath,tooltip:t,value:s}}}function Qi(e){return{_custom:{display:e}}}let Fy=0;function Ly(e,t,s){if(t.__hasDevtools)return;t.__hasDevtools=!0;const i=Fy++;Xl({id:"org.vuejs.router"+(i?"."+i:""),label:"Vue Router",packageName:"vue-router",homepage:"https://router.vuejs.org",logo:"https://router.vuejs.org/logo.png",componentStateTypes:["Routing"],app:e},n=>{typeof n.now!="function"&&console.warn("[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),n.on.inspectComponent((p,g)=>{p.instanceData&&p.instanceData.state.push({type:"Routing",key:"$route",editable:!1,value:$o(t.currentRoute.value,"Current Route")})}),n.on.visitComponentTree(({treeNode:p,componentInstance:g})=>{if(g.__vrv_devtools){const w=g.__vrv_devtools;p.tags.push({label:(w.name?`${w.name.toString()}: `:"")+w.path,textColor:0,tooltip:"This component is rendered by &lt;router-view&gt;",backgroundColor:Wh})}ls(g.__vrl_devtools)&&(g.__devtoolsApi=n,g.__vrl_devtools.forEach(w=>{let C=w.route.path,M=Gh,F="",se=0;w.error?(C=w.error,M=Hy,se=Wy):w.isExactActive?(M=zh,F="This is exactly active"):w.isActive&&(M=jh,F="This link is active"),p.tags.push({label:C,textColor:se,tooltip:F,backgroundColor:M})}))}),Rn(t.currentRoute,()=>{h(),n.notifyComponentUpdate(),n.sendInspectorTree(c),n.sendInspectorState(c)});const a="router:navigations:"+i;n.addTimelineLayer({id:a,label:`Router${i?" "+i:""} Navigations`,color:4237508}),t.onError((p,g)=>{n.addTimelineEvent({layerId:a,event:{title:"Error during Navigation",subtitle:g.fullPath,logType:"error",time:n.now(),data:{error:p},groupId:g.meta.__navigationId}})});let u=0;t.beforeEach((p,g)=>{const w={guard:Qi("beforeEach"),from:$o(g,"Current Location during this navigation"),to:$o(p,"Target location")};Object.defineProperty(p.meta,"__navigationId",{value:u++}),n.addTimelineEvent({layerId:a,event:{time:n.now(),title:"Start of navigation",subtitle:p.fullPath,data:w,groupId:p.meta.__navigationId}})}),t.afterEach((p,g,w)=>{const C={guard:Qi("afterEach")};w?(C.failure={_custom:{type:Error,readOnly:!0,display:w?w.message:"",tooltip:"Navigation Failure",value:w}},C.status=Qi("❌")):C.status=Qi("✅"),C.from=$o(g,"Current Location during this navigation"),C.to=$o(p,"Target location"),n.addTimelineEvent({layerId:a,event:{title:"End of navigation",subtitle:p.fullPath,time:n.now(),data:C,logType:w?"warning":"default",groupId:p.meta.__navigationId}})});const c="router-inspector:"+i;n.addInspector({id:c,label:"Routes"+(i?" "+i:""),icon:"book",treeFilterPlaceholder:"Search routes"});function h(){if(!_)return;const p=_;let g=s.getRoutes().filter(w=>!w.parent||!w.parent.record.components);g.forEach(Yh),p.filter&&(g=g.filter(w=>fu(w,p.filter.toLowerCase()))),g.forEach(w=>Zh(w,t.currentRoute.value)),p.rootNodes=g.map(Kh)}let _;n.on.getInspectorTree(p=>{_=p,p.app===e&&p.inspectorId===c&&h()}),n.on.getInspectorState(p=>{if(p.app===e&&p.inspectorId===c){const w=s.getRoutes().find(C=>C.record.__vd_id===p.nodeId);w&&(p.state={options:By(w)})}}),n.sendInspectorTree(c),n.sendInspectorState(c)})}function Uy(e){return e.optional?e.repeatable?"*":"?":e.repeatable?"+":""}function By(e){const{record:t}=e,s=[{editable:!1,key:"path",value:t.path}];return t.name!=null&&s.push({editable:!1,key:"name",value:t.name}),s.push({editable:!1,key:"regexp",value:e.re}),e.keys.length&&s.push({editable:!1,key:"keys",value:{_custom:{type:null,readOnly:!0,display:e.keys.map(i=>`${i.name}${Uy(i)}`).join(" "),tooltip:"Param keys",value:e.keys}}}),t.redirect!=null&&s.push({editable:!1,key:"redirect",value:t.redirect}),e.alias.length&&s.push({editable:!1,key:"aliases",value:e.alias.map(i=>i.record.path)}),Object.keys(e.record.meta).length&&s.push({editable:!1,key:"meta",value:e.record.meta}),s.push({key:"score",editable:!1,value:{_custom:{type:null,readOnly:!0,display:e.score.map(i=>i.join(", ")).join(" | "),tooltip:"Score used to sort routes",value:e.score}}}),s}const Wh=15485081,jh=2450411,zh=8702998,$y=2282478,Gh=16486972,qy=6710886,Hy=16704226,Wy=12131356;function Kh(e){const t=[],{record:s}=e;s.name!=null&&t.push({label:String(s.name),textColor:0,backgroundColor:$y}),s.aliasOf&&t.push({label:"alias",textColor:0,backgroundColor:Gh}),e.__vd_match&&t.push({label:"matches",textColor:0,backgroundColor:Wh}),e.__vd_exactActive&&t.push({label:"exact",textColor:0,backgroundColor:zh}),e.__vd_active&&t.push({label:"active",textColor:0,backgroundColor:jh}),s.redirect&&t.push({label:typeof s.redirect=="string"?`redirect: ${s.redirect}`:"redirects",textColor:16777215,backgroundColor:qy});let i=s.__vd_id;return i==null&&(i=String(jy++),s.__vd_id=i),{id:i,label:s.path,tags:t,children:e.children.map(Kh)}}let jy=0;const zy=/^\/(.*)\/([a-z]*)$/;function Zh(e,t){const s=t.matched.length&&Rr(t.matched[t.matched.length-1],e.record);e.__vd_exactActive=e.__vd_active=s,s||(e.__vd_active=t.matched.some(i=>Rr(i,e.record))),e.children.forEach(i=>Zh(i,t))}function Yh(e){e.__vd_match=!1,e.children.forEach(Yh)}function fu(e,t){const s=String(e.re).match(zy);if(e.__vd_match=!1,!s||s.length<3)return!1;if(new RegExp(s[1].replace(/\$$/,""),s[2]).test(t))return e.children.forEach(u=>fu(u,t)),e.record.path!=="/"||t==="/"?(e.__vd_match=e.re.test(t),!0):!1;const n=e.record.path.toLowerCase(),a=Bn(n);return!t.startsWith("/")&&(a.includes(t)||n.includes(t))||a.startsWith(t)||n.startsWith(t)||e.record.name&&String(e.record.name).includes(t)?!0:e.children.some(u=>fu(u,t))}function Gy(e,t){const s={};for(const i in e)t.includes(i)||(s[i]=e[i]);return s}function Ky(e){const t=_y(e.routes,e),s=e.parseQuery||Sy,i=e.stringifyQuery||Fh,n=e.history;if({}.NODE_ENV!=="production"&&!n)throw new Error('Provide the "history" option when calling "createRouter()": https://router.vuejs.org/api/interfaces/RouterOptions.html#history');const a=Bo(),u=Bo(),c=Bo(),h=O_(Fr);let _=Fr;hr&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const p=ru.bind(null,V=>""+V),g=ru.bind(null,H0),w=ru.bind(null,Bn);function C(V,oe){let re,me;return Dh(V)?(re=t.getRecordMatcher(V),{}.NODE_ENV!=="production"&&!re&&$e(`Parent route "${String(V)}" not found when adding child route`,oe),me=oe):me=V,t.addRoute(me,re)}function M(V){const oe=t.getRecordMatcher(V);oe?t.removeRoute(oe):{}.NODE_ENV!=="production"&&$e(`Cannot remove non-existent route "${String(V)}"`)}function F(){return t.getRoutes().map(V=>V.record)}function se(V){return!!t.getRecordMatcher(V)}function Q(V,oe){if(oe=Je({},oe||h.value),typeof V=="string"){const b=iu(s,V,oe.path),O=t.resolve({path:b.path},oe),k=n.createHref(b.fullPath);return{}.NODE_ENV!=="production"&&(k.startsWith("//")?$e(`Location "${V}" resolved to "${k}". A resolved location cannot start with multiple slashes.`):O.matched.length||$e(`No match found for location with path "${V}"`)),Je(b,O,{params:w(O.params),hash:Bn(b.hash),redirectedFrom:void 0,href:k})}if({}.NODE_ENV!=="production"&&!Yi(V))return $e(`router.resolve() was passed an invalid location. This will fail in production.
- Location:`,V),Q({});let re;if(V.path!=null)({}).NODE_ENV!=="production"&&"params"in V&&!("name"in V)&&Object.keys(V.params).length&&$e(`Path "${V.path}" was passed with params but they will be ignored. Use a named route alongside params instead.`),re=Je({},V,{path:iu(s,V.path,oe.path).path});else{const b=Je({},V.params);for(const O in b)b[O]==null&&delete b[O];re=Je({},V,{params:g(b)}),oe.params=g(oe.params)}const me=t.resolve(re,oe),ke=V.hash||"";({}).NODE_ENV!=="production"&&ke&&!ke.startsWith("#")&&$e(`A \`hash\` should always start with the character "#". Replace "${ke}" with "#${ke}".`),me.params=p(w(me.params));const rt=z0(i,Je({},V,{hash:B0(ke),path:me.path})),Ve=n.createHref(rt);return{}.NODE_ENV!=="production"&&(Ve.startsWith("//")?$e(`Location "${V}" resolved to "${Ve}". A resolved location cannot start with multiple slashes.`):me.matched.length||$e(`No match found for location with path "${V.path!=null?V.path:V}"`)),Je({fullPath:rt,hash:ke,query:i===Fh?Dy(V.query):V.query||{}},me,{redirectedFrom:void 0,href:Ve})}function ne(V){return typeof V=="string"?iu(s,V,h.value.path):Je({},V)}function Y(V,oe){if(_!==V)return $n(8,{from:oe,to:V})}function be(V){return ve(V)}function J(V){return be(Je(ne(V),{replace:!0}))}function he(V){const oe=V.matched[V.matched.length-1];if(oe&&oe.redirect){const{redirect:re}=oe;let me=typeof re=="function"?re(V):re;if(typeof me=="string"&&(me=me.includes("?")||me.includes("#")?me=ne(me):{path:me},me.params={}),{}.NODE_ENV!=="production"&&me.path==null&&!("name"in me))throw $e(`Invalid redirect found:
${JSON.stringify(me,null,2)}
 when navigating to "${V.fullPath}". A redirect must contain a name or path. This will break in production.`),new Error("Invalid redirect");return Je({query:V.query,hash:V.hash,params:me.path!=null?{}:V.params},me)}}function ve(V,oe){const re=_=Q(V),me=h.value,ke=V.state,rt=V.force,Ve=V.replace===!0,b=he(re);if(b)return ve(Je(ne(b),{state:typeof b=="object"?Je({},ke,b.state):ke,force:rt,replace:Ve}),oe||re);const O=re;O.redirectedFrom=oe;let k;return!rt&&Ch(i,me,re)&&(k=$n(16,{to:O,from:me}),Tt(me,me,!0,!1)),(k?Promise.resolve(k):I(O,me)).catch(L=>pr(L)?pr(L,2)?L:_s(L):we(L,O,me)).then(L=>{if(L){if(pr(L,2))return{}.NODE_ENV!=="production"&&Ch(i,Q(L.to),O)&&oe&&(oe._count=oe._count?oe._count+1:1)>30?($e(`Detected a possibly infinite redirection in a navigation guard when going from "${me.fullPath}" to "${O.fullPath}". Aborting to avoid a Stack Overflow.
 Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`),Promise.reject(new Error("Infinite redirect in navigation guard"))):ve(Je({replace:Ve},ne(L.to),{state:typeof L.to=="object"?Je({},ke,L.to.state):ke,force:rt}),oe||O)}else L=le(O,me,!0,Ve,ke);return Ce(O,me,L),L})}function Ie(V,oe){const re=Y(V,oe);return re?Promise.reject(re):Promise.resolve()}function ie(V){const oe=Xs.values().next().value;return oe&&typeof oe.runWithContext=="function"?oe.runWithContext(V):V()}function I(V,oe){let re;const[me,ke,rt]=Zy(V,oe);re=du(me.reverse(),"beforeRouteLeave",V,oe);for(const b of me)b.leaveGuards.forEach(O=>{re.push(Lr(O,V,oe))});const Ve=Ie.bind(null,V,oe);return re.push(Ve),us(re).then(()=>{re=[];for(const b of a.list())re.push(Lr(b,V,oe));return re.push(Ve),us(re)}).then(()=>{re=du(ke,"beforeRouteUpdate",V,oe);for(const b of ke)b.updateGuards.forEach(O=>{re.push(Lr(O,V,oe))});return re.push(Ve),us(re)}).then(()=>{re=[];for(const b of rt)if(b.beforeEnter)if(ls(b.beforeEnter))for(const O of b.beforeEnter)re.push(Lr(O,V,oe));else re.push(Lr(b.beforeEnter,V,oe));return re.push(Ve),us(re)}).then(()=>(V.matched.forEach(b=>b.enterCallbacks={}),re=du(rt,"beforeRouteEnter",V,oe,ie),re.push(Ve),us(re))).then(()=>{re=[];for(const b of u.list())re.push(Lr(b,V,oe));return re.push(Ve),us(re)}).catch(b=>pr(b,8)?b:Promise.reject(b))}function Ce(V,oe,re){c.list().forEach(me=>ie(()=>me(V,oe,re)))}function le(V,oe,re,me,ke){const rt=Y(V,oe);if(rt)return rt;const Ve=oe===Fr,b=hr?history.state:{};re&&(me||Ve?n.replace(V.fullPath,Je({scroll:Ve&&b&&b.scroll},ke)):n.push(V.fullPath,ke)),h.value=V,Tt(V,oe,re,Ve),_s()}let ze;function mt(){ze||(ze=n.listen((V,oe,re)=>{if(!Fs.listening)return;const me=Q(V),ke=he(me);if(ke){ve(Je(ke,{replace:!0,force:!0}),me).catch(Fo);return}_=me;const rt=h.value;hr&&ey(Oh(rt.fullPath,re.delta),Zi()),I(me,rt).catch(Ve=>pr(Ve,12)?Ve:pr(Ve,2)?(ve(Je(ne(Ve.to),{force:!0}),me).then(b=>{pr(b,20)&&!re.delta&&re.type===Lo.pop&&n.go(-1,!1)}).catch(Fo),Promise.reject()):(re.delta&&n.go(-re.delta,!1),we(Ve,me,rt))).then(Ve=>{Ve=Ve||le(me,rt,!1),Ve&&(re.delta&&!pr(Ve,8)?n.go(-re.delta,!1):re.type===Lo.pop&&pr(Ve,20)&&n.go(-1,!1)),Ce(me,rt,Ve)}).catch(Fo)}))}let ue=Bo(),ot=Bo(),ce;function we(V,oe,re){_s(V);const me=ot.list();return me.length?me.forEach(ke=>ke(V,oe,re)):({}.NODE_ENV!=="production"&&$e("uncaught error during route navigation:"),console.error(V)),Promise.reject(V)}function Et(){return ce&&h.value!==Fr?Promise.resolve():new Promise((V,oe)=>{ue.add([V,oe])})}function _s(V){return ce||(ce=!V,mt(),ue.list().forEach(([oe,re])=>V?re(V):oe()),ue.reset()),V}function Tt(V,oe,re,me){const{scrollBehavior:ke}=e;if(!hr||!ke)return Promise.resolve();const rt=!re&&ty(Oh(V.fullPath,0))||(me||!re)&&history.state&&history.state.scroll||null;return gl().then(()=>ke(V,oe,rt)).then(Ve=>Ve&&X0(Ve)).catch(Ve=>we(Ve,V,oe))}const vs=V=>n.go(V);let ts;const Xs=new Set,Fs={currentRoute:h,listening:!0,addRoute:C,removeRoute:M,clearRoutes:t.clearRoutes,hasRoute:se,getRoutes:F,resolve:Q,options:e,push:be,replace:J,go:vs,back:()=>vs(-1),forward:()=>vs(1),beforeEach:a.add,beforeResolve:u.add,afterEach:c.add,onError:ot.add,isReady:Et,install(V){const oe=this;V.component("RouterLink",Ay),V.component("RouterView",Vy),V.config.globalProperties.$router=oe,Object.defineProperty(V.config.globalProperties,"$route",{enumerable:!0,get:()=>Tr(h)}),hr&&!ts&&h.value===Fr&&(ts=!0,be(n.location).catch(ke=>{({}).NODE_ENV!=="production"&&$e("Unexpected error when starting the router:",ke)}));const re={};for(const ke in Fr)Object.defineProperty(re,ke,{get:()=>h.value[ke],enumerable:!0});V.provide(Ji,oe),V.provide(Uh,od(re)),V.provide(cu,h);const me=V.unmount;Xs.add(V),V.unmount=function(){Xs.delete(V),Xs.size<1&&(_=Fr,ze&&ze(),ze=null,h.value=Fr,ts=!1,ce=!1),me()},{}.NODE_ENV!=="production"&&hr&&Ly(V,oe,t)}};function us(V){return V.reduce((oe,re)=>oe.then(()=>ie(re)),Promise.resolve())}return Fs}function Zy(e,t){const s=[],i=[],n=[],a=Math.max(t.matched.length,e.matched.length);for(let u=0;u<a;u++){const c=t.matched[u];c&&(e.matched.find(_=>Rr(_,c))?i.push(c):s.push(c));const h=e.matched[u];h&&(t.matched.find(_=>Rr(_,h))||n.push(h))}return[s,i,n]}function Jh(){return Ks(Ji)}var qo=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Xi={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */Xi.exports,function(e,t){(function(){var s,i="4.17.21",n=200,a="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",u="Expected a function",c="Invalid `variable` option passed into `_.template`",h="__lodash_hash_undefined__",_=500,p="__lodash_placeholder__",g=1,w=2,C=4,M=1,F=2,se=1,Q=2,ne=4,Y=8,be=16,J=32,he=64,ve=128,Ie=256,ie=512,I=30,Ce="...",le=800,ze=16,mt=1,ue=2,ot=3,ce=1/0,we=9007199254740991,Et=17976931348623157e292,_s=0/0,Tt=**********,vs=Tt-1,ts=Tt>>>1,Xs=[["ary",ve],["bind",se],["bindKey",Q],["curry",Y],["curryRight",be],["flip",ie],["partial",J],["partialRight",he],["rearg",Ie]],Fs="[object Arguments]",us="[object Array]",V="[object AsyncFunction]",oe="[object Boolean]",re="[object Date]",me="[object DOMException]",ke="[object Error]",rt="[object Function]",Ve="[object GeneratorFunction]",b="[object Map]",O="[object Number]",k="[object Null]",L="[object Object]",$="[object Promise]",H="[object Proxy]",ee="[object RegExp]",G="[object Set]",X="[object String]",j="[object Symbol]",ye="[object Undefined]",te="[object WeakMap]",ge="[object WeakSet]",Ee="[object ArrayBuffer]",Fe="[object DataView]",Qe="[object Float32Array]",Ze="[object Float64Array]",Vt="[object Int8Array]",Ot="[object Int16Array]",Yt="[object Int32Array]",Lt="[object Uint8Array]",gr="[object Uint8ClampedArray]",Wn="[object Uint16Array]",Nt="[object Uint32Array]",ys=/\b__p \+= '';/g,aa=/\b(__p \+=) '' \+/g,h3=/(__e\(.*?\)|\b__t\)) \+\n'';/g,ip=/&(?:amp|lt|gt|quot|#39);/g,ap=/[&<>"']/g,p3=RegExp(ip.source),m3=RegExp(ap.source),g3=/<%-([\s\S]+?)%>/g,_3=/<%([\s\S]+?)%>/g,lp=/<%=([\s\S]+?)%>/g,v3=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,y3=/^\w*$/,b3=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,bu=/[\\^$.*+?()[\]{}|]/g,C3=RegExp(bu.source),Cu=/^\s+/,w3=/\s/,E3=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,O3=/\{\n\/\* \[wrapped with (.+)\] \*/,x3=/,? & /,S3=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,D3=/[()=,{}\[\]\/\s]/,T3=/\\(\\)?/g,N3=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,up=/\w*$/,I3=/^[-+]0x[0-9a-f]+$/i,A3=/^0b[01]+$/i,M3=/^\[object .+?Constructor\]$/,P3=/^0o[0-7]+$/i,k3=/^(?:0|[1-9]\d*)$/,V3=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,la=/($^)/,R3=/['\n\r\u2028\u2029\\]/g,ua="\\ud800-\\udfff",F3="\\u0300-\\u036f",L3="\\ufe20-\\ufe2f",U3="\\u20d0-\\u20ff",cp=F3+L3+U3,dp="\\u2700-\\u27bf",fp="a-z\\xdf-\\xf6\\xf8-\\xff",B3="\\xac\\xb1\\xd7\\xf7",$3="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",q3="\\u2000-\\u206f",H3=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",hp="A-Z\\xc0-\\xd6\\xd8-\\xde",pp="\\ufe0e\\ufe0f",mp=B3+$3+q3+H3,wu="['’]",W3="["+ua+"]",gp="["+mp+"]",ca="["+cp+"]",_p="\\d+",j3="["+dp+"]",vp="["+fp+"]",yp="[^"+ua+mp+_p+dp+fp+hp+"]",Eu="\\ud83c[\\udffb-\\udfff]",z3="(?:"+ca+"|"+Eu+")",bp="[^"+ua+"]",Ou="(?:\\ud83c[\\udde6-\\uddff]){2}",xu="[\\ud800-\\udbff][\\udc00-\\udfff]",jn="["+hp+"]",Cp="\\u200d",wp="(?:"+vp+"|"+yp+")",G3="(?:"+jn+"|"+yp+")",Ep="(?:"+wu+"(?:d|ll|m|re|s|t|ve))?",Op="(?:"+wu+"(?:D|LL|M|RE|S|T|VE))?",xp=z3+"?",Sp="["+pp+"]?",K3="(?:"+Cp+"(?:"+[bp,Ou,xu].join("|")+")"+Sp+xp+")*",Z3="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Y3="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Dp=Sp+xp+K3,J3="(?:"+[j3,Ou,xu].join("|")+")"+Dp,Q3="(?:"+[bp+ca+"?",ca,Ou,xu,W3].join("|")+")",X3=RegExp(wu,"g"),eN=RegExp(ca,"g"),Su=RegExp(Eu+"(?="+Eu+")|"+Q3+Dp,"g"),tN=RegExp([jn+"?"+vp+"+"+Ep+"(?="+[gp,jn,"$"].join("|")+")",G3+"+"+Op+"(?="+[gp,jn+wp,"$"].join("|")+")",jn+"?"+wp+"+"+Ep,jn+"+"+Op,Y3,Z3,_p,J3].join("|"),"g"),sN=RegExp("["+Cp+ua+cp+pp+"]"),rN=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,nN=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],oN=-1,ft={};ft[Qe]=ft[Ze]=ft[Vt]=ft[Ot]=ft[Yt]=ft[Lt]=ft[gr]=ft[Wn]=ft[Nt]=!0,ft[Fs]=ft[us]=ft[Ee]=ft[oe]=ft[Fe]=ft[re]=ft[ke]=ft[rt]=ft[b]=ft[O]=ft[L]=ft[ee]=ft[G]=ft[X]=ft[te]=!1;var ut={};ut[Fs]=ut[us]=ut[Ee]=ut[Fe]=ut[oe]=ut[re]=ut[Qe]=ut[Ze]=ut[Vt]=ut[Ot]=ut[Yt]=ut[b]=ut[O]=ut[L]=ut[ee]=ut[G]=ut[X]=ut[j]=ut[Lt]=ut[gr]=ut[Wn]=ut[Nt]=!0,ut[ke]=ut[rt]=ut[te]=!1;var iN={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},aN={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},lN={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},uN={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},cN=parseFloat,dN=parseInt,Tp=typeof qo=="object"&&qo&&qo.Object===Object&&qo,fN=typeof self=="object"&&self&&self.Object===Object&&self,Ut=Tp||fN||Function("return this")(),Du=t&&!t.nodeType&&t,vn=Du&&!0&&e&&!e.nodeType&&e,Np=vn&&vn.exports===Du,Tu=Np&&Tp.process,bs=function(){try{var T=vn&&vn.require&&vn.require("util").types;return T||Tu&&Tu.binding&&Tu.binding("util")}catch{}}(),Ip=bs&&bs.isArrayBuffer,Ap=bs&&bs.isDate,Mp=bs&&bs.isMap,Pp=bs&&bs.isRegExp,kp=bs&&bs.isSet,Vp=bs&&bs.isTypedArray;function cs(T,R,P){switch(P.length){case 0:return T.call(R);case 1:return T.call(R,P[0]);case 2:return T.call(R,P[0],P[1]);case 3:return T.call(R,P[0],P[1],P[2])}return T.apply(R,P)}function hN(T,R,P,fe){for(var Me=-1,Xe=T==null?0:T.length;++Me<Xe;){var It=T[Me];R(fe,It,P(It),T)}return fe}function Cs(T,R){for(var P=-1,fe=T==null?0:T.length;++P<fe&&R(T[P],P,T)!==!1;);return T}function pN(T,R){for(var P=T==null?0:T.length;P--&&R(T[P],P,T)!==!1;);return T}function Rp(T,R){for(var P=-1,fe=T==null?0:T.length;++P<fe;)if(!R(T[P],P,T))return!1;return!0}function $r(T,R){for(var P=-1,fe=T==null?0:T.length,Me=0,Xe=[];++P<fe;){var It=T[P];R(It,P,T)&&(Xe[Me++]=It)}return Xe}function da(T,R){var P=T==null?0:T.length;return!!P&&zn(T,R,0)>-1}function Nu(T,R,P){for(var fe=-1,Me=T==null?0:T.length;++fe<Me;)if(P(R,T[fe]))return!0;return!1}function pt(T,R){for(var P=-1,fe=T==null?0:T.length,Me=Array(fe);++P<fe;)Me[P]=R(T[P],P,T);return Me}function qr(T,R){for(var P=-1,fe=R.length,Me=T.length;++P<fe;)T[Me+P]=R[P];return T}function Iu(T,R,P,fe){var Me=-1,Xe=T==null?0:T.length;for(fe&&Xe&&(P=T[++Me]);++Me<Xe;)P=R(P,T[Me],Me,T);return P}function mN(T,R,P,fe){var Me=T==null?0:T.length;for(fe&&Me&&(P=T[--Me]);Me--;)P=R(P,T[Me],Me,T);return P}function Au(T,R){for(var P=-1,fe=T==null?0:T.length;++P<fe;)if(R(T[P],P,T))return!0;return!1}var gN=Mu("length");function _N(T){return T.split("")}function vN(T){return T.match(S3)||[]}function Fp(T,R,P){var fe;return P(T,function(Me,Xe,It){if(R(Me,Xe,It))return fe=Xe,!1}),fe}function fa(T,R,P,fe){for(var Me=T.length,Xe=P+(fe?1:-1);fe?Xe--:++Xe<Me;)if(R(T[Xe],Xe,T))return Xe;return-1}function zn(T,R,P){return R===R?IN(T,R,P):fa(T,Lp,P)}function yN(T,R,P,fe){for(var Me=P-1,Xe=T.length;++Me<Xe;)if(fe(T[Me],R))return Me;return-1}function Lp(T){return T!==T}function Up(T,R){var P=T==null?0:T.length;return P?ku(T,R)/P:_s}function Mu(T){return function(R){return R==null?s:R[T]}}function Pu(T){return function(R){return T==null?s:T[R]}}function Bp(T,R,P,fe,Me){return Me(T,function(Xe,It,at){P=fe?(fe=!1,Xe):R(P,Xe,It,at)}),P}function bN(T,R){var P=T.length;for(T.sort(R);P--;)T[P]=T[P].value;return T}function ku(T,R){for(var P,fe=-1,Me=T.length;++fe<Me;){var Xe=R(T[fe]);Xe!==s&&(P=P===s?Xe:P+Xe)}return P}function Vu(T,R){for(var P=-1,fe=Array(T);++P<T;)fe[P]=R(P);return fe}function CN(T,R){return pt(R,function(P){return[P,T[P]]})}function $p(T){return T&&T.slice(0,jp(T)+1).replace(Cu,"")}function ds(T){return function(R){return T(R)}}function Ru(T,R){return pt(R,function(P){return T[P]})}function zo(T,R){return T.has(R)}function qp(T,R){for(var P=-1,fe=T.length;++P<fe&&zn(R,T[P],0)>-1;);return P}function Hp(T,R){for(var P=T.length;P--&&zn(R,T[P],0)>-1;);return P}function wN(T,R){for(var P=T.length,fe=0;P--;)T[P]===R&&++fe;return fe}var EN=Pu(iN),ON=Pu(aN);function xN(T){return"\\"+uN[T]}function SN(T,R){return T==null?s:T[R]}function Gn(T){return sN.test(T)}function DN(T){return rN.test(T)}function TN(T){for(var R,P=[];!(R=T.next()).done;)P.push(R.value);return P}function Fu(T){var R=-1,P=Array(T.size);return T.forEach(function(fe,Me){P[++R]=[Me,fe]}),P}function Wp(T,R){return function(P){return T(R(P))}}function Hr(T,R){for(var P=-1,fe=T.length,Me=0,Xe=[];++P<fe;){var It=T[P];(It===R||It===p)&&(T[P]=p,Xe[Me++]=P)}return Xe}function ha(T){var R=-1,P=Array(T.size);return T.forEach(function(fe){P[++R]=fe}),P}function NN(T){var R=-1,P=Array(T.size);return T.forEach(function(fe){P[++R]=[fe,fe]}),P}function IN(T,R,P){for(var fe=P-1,Me=T.length;++fe<Me;)if(T[fe]===R)return fe;return-1}function AN(T,R,P){for(var fe=P+1;fe--;)if(T[fe]===R)return fe;return fe}function Kn(T){return Gn(T)?PN(T):gN(T)}function Ls(T){return Gn(T)?kN(T):_N(T)}function jp(T){for(var R=T.length;R--&&w3.test(T.charAt(R)););return R}var MN=Pu(lN);function PN(T){for(var R=Su.lastIndex=0;Su.test(T);)++R;return R}function kN(T){return T.match(Su)||[]}function VN(T){return T.match(tN)||[]}var RN=function T(R){R=R==null?Ut:Zn.defaults(Ut.Object(),R,Zn.pick(Ut,nN));var P=R.Array,fe=R.Date,Me=R.Error,Xe=R.Function,It=R.Math,at=R.Object,Lu=R.RegExp,FN=R.String,ws=R.TypeError,pa=P.prototype,LN=Xe.prototype,Yn=at.prototype,ma=R["__core-js_shared__"],ga=LN.toString,nt=Yn.hasOwnProperty,UN=0,zp=function(){var r=/[^.]+$/.exec(ma&&ma.keys&&ma.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}(),_a=Yn.toString,BN=ga.call(at),$N=Ut._,qN=Lu("^"+ga.call(nt).replace(bu,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),va=Np?R.Buffer:s,Wr=R.Symbol,ya=R.Uint8Array,Gp=va?va.allocUnsafe:s,ba=Wp(at.getPrototypeOf,at),Kp=at.create,Zp=Yn.propertyIsEnumerable,Ca=pa.splice,Yp=Wr?Wr.isConcatSpreadable:s,Go=Wr?Wr.iterator:s,yn=Wr?Wr.toStringTag:s,wa=function(){try{var r=On(at,"defineProperty");return r({},"",{}),r}catch{}}(),HN=R.clearTimeout!==Ut.clearTimeout&&R.clearTimeout,WN=fe&&fe.now!==Ut.Date.now&&fe.now,jN=R.setTimeout!==Ut.setTimeout&&R.setTimeout,Ea=It.ceil,Oa=It.floor,Uu=at.getOwnPropertySymbols,zN=va?va.isBuffer:s,Jp=R.isFinite,GN=pa.join,KN=Wp(at.keys,at),At=It.max,qt=It.min,ZN=fe.now,YN=R.parseInt,Qp=It.random,JN=pa.reverse,Bu=On(R,"DataView"),Ko=On(R,"Map"),$u=On(R,"Promise"),Jn=On(R,"Set"),Zo=On(R,"WeakMap"),Yo=On(at,"create"),xa=Zo&&new Zo,Qn={},QN=xn(Bu),XN=xn(Ko),eI=xn($u),tI=xn(Jn),sI=xn(Zo),Sa=Wr?Wr.prototype:s,Jo=Sa?Sa.valueOf:s,Xp=Sa?Sa.toString:s;function v(r){if(_t(r)&&!Re(r)&&!(r instanceof je)){if(r instanceof Es)return r;if(nt.call(r,"__wrapped__"))return eg(r)}return new Es(r)}var Xn=function(){function r(){}return function(o){if(!gt(o))return{};if(Kp)return Kp(o);r.prototype=o;var l=new r;return r.prototype=s,l}}();function Da(){}function Es(r,o){this.__wrapped__=r,this.__actions__=[],this.__chain__=!!o,this.__index__=0,this.__values__=s}v.templateSettings={escape:g3,evaluate:_3,interpolate:lp,variable:"",imports:{_:v}},v.prototype=Da.prototype,v.prototype.constructor=v,Es.prototype=Xn(Da.prototype),Es.prototype.constructor=Es;function je(r){this.__wrapped__=r,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=Tt,this.__views__=[]}function rI(){var r=new je(this.__wrapped__);return r.__actions__=ss(this.__actions__),r.__dir__=this.__dir__,r.__filtered__=this.__filtered__,r.__iteratees__=ss(this.__iteratees__),r.__takeCount__=this.__takeCount__,r.__views__=ss(this.__views__),r}function nI(){if(this.__filtered__){var r=new je(this);r.__dir__=-1,r.__filtered__=!0}else r=this.clone(),r.__dir__*=-1;return r}function oI(){var r=this.__wrapped__.value(),o=this.__dir__,l=Re(r),d=o<0,m=l?r.length:0,y=_5(0,m,this.__views__),E=y.start,S=y.end,N=S-E,U=d?S:E-1,B=this.__iteratees__,W=B.length,ae=0,_e=qt(N,this.__takeCount__);if(!l||!d&&m==N&&_e==N)return Em(r,this.__actions__);var Se=[];e:for(;N--&&ae<_e;){U+=o;for(var Be=-1,De=r[U];++Be<W;){var He=B[Be],Ge=He.iteratee,ps=He.type,Xt=Ge(De);if(ps==ue)De=Xt;else if(!Xt){if(ps==mt)continue e;break e}}Se[ae++]=De}return Se}je.prototype=Xn(Da.prototype),je.prototype.constructor=je;function bn(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var d=r[o];this.set(d[0],d[1])}}function iI(){this.__data__=Yo?Yo(null):{},this.size=0}function aI(r){var o=this.has(r)&&delete this.__data__[r];return this.size-=o?1:0,o}function lI(r){var o=this.__data__;if(Yo){var l=o[r];return l===h?s:l}return nt.call(o,r)?o[r]:s}function uI(r){var o=this.__data__;return Yo?o[r]!==s:nt.call(o,r)}function cI(r,o){var l=this.__data__;return this.size+=this.has(r)?0:1,l[r]=Yo&&o===s?h:o,this}bn.prototype.clear=iI,bn.prototype.delete=aI,bn.prototype.get=lI,bn.prototype.has=uI,bn.prototype.set=cI;function _r(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var d=r[o];this.set(d[0],d[1])}}function dI(){this.__data__=[],this.size=0}function fI(r){var o=this.__data__,l=Ta(o,r);if(l<0)return!1;var d=o.length-1;return l==d?o.pop():Ca.call(o,l,1),--this.size,!0}function hI(r){var o=this.__data__,l=Ta(o,r);return l<0?s:o[l][1]}function pI(r){return Ta(this.__data__,r)>-1}function mI(r,o){var l=this.__data__,d=Ta(l,r);return d<0?(++this.size,l.push([r,o])):l[d][1]=o,this}_r.prototype.clear=dI,_r.prototype.delete=fI,_r.prototype.get=hI,_r.prototype.has=pI,_r.prototype.set=mI;function vr(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var d=r[o];this.set(d[0],d[1])}}function gI(){this.size=0,this.__data__={hash:new bn,map:new(Ko||_r),string:new bn}}function _I(r){var o=Ba(this,r).delete(r);return this.size-=o?1:0,o}function vI(r){return Ba(this,r).get(r)}function yI(r){return Ba(this,r).has(r)}function bI(r,o){var l=Ba(this,r),d=l.size;return l.set(r,o),this.size+=l.size==d?0:1,this}vr.prototype.clear=gI,vr.prototype.delete=_I,vr.prototype.get=vI,vr.prototype.has=yI,vr.prototype.set=bI;function Cn(r){var o=-1,l=r==null?0:r.length;for(this.__data__=new vr;++o<l;)this.add(r[o])}function CI(r){return this.__data__.set(r,h),this}function wI(r){return this.__data__.has(r)}Cn.prototype.add=Cn.prototype.push=CI,Cn.prototype.has=wI;function Us(r){var o=this.__data__=new _r(r);this.size=o.size}function EI(){this.__data__=new _r,this.size=0}function OI(r){var o=this.__data__,l=o.delete(r);return this.size=o.size,l}function xI(r){return this.__data__.get(r)}function SI(r){return this.__data__.has(r)}function DI(r,o){var l=this.__data__;if(l instanceof _r){var d=l.__data__;if(!Ko||d.length<n-1)return d.push([r,o]),this.size=++l.size,this;l=this.__data__=new vr(d)}return l.set(r,o),this.size=l.size,this}Us.prototype.clear=EI,Us.prototype.delete=OI,Us.prototype.get=xI,Us.prototype.has=SI,Us.prototype.set=DI;function em(r,o){var l=Re(r),d=!l&&Sn(r),m=!l&&!d&&Zr(r),y=!l&&!d&&!m&&ro(r),E=l||d||m||y,S=E?Vu(r.length,FN):[],N=S.length;for(var U in r)(o||nt.call(r,U))&&!(E&&(U=="length"||m&&(U=="offset"||U=="parent")||y&&(U=="buffer"||U=="byteLength"||U=="byteOffset")||wr(U,N)))&&S.push(U);return S}function tm(r){var o=r.length;return o?r[Qu(0,o-1)]:s}function TI(r,o){return $a(ss(r),wn(o,0,r.length))}function NI(r){return $a(ss(r))}function qu(r,o,l){(l!==s&&!Bs(r[o],l)||l===s&&!(o in r))&&yr(r,o,l)}function Qo(r,o,l){var d=r[o];(!(nt.call(r,o)&&Bs(d,l))||l===s&&!(o in r))&&yr(r,o,l)}function Ta(r,o){for(var l=r.length;l--;)if(Bs(r[l][0],o))return l;return-1}function II(r,o,l,d){return jr(r,function(m,y,E){o(d,m,l(m),E)}),d}function sm(r,o){return r&&tr(o,Rt(o),r)}function AI(r,o){return r&&tr(o,ns(o),r)}function yr(r,o,l){o=="__proto__"&&wa?wa(r,o,{configurable:!0,enumerable:!0,value:l,writable:!0}):r[o]=l}function Hu(r,o){for(var l=-1,d=o.length,m=P(d),y=r==null;++l<d;)m[l]=y?s:Ec(r,o[l]);return m}function wn(r,o,l){return r===r&&(l!==s&&(r=r<=l?r:l),o!==s&&(r=r>=o?r:o)),r}function Os(r,o,l,d,m,y){var E,S=o&g,N=o&w,U=o&C;if(l&&(E=m?l(r,d,m,y):l(r)),E!==s)return E;if(!gt(r))return r;var B=Re(r);if(B){if(E=y5(r),!S)return ss(r,E)}else{var W=Ht(r),ae=W==rt||W==Ve;if(Zr(r))return Sm(r,S);if(W==L||W==Fs||ae&&!m){if(E=N||ae?{}:jm(r),!S)return N?l5(r,AI(E,r)):a5(r,sm(E,r))}else{if(!ut[W])return m?r:{};E=b5(r,W,S)}}y||(y=new Us);var _e=y.get(r);if(_e)return _e;y.set(r,E),bg(r)?r.forEach(function(De){E.add(Os(De,o,l,De,r,y))}):vg(r)&&r.forEach(function(De,He){E.set(He,Os(De,o,l,He,r,y))});var Se=U?N?uc:lc:N?ns:Rt,Be=B?s:Se(r);return Cs(Be||r,function(De,He){Be&&(He=De,De=r[He]),Qo(E,He,Os(De,o,l,He,r,y))}),E}function MI(r){var o=Rt(r);return function(l){return rm(l,r,o)}}function rm(r,o,l){var d=l.length;if(r==null)return!d;for(r=at(r);d--;){var m=l[d],y=o[m],E=r[m];if(E===s&&!(m in r)||!y(E))return!1}return!0}function nm(r,o,l){if(typeof r!="function")throw new ws(u);return oi(function(){r.apply(s,l)},o)}function Xo(r,o,l,d){var m=-1,y=da,E=!0,S=r.length,N=[],U=o.length;if(!S)return N;l&&(o=pt(o,ds(l))),d?(y=Nu,E=!1):o.length>=n&&(y=zo,E=!1,o=new Cn(o));e:for(;++m<S;){var B=r[m],W=l==null?B:l(B);if(B=d||B!==0?B:0,E&&W===W){for(var ae=U;ae--;)if(o[ae]===W)continue e;N.push(B)}else y(o,W,d)||N.push(B)}return N}var jr=Am(er),om=Am(ju,!0);function PI(r,o){var l=!0;return jr(r,function(d,m,y){return l=!!o(d,m,y),l}),l}function Na(r,o,l){for(var d=-1,m=r.length;++d<m;){var y=r[d],E=o(y);if(E!=null&&(S===s?E===E&&!hs(E):l(E,S)))var S=E,N=y}return N}function kI(r,o,l,d){var m=r.length;for(l=Le(l),l<0&&(l=-l>m?0:m+l),d=d===s||d>m?m:Le(d),d<0&&(d+=m),d=l>d?0:wg(d);l<d;)r[l++]=o;return r}function im(r,o){var l=[];return jr(r,function(d,m,y){o(d,m,y)&&l.push(d)}),l}function Bt(r,o,l,d,m){var y=-1,E=r.length;for(l||(l=w5),m||(m=[]);++y<E;){var S=r[y];o>0&&l(S)?o>1?Bt(S,o-1,l,d,m):qr(m,S):d||(m[m.length]=S)}return m}var Wu=Mm(),am=Mm(!0);function er(r,o){return r&&Wu(r,o,Rt)}function ju(r,o){return r&&am(r,o,Rt)}function Ia(r,o){return $r(o,function(l){return Er(r[l])})}function En(r,o){o=Gr(o,r);for(var l=0,d=o.length;r!=null&&l<d;)r=r[sr(o[l++])];return l&&l==d?r:s}function lm(r,o,l){var d=o(r);return Re(r)?d:qr(d,l(r))}function Jt(r){return r==null?r===s?ye:k:yn&&yn in at(r)?g5(r):N5(r)}function zu(r,o){return r>o}function VI(r,o){return r!=null&&nt.call(r,o)}function RI(r,o){return r!=null&&o in at(r)}function FI(r,o,l){return r>=qt(o,l)&&r<At(o,l)}function Gu(r,o,l){for(var d=l?Nu:da,m=r[0].length,y=r.length,E=y,S=P(y),N=1/0,U=[];E--;){var B=r[E];E&&o&&(B=pt(B,ds(o))),N=qt(B.length,N),S[E]=!l&&(o||m>=120&&B.length>=120)?new Cn(E&&B):s}B=r[0];var W=-1,ae=S[0];e:for(;++W<m&&U.length<N;){var _e=B[W],Se=o?o(_e):_e;if(_e=l||_e!==0?_e:0,!(ae?zo(ae,Se):d(U,Se,l))){for(E=y;--E;){var Be=S[E];if(!(Be?zo(Be,Se):d(r[E],Se,l)))continue e}ae&&ae.push(Se),U.push(_e)}}return U}function LI(r,o,l,d){return er(r,function(m,y,E){o(d,l(m),y,E)}),d}function ei(r,o,l){o=Gr(o,r),r=Zm(r,o);var d=r==null?r:r[sr(Ss(o))];return d==null?s:cs(d,r,l)}function um(r){return _t(r)&&Jt(r)==Fs}function UI(r){return _t(r)&&Jt(r)==Ee}function BI(r){return _t(r)&&Jt(r)==re}function ti(r,o,l,d,m){return r===o?!0:r==null||o==null||!_t(r)&&!_t(o)?r!==r&&o!==o:$I(r,o,l,d,ti,m)}function $I(r,o,l,d,m,y){var E=Re(r),S=Re(o),N=E?us:Ht(r),U=S?us:Ht(o);N=N==Fs?L:N,U=U==Fs?L:U;var B=N==L,W=U==L,ae=N==U;if(ae&&Zr(r)){if(!Zr(o))return!1;E=!0,B=!1}if(ae&&!B)return y||(y=new Us),E||ro(r)?qm(r,o,l,d,m,y):p5(r,o,N,l,d,m,y);if(!(l&M)){var _e=B&&nt.call(r,"__wrapped__"),Se=W&&nt.call(o,"__wrapped__");if(_e||Se){var Be=_e?r.value():r,De=Se?o.value():o;return y||(y=new Us),m(Be,De,l,d,y)}}return ae?(y||(y=new Us),m5(r,o,l,d,m,y)):!1}function qI(r){return _t(r)&&Ht(r)==b}function Ku(r,o,l,d){var m=l.length,y=m,E=!d;if(r==null)return!y;for(r=at(r);m--;){var S=l[m];if(E&&S[2]?S[1]!==r[S[0]]:!(S[0]in r))return!1}for(;++m<y;){S=l[m];var N=S[0],U=r[N],B=S[1];if(E&&S[2]){if(U===s&&!(N in r))return!1}else{var W=new Us;if(d)var ae=d(U,B,N,r,o,W);if(!(ae===s?ti(B,U,M|F,d,W):ae))return!1}}return!0}function cm(r){if(!gt(r)||O5(r))return!1;var o=Er(r)?qN:M3;return o.test(xn(r))}function HI(r){return _t(r)&&Jt(r)==ee}function WI(r){return _t(r)&&Ht(r)==G}function jI(r){return _t(r)&&Ga(r.length)&&!!ft[Jt(r)]}function dm(r){return typeof r=="function"?r:r==null?os:typeof r=="object"?Re(r)?pm(r[0],r[1]):hm(r):Pg(r)}function Zu(r){if(!ni(r))return KN(r);var o=[];for(var l in at(r))nt.call(r,l)&&l!="constructor"&&o.push(l);return o}function zI(r){if(!gt(r))return T5(r);var o=ni(r),l=[];for(var d in r)d=="constructor"&&(o||!nt.call(r,d))||l.push(d);return l}function Yu(r,o){return r<o}function fm(r,o){var l=-1,d=rs(r)?P(r.length):[];return jr(r,function(m,y,E){d[++l]=o(m,y,E)}),d}function hm(r){var o=dc(r);return o.length==1&&o[0][2]?Gm(o[0][0],o[0][1]):function(l){return l===r||Ku(l,r,o)}}function pm(r,o){return hc(r)&&zm(o)?Gm(sr(r),o):function(l){var d=Ec(l,r);return d===s&&d===o?Oc(l,r):ti(o,d,M|F)}}function Aa(r,o,l,d,m){r!==o&&Wu(o,function(y,E){if(m||(m=new Us),gt(y))GI(r,o,E,l,Aa,d,m);else{var S=d?d(mc(r,E),y,E+"",r,o,m):s;S===s&&(S=y),qu(r,E,S)}},ns)}function GI(r,o,l,d,m,y,E){var S=mc(r,l),N=mc(o,l),U=E.get(N);if(U){qu(r,l,U);return}var B=y?y(S,N,l+"",r,o,E):s,W=B===s;if(W){var ae=Re(N),_e=!ae&&Zr(N),Se=!ae&&!_e&&ro(N);B=N,ae||_e||Se?Re(S)?B=S:bt(S)?B=ss(S):_e?(W=!1,B=Sm(N,!0)):Se?(W=!1,B=Dm(N,!0)):B=[]:ii(N)||Sn(N)?(B=S,Sn(S)?B=Eg(S):(!gt(S)||Er(S))&&(B=jm(N))):W=!1}W&&(E.set(N,B),m(B,N,d,y,E),E.delete(N)),qu(r,l,B)}function mm(r,o){var l=r.length;if(l)return o+=o<0?l:0,wr(o,l)?r[o]:s}function gm(r,o,l){o.length?o=pt(o,function(y){return Re(y)?function(E){return En(E,y.length===1?y[0]:y)}:y}):o=[os];var d=-1;o=pt(o,ds(Oe()));var m=fm(r,function(y,E,S){var N=pt(o,function(U){return U(y)});return{criteria:N,index:++d,value:y}});return bN(m,function(y,E){return i5(y,E,l)})}function KI(r,o){return _m(r,o,function(l,d){return Oc(r,d)})}function _m(r,o,l){for(var d=-1,m=o.length,y={};++d<m;){var E=o[d],S=En(r,E);l(S,E)&&si(y,Gr(E,r),S)}return y}function ZI(r){return function(o){return En(o,r)}}function Ju(r,o,l,d){var m=d?yN:zn,y=-1,E=o.length,S=r;for(r===o&&(o=ss(o)),l&&(S=pt(r,ds(l)));++y<E;)for(var N=0,U=o[y],B=l?l(U):U;(N=m(S,B,N,d))>-1;)S!==r&&Ca.call(S,N,1),Ca.call(r,N,1);return r}function vm(r,o){for(var l=r?o.length:0,d=l-1;l--;){var m=o[l];if(l==d||m!==y){var y=m;wr(m)?Ca.call(r,m,1):tc(r,m)}}return r}function Qu(r,o){return r+Oa(Qp()*(o-r+1))}function YI(r,o,l,d){for(var m=-1,y=At(Ea((o-r)/(l||1)),0),E=P(y);y--;)E[d?y:++m]=r,r+=l;return E}function Xu(r,o){var l="";if(!r||o<1||o>we)return l;do o%2&&(l+=r),o=Oa(o/2),o&&(r+=r);while(o);return l}function qe(r,o){return gc(Km(r,o,os),r+"")}function JI(r){return tm(no(r))}function QI(r,o){var l=no(r);return $a(l,wn(o,0,l.length))}function si(r,o,l,d){if(!gt(r))return r;o=Gr(o,r);for(var m=-1,y=o.length,E=y-1,S=r;S!=null&&++m<y;){var N=sr(o[m]),U=l;if(N==="__proto__"||N==="constructor"||N==="prototype")return r;if(m!=E){var B=S[N];U=d?d(B,N,S):s,U===s&&(U=gt(B)?B:wr(o[m+1])?[]:{})}Qo(S,N,U),S=S[N]}return r}var ym=xa?function(r,o){return xa.set(r,o),r}:os,XI=wa?function(r,o){return wa(r,"toString",{configurable:!0,enumerable:!1,value:Sc(o),writable:!0})}:os;function e5(r){return $a(no(r))}function xs(r,o,l){var d=-1,m=r.length;o<0&&(o=-o>m?0:m+o),l=l>m?m:l,l<0&&(l+=m),m=o>l?0:l-o>>>0,o>>>=0;for(var y=P(m);++d<m;)y[d]=r[d+o];return y}function t5(r,o){var l;return jr(r,function(d,m,y){return l=o(d,m,y),!l}),!!l}function Ma(r,o,l){var d=0,m=r==null?d:r.length;if(typeof o=="number"&&o===o&&m<=ts){for(;d<m;){var y=d+m>>>1,E=r[y];E!==null&&!hs(E)&&(l?E<=o:E<o)?d=y+1:m=y}return m}return ec(r,o,os,l)}function ec(r,o,l,d){var m=0,y=r==null?0:r.length;if(y===0)return 0;o=l(o);for(var E=o!==o,S=o===null,N=hs(o),U=o===s;m<y;){var B=Oa((m+y)/2),W=l(r[B]),ae=W!==s,_e=W===null,Se=W===W,Be=hs(W);if(E)var De=d||Se;else U?De=Se&&(d||ae):S?De=Se&&ae&&(d||!_e):N?De=Se&&ae&&!_e&&(d||!Be):_e||Be?De=!1:De=d?W<=o:W<o;De?m=B+1:y=B}return qt(y,vs)}function bm(r,o){for(var l=-1,d=r.length,m=0,y=[];++l<d;){var E=r[l],S=o?o(E):E;if(!l||!Bs(S,N)){var N=S;y[m++]=E===0?0:E}}return y}function Cm(r){return typeof r=="number"?r:hs(r)?_s:+r}function fs(r){if(typeof r=="string")return r;if(Re(r))return pt(r,fs)+"";if(hs(r))return Xp?Xp.call(r):"";var o=r+"";return o=="0"&&1/r==-ce?"-0":o}function zr(r,o,l){var d=-1,m=da,y=r.length,E=!0,S=[],N=S;if(l)E=!1,m=Nu;else if(y>=n){var U=o?null:f5(r);if(U)return ha(U);E=!1,m=zo,N=new Cn}else N=o?[]:S;e:for(;++d<y;){var B=r[d],W=o?o(B):B;if(B=l||B!==0?B:0,E&&W===W){for(var ae=N.length;ae--;)if(N[ae]===W)continue e;o&&N.push(W),S.push(B)}else m(N,W,l)||(N!==S&&N.push(W),S.push(B))}return S}function tc(r,o){return o=Gr(o,r),r=Zm(r,o),r==null||delete r[sr(Ss(o))]}function wm(r,o,l,d){return si(r,o,l(En(r,o)),d)}function Pa(r,o,l,d){for(var m=r.length,y=d?m:-1;(d?y--:++y<m)&&o(r[y],y,r););return l?xs(r,d?0:y,d?y+1:m):xs(r,d?y+1:0,d?m:y)}function Em(r,o){var l=r;return l instanceof je&&(l=l.value()),Iu(o,function(d,m){return m.func.apply(m.thisArg,qr([d],m.args))},l)}function sc(r,o,l){var d=r.length;if(d<2)return d?zr(r[0]):[];for(var m=-1,y=P(d);++m<d;)for(var E=r[m],S=-1;++S<d;)S!=m&&(y[m]=Xo(y[m]||E,r[S],o,l));return zr(Bt(y,1),o,l)}function Om(r,o,l){for(var d=-1,m=r.length,y=o.length,E={};++d<m;){var S=d<y?o[d]:s;l(E,r[d],S)}return E}function rc(r){return bt(r)?r:[]}function nc(r){return typeof r=="function"?r:os}function Gr(r,o){return Re(r)?r:hc(r,o)?[r]:Xm(st(r))}var s5=qe;function Kr(r,o,l){var d=r.length;return l=l===s?d:l,!o&&l>=d?r:xs(r,o,l)}var xm=HN||function(r){return Ut.clearTimeout(r)};function Sm(r,o){if(o)return r.slice();var l=r.length,d=Gp?Gp(l):new r.constructor(l);return r.copy(d),d}function oc(r){var o=new r.constructor(r.byteLength);return new ya(o).set(new ya(r)),o}function r5(r,o){var l=o?oc(r.buffer):r.buffer;return new r.constructor(l,r.byteOffset,r.byteLength)}function n5(r){var o=new r.constructor(r.source,up.exec(r));return o.lastIndex=r.lastIndex,o}function o5(r){return Jo?at(Jo.call(r)):{}}function Dm(r,o){var l=o?oc(r.buffer):r.buffer;return new r.constructor(l,r.byteOffset,r.length)}function Tm(r,o){if(r!==o){var l=r!==s,d=r===null,m=r===r,y=hs(r),E=o!==s,S=o===null,N=o===o,U=hs(o);if(!S&&!U&&!y&&r>o||y&&E&&N&&!S&&!U||d&&E&&N||!l&&N||!m)return 1;if(!d&&!y&&!U&&r<o||U&&l&&m&&!d&&!y||S&&l&&m||!E&&m||!N)return-1}return 0}function i5(r,o,l){for(var d=-1,m=r.criteria,y=o.criteria,E=m.length,S=l.length;++d<E;){var N=Tm(m[d],y[d]);if(N){if(d>=S)return N;var U=l[d];return N*(U=="desc"?-1:1)}}return r.index-o.index}function Nm(r,o,l,d){for(var m=-1,y=r.length,E=l.length,S=-1,N=o.length,U=At(y-E,0),B=P(N+U),W=!d;++S<N;)B[S]=o[S];for(;++m<E;)(W||m<y)&&(B[l[m]]=r[m]);for(;U--;)B[S++]=r[m++];return B}function Im(r,o,l,d){for(var m=-1,y=r.length,E=-1,S=l.length,N=-1,U=o.length,B=At(y-S,0),W=P(B+U),ae=!d;++m<B;)W[m]=r[m];for(var _e=m;++N<U;)W[_e+N]=o[N];for(;++E<S;)(ae||m<y)&&(W[_e+l[E]]=r[m++]);return W}function ss(r,o){var l=-1,d=r.length;for(o||(o=P(d));++l<d;)o[l]=r[l];return o}function tr(r,o,l,d){var m=!l;l||(l={});for(var y=-1,E=o.length;++y<E;){var S=o[y],N=d?d(l[S],r[S],S,l,r):s;N===s&&(N=r[S]),m?yr(l,S,N):Qo(l,S,N)}return l}function a5(r,o){return tr(r,fc(r),o)}function l5(r,o){return tr(r,Hm(r),o)}function ka(r,o){return function(l,d){var m=Re(l)?hN:II,y=o?o():{};return m(l,r,Oe(d,2),y)}}function eo(r){return qe(function(o,l){var d=-1,m=l.length,y=m>1?l[m-1]:s,E=m>2?l[2]:s;for(y=r.length>3&&typeof y=="function"?(m--,y):s,E&&Qt(l[0],l[1],E)&&(y=m<3?s:y,m=1),o=at(o);++d<m;){var S=l[d];S&&r(o,S,d,y)}return o})}function Am(r,o){return function(l,d){if(l==null)return l;if(!rs(l))return r(l,d);for(var m=l.length,y=o?m:-1,E=at(l);(o?y--:++y<m)&&d(E[y],y,E)!==!1;);return l}}function Mm(r){return function(o,l,d){for(var m=-1,y=at(o),E=d(o),S=E.length;S--;){var N=E[r?S:++m];if(l(y[N],N,y)===!1)break}return o}}function u5(r,o,l){var d=o&se,m=ri(r);function y(){var E=this&&this!==Ut&&this instanceof y?m:r;return E.apply(d?l:this,arguments)}return y}function Pm(r){return function(o){o=st(o);var l=Gn(o)?Ls(o):s,d=l?l[0]:o.charAt(0),m=l?Kr(l,1).join(""):o.slice(1);return d[r]()+m}}function to(r){return function(o){return Iu(Ag(Ig(o).replace(X3,"")),r,"")}}function ri(r){return function(){var o=arguments;switch(o.length){case 0:return new r;case 1:return new r(o[0]);case 2:return new r(o[0],o[1]);case 3:return new r(o[0],o[1],o[2]);case 4:return new r(o[0],o[1],o[2],o[3]);case 5:return new r(o[0],o[1],o[2],o[3],o[4]);case 6:return new r(o[0],o[1],o[2],o[3],o[4],o[5]);case 7:return new r(o[0],o[1],o[2],o[3],o[4],o[5],o[6])}var l=Xn(r.prototype),d=r.apply(l,o);return gt(d)?d:l}}function c5(r,o,l){var d=ri(r);function m(){for(var y=arguments.length,E=P(y),S=y,N=so(m);S--;)E[S]=arguments[S];var U=y<3&&E[0]!==N&&E[y-1]!==N?[]:Hr(E,N);if(y-=U.length,y<l)return Lm(r,o,Va,m.placeholder,s,E,U,s,s,l-y);var B=this&&this!==Ut&&this instanceof m?d:r;return cs(B,this,E)}return m}function km(r){return function(o,l,d){var m=at(o);if(!rs(o)){var y=Oe(l,3);o=Rt(o),l=function(S){return y(m[S],S,m)}}var E=r(o,l,d);return E>-1?m[y?o[E]:E]:s}}function Vm(r){return Cr(function(o){var l=o.length,d=l,m=Es.prototype.thru;for(r&&o.reverse();d--;){var y=o[d];if(typeof y!="function")throw new ws(u);if(m&&!E&&Ua(y)=="wrapper")var E=new Es([],!0)}for(d=E?d:l;++d<l;){y=o[d];var S=Ua(y),N=S=="wrapper"?cc(y):s;N&&pc(N[0])&&N[1]==(ve|Y|J|Ie)&&!N[4].length&&N[9]==1?E=E[Ua(N[0])].apply(E,N[3]):E=y.length==1&&pc(y)?E[S]():E.thru(y)}return function(){var U=arguments,B=U[0];if(E&&U.length==1&&Re(B))return E.plant(B).value();for(var W=0,ae=l?o[W].apply(this,U):B;++W<l;)ae=o[W].call(this,ae);return ae}})}function Va(r,o,l,d,m,y,E,S,N,U){var B=o&ve,W=o&se,ae=o&Q,_e=o&(Y|be),Se=o&ie,Be=ae?s:ri(r);function De(){for(var He=arguments.length,Ge=P(He),ps=He;ps--;)Ge[ps]=arguments[ps];if(_e)var Xt=so(De),ms=wN(Ge,Xt);if(d&&(Ge=Nm(Ge,d,m,_e)),y&&(Ge=Im(Ge,y,E,_e)),He-=ms,_e&&He<U){var Ct=Hr(Ge,Xt);return Lm(r,o,Va,De.placeholder,l,Ge,Ct,S,N,U-He)}var $s=W?l:this,xr=ae?$s[r]:r;return He=Ge.length,S?Ge=I5(Ge,S):Se&&He>1&&Ge.reverse(),B&&N<He&&(Ge.length=N),this&&this!==Ut&&this instanceof De&&(xr=Be||ri(xr)),xr.apply($s,Ge)}return De}function Rm(r,o){return function(l,d){return LI(l,r,o(d),{})}}function Ra(r,o){return function(l,d){var m;if(l===s&&d===s)return o;if(l!==s&&(m=l),d!==s){if(m===s)return d;typeof l=="string"||typeof d=="string"?(l=fs(l),d=fs(d)):(l=Cm(l),d=Cm(d)),m=r(l,d)}return m}}function ic(r){return Cr(function(o){return o=pt(o,ds(Oe())),qe(function(l){var d=this;return r(o,function(m){return cs(m,d,l)})})})}function Fa(r,o){o=o===s?" ":fs(o);var l=o.length;if(l<2)return l?Xu(o,r):o;var d=Xu(o,Ea(r/Kn(o)));return Gn(o)?Kr(Ls(d),0,r).join(""):d.slice(0,r)}function d5(r,o,l,d){var m=o&se,y=ri(r);function E(){for(var S=-1,N=arguments.length,U=-1,B=d.length,W=P(B+N),ae=this&&this!==Ut&&this instanceof E?y:r;++U<B;)W[U]=d[U];for(;N--;)W[U++]=arguments[++S];return cs(ae,m?l:this,W)}return E}function Fm(r){return function(o,l,d){return d&&typeof d!="number"&&Qt(o,l,d)&&(l=d=s),o=Or(o),l===s?(l=o,o=0):l=Or(l),d=d===s?o<l?1:-1:Or(d),YI(o,l,d,r)}}function La(r){return function(o,l){return typeof o=="string"&&typeof l=="string"||(o=Ds(o),l=Ds(l)),r(o,l)}}function Lm(r,o,l,d,m,y,E,S,N,U){var B=o&Y,W=B?E:s,ae=B?s:E,_e=B?y:s,Se=B?s:y;o|=B?J:he,o&=~(B?he:J),o&ne||(o&=~(se|Q));var Be=[r,o,m,_e,W,Se,ae,S,N,U],De=l.apply(s,Be);return pc(r)&&Ym(De,Be),De.placeholder=d,Jm(De,r,o)}function ac(r){var o=It[r];return function(l,d){if(l=Ds(l),d=d==null?0:qt(Le(d),292),d&&Jp(l)){var m=(st(l)+"e").split("e"),y=o(m[0]+"e"+(+m[1]+d));return m=(st(y)+"e").split("e"),+(m[0]+"e"+(+m[1]-d))}return o(l)}}var f5=Jn&&1/ha(new Jn([,-0]))[1]==ce?function(r){return new Jn(r)}:Nc;function Um(r){return function(o){var l=Ht(o);return l==b?Fu(o):l==G?NN(o):CN(o,r(o))}}function br(r,o,l,d,m,y,E,S){var N=o&Q;if(!N&&typeof r!="function")throw new ws(u);var U=d?d.length:0;if(U||(o&=~(J|he),d=m=s),E=E===s?E:At(Le(E),0),S=S===s?S:Le(S),U-=m?m.length:0,o&he){var B=d,W=m;d=m=s}var ae=N?s:cc(r),_e=[r,o,l,d,m,B,W,y,E,S];if(ae&&D5(_e,ae),r=_e[0],o=_e[1],l=_e[2],d=_e[3],m=_e[4],S=_e[9]=_e[9]===s?N?0:r.length:At(_e[9]-U,0),!S&&o&(Y|be)&&(o&=~(Y|be)),!o||o==se)var Se=u5(r,o,l);else o==Y||o==be?Se=c5(r,o,S):(o==J||o==(se|J))&&!m.length?Se=d5(r,o,l,d):Se=Va.apply(s,_e);var Be=ae?ym:Ym;return Jm(Be(Se,_e),r,o)}function Bm(r,o,l,d){return r===s||Bs(r,Yn[l])&&!nt.call(d,l)?o:r}function $m(r,o,l,d,m,y){return gt(r)&&gt(o)&&(y.set(o,r),Aa(r,o,s,$m,y),y.delete(o)),r}function h5(r){return ii(r)?s:r}function qm(r,o,l,d,m,y){var E=l&M,S=r.length,N=o.length;if(S!=N&&!(E&&N>S))return!1;var U=y.get(r),B=y.get(o);if(U&&B)return U==o&&B==r;var W=-1,ae=!0,_e=l&F?new Cn:s;for(y.set(r,o),y.set(o,r);++W<S;){var Se=r[W],Be=o[W];if(d)var De=E?d(Be,Se,W,o,r,y):d(Se,Be,W,r,o,y);if(De!==s){if(De)continue;ae=!1;break}if(_e){if(!Au(o,function(He,Ge){if(!zo(_e,Ge)&&(Se===He||m(Se,He,l,d,y)))return _e.push(Ge)})){ae=!1;break}}else if(!(Se===Be||m(Se,Be,l,d,y))){ae=!1;break}}return y.delete(r),y.delete(o),ae}function p5(r,o,l,d,m,y,E){switch(l){case Fe:if(r.byteLength!=o.byteLength||r.byteOffset!=o.byteOffset)return!1;r=r.buffer,o=o.buffer;case Ee:return!(r.byteLength!=o.byteLength||!y(new ya(r),new ya(o)));case oe:case re:case O:return Bs(+r,+o);case ke:return r.name==o.name&&r.message==o.message;case ee:case X:return r==o+"";case b:var S=Fu;case G:var N=d&M;if(S||(S=ha),r.size!=o.size&&!N)return!1;var U=E.get(r);if(U)return U==o;d|=F,E.set(r,o);var B=qm(S(r),S(o),d,m,y,E);return E.delete(r),B;case j:if(Jo)return Jo.call(r)==Jo.call(o)}return!1}function m5(r,o,l,d,m,y){var E=l&M,S=lc(r),N=S.length,U=lc(o),B=U.length;if(N!=B&&!E)return!1;for(var W=N;W--;){var ae=S[W];if(!(E?ae in o:nt.call(o,ae)))return!1}var _e=y.get(r),Se=y.get(o);if(_e&&Se)return _e==o&&Se==r;var Be=!0;y.set(r,o),y.set(o,r);for(var De=E;++W<N;){ae=S[W];var He=r[ae],Ge=o[ae];if(d)var ps=E?d(Ge,He,ae,o,r,y):d(He,Ge,ae,r,o,y);if(!(ps===s?He===Ge||m(He,Ge,l,d,y):ps)){Be=!1;break}De||(De=ae=="constructor")}if(Be&&!De){var Xt=r.constructor,ms=o.constructor;Xt!=ms&&"constructor"in r&&"constructor"in o&&!(typeof Xt=="function"&&Xt instanceof Xt&&typeof ms=="function"&&ms instanceof ms)&&(Be=!1)}return y.delete(r),y.delete(o),Be}function Cr(r){return gc(Km(r,s,rg),r+"")}function lc(r){return lm(r,Rt,fc)}function uc(r){return lm(r,ns,Hm)}var cc=xa?function(r){return xa.get(r)}:Nc;function Ua(r){for(var o=r.name+"",l=Qn[o],d=nt.call(Qn,o)?l.length:0;d--;){var m=l[d],y=m.func;if(y==null||y==r)return m.name}return o}function so(r){var o=nt.call(v,"placeholder")?v:r;return o.placeholder}function Oe(){var r=v.iteratee||Dc;return r=r===Dc?dm:r,arguments.length?r(arguments[0],arguments[1]):r}function Ba(r,o){var l=r.__data__;return E5(o)?l[typeof o=="string"?"string":"hash"]:l.map}function dc(r){for(var o=Rt(r),l=o.length;l--;){var d=o[l],m=r[d];o[l]=[d,m,zm(m)]}return o}function On(r,o){var l=SN(r,o);return cm(l)?l:s}function g5(r){var o=nt.call(r,yn),l=r[yn];try{r[yn]=s;var d=!0}catch{}var m=_a.call(r);return d&&(o?r[yn]=l:delete r[yn]),m}var fc=Uu?function(r){return r==null?[]:(r=at(r),$r(Uu(r),function(o){return Zp.call(r,o)}))}:Ic,Hm=Uu?function(r){for(var o=[];r;)qr(o,fc(r)),r=ba(r);return o}:Ic,Ht=Jt;(Bu&&Ht(new Bu(new ArrayBuffer(1)))!=Fe||Ko&&Ht(new Ko)!=b||$u&&Ht($u.resolve())!=$||Jn&&Ht(new Jn)!=G||Zo&&Ht(new Zo)!=te)&&(Ht=function(r){var o=Jt(r),l=o==L?r.constructor:s,d=l?xn(l):"";if(d)switch(d){case QN:return Fe;case XN:return b;case eI:return $;case tI:return G;case sI:return te}return o});function _5(r,o,l){for(var d=-1,m=l.length;++d<m;){var y=l[d],E=y.size;switch(y.type){case"drop":r+=E;break;case"dropRight":o-=E;break;case"take":o=qt(o,r+E);break;case"takeRight":r=At(r,o-E);break}}return{start:r,end:o}}function v5(r){var o=r.match(O3);return o?o[1].split(x3):[]}function Wm(r,o,l){o=Gr(o,r);for(var d=-1,m=o.length,y=!1;++d<m;){var E=sr(o[d]);if(!(y=r!=null&&l(r,E)))break;r=r[E]}return y||++d!=m?y:(m=r==null?0:r.length,!!m&&Ga(m)&&wr(E,m)&&(Re(r)||Sn(r)))}function y5(r){var o=r.length,l=new r.constructor(o);return o&&typeof r[0]=="string"&&nt.call(r,"index")&&(l.index=r.index,l.input=r.input),l}function jm(r){return typeof r.constructor=="function"&&!ni(r)?Xn(ba(r)):{}}function b5(r,o,l){var d=r.constructor;switch(o){case Ee:return oc(r);case oe:case re:return new d(+r);case Fe:return r5(r,l);case Qe:case Ze:case Vt:case Ot:case Yt:case Lt:case gr:case Wn:case Nt:return Dm(r,l);case b:return new d;case O:case X:return new d(r);case ee:return n5(r);case G:return new d;case j:return o5(r)}}function C5(r,o){var l=o.length;if(!l)return r;var d=l-1;return o[d]=(l>1?"& ":"")+o[d],o=o.join(l>2?", ":" "),r.replace(E3,`{
/* [wrapped with `+o+`] */
`)}function w5(r){return Re(r)||Sn(r)||!!(Yp&&r&&r[Yp])}function wr(r,o){var l=typeof r;return o=o??we,!!o&&(l=="number"||l!="symbol"&&k3.test(r))&&r>-1&&r%1==0&&r<o}function Qt(r,o,l){if(!gt(l))return!1;var d=typeof o;return(d=="number"?rs(l)&&wr(o,l.length):d=="string"&&o in l)?Bs(l[o],r):!1}function hc(r,o){if(Re(r))return!1;var l=typeof r;return l=="number"||l=="symbol"||l=="boolean"||r==null||hs(r)?!0:y3.test(r)||!v3.test(r)||o!=null&&r in at(o)}function E5(r){var o=typeof r;return o=="string"||o=="number"||o=="symbol"||o=="boolean"?r!=="__proto__":r===null}function pc(r){var o=Ua(r),l=v[o];if(typeof l!="function"||!(o in je.prototype))return!1;if(r===l)return!0;var d=cc(l);return!!d&&r===d[0]}function O5(r){return!!zp&&zp in r}var x5=ma?Er:Ac;function ni(r){var o=r&&r.constructor,l=typeof o=="function"&&o.prototype||Yn;return r===l}function zm(r){return r===r&&!gt(r)}function Gm(r,o){return function(l){return l==null?!1:l[r]===o&&(o!==s||r in at(l))}}function S5(r){var o=ja(r,function(d){return l.size===_&&l.clear(),d}),l=o.cache;return o}function D5(r,o){var l=r[1],d=o[1],m=l|d,y=m<(se|Q|ve),E=d==ve&&l==Y||d==ve&&l==Ie&&r[7].length<=o[8]||d==(ve|Ie)&&o[7].length<=o[8]&&l==Y;if(!(y||E))return r;d&se&&(r[2]=o[2],m|=l&se?0:ne);var S=o[3];if(S){var N=r[3];r[3]=N?Nm(N,S,o[4]):S,r[4]=N?Hr(r[3],p):o[4]}return S=o[5],S&&(N=r[5],r[5]=N?Im(N,S,o[6]):S,r[6]=N?Hr(r[5],p):o[6]),S=o[7],S&&(r[7]=S),d&ve&&(r[8]=r[8]==null?o[8]:qt(r[8],o[8])),r[9]==null&&(r[9]=o[9]),r[0]=o[0],r[1]=m,r}function T5(r){var o=[];if(r!=null)for(var l in at(r))o.push(l);return o}function N5(r){return _a.call(r)}function Km(r,o,l){return o=At(o===s?r.length-1:o,0),function(){for(var d=arguments,m=-1,y=At(d.length-o,0),E=P(y);++m<y;)E[m]=d[o+m];m=-1;for(var S=P(o+1);++m<o;)S[m]=d[m];return S[o]=l(E),cs(r,this,S)}}function Zm(r,o){return o.length<2?r:En(r,xs(o,0,-1))}function I5(r,o){for(var l=r.length,d=qt(o.length,l),m=ss(r);d--;){var y=o[d];r[d]=wr(y,l)?m[y]:s}return r}function mc(r,o){if(!(o==="constructor"&&typeof r[o]=="function")&&o!="__proto__")return r[o]}var Ym=Qm(ym),oi=jN||function(r,o){return Ut.setTimeout(r,o)},gc=Qm(XI);function Jm(r,o,l){var d=o+"";return gc(r,C5(d,A5(v5(d),l)))}function Qm(r){var o=0,l=0;return function(){var d=ZN(),m=ze-(d-l);if(l=d,m>0){if(++o>=le)return arguments[0]}else o=0;return r.apply(s,arguments)}}function $a(r,o){var l=-1,d=r.length,m=d-1;for(o=o===s?d:o;++l<o;){var y=Qu(l,m),E=r[y];r[y]=r[l],r[l]=E}return r.length=o,r}var Xm=S5(function(r){var o=[];return r.charCodeAt(0)===46&&o.push(""),r.replace(b3,function(l,d,m,y){o.push(m?y.replace(T3,"$1"):d||l)}),o});function sr(r){if(typeof r=="string"||hs(r))return r;var o=r+"";return o=="0"&&1/r==-ce?"-0":o}function xn(r){if(r!=null){try{return ga.call(r)}catch{}try{return r+""}catch{}}return""}function A5(r,o){return Cs(Xs,function(l){var d="_."+l[0];o&l[1]&&!da(r,d)&&r.push(d)}),r.sort()}function eg(r){if(r instanceof je)return r.clone();var o=new Es(r.__wrapped__,r.__chain__);return o.__actions__=ss(r.__actions__),o.__index__=r.__index__,o.__values__=r.__values__,o}function M5(r,o,l){(l?Qt(r,o,l):o===s)?o=1:o=At(Le(o),0);var d=r==null?0:r.length;if(!d||o<1)return[];for(var m=0,y=0,E=P(Ea(d/o));m<d;)E[y++]=xs(r,m,m+=o);return E}function P5(r){for(var o=-1,l=r==null?0:r.length,d=0,m=[];++o<l;){var y=r[o];y&&(m[d++]=y)}return m}function k5(){var r=arguments.length;if(!r)return[];for(var o=P(r-1),l=arguments[0],d=r;d--;)o[d-1]=arguments[d];return qr(Re(l)?ss(l):[l],Bt(o,1))}var V5=qe(function(r,o){return bt(r)?Xo(r,Bt(o,1,bt,!0)):[]}),R5=qe(function(r,o){var l=Ss(o);return bt(l)&&(l=s),bt(r)?Xo(r,Bt(o,1,bt,!0),Oe(l,2)):[]}),F5=qe(function(r,o){var l=Ss(o);return bt(l)&&(l=s),bt(r)?Xo(r,Bt(o,1,bt,!0),s,l):[]});function L5(r,o,l){var d=r==null?0:r.length;return d?(o=l||o===s?1:Le(o),xs(r,o<0?0:o,d)):[]}function U5(r,o,l){var d=r==null?0:r.length;return d?(o=l||o===s?1:Le(o),o=d-o,xs(r,0,o<0?0:o)):[]}function B5(r,o){return r&&r.length?Pa(r,Oe(o,3),!0,!0):[]}function $5(r,o){return r&&r.length?Pa(r,Oe(o,3),!0):[]}function q5(r,o,l,d){var m=r==null?0:r.length;return m?(l&&typeof l!="number"&&Qt(r,o,l)&&(l=0,d=m),kI(r,o,l,d)):[]}function tg(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var m=l==null?0:Le(l);return m<0&&(m=At(d+m,0)),fa(r,Oe(o,3),m)}function sg(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var m=d-1;return l!==s&&(m=Le(l),m=l<0?At(d+m,0):qt(m,d-1)),fa(r,Oe(o,3),m,!0)}function rg(r){var o=r==null?0:r.length;return o?Bt(r,1):[]}function H5(r){var o=r==null?0:r.length;return o?Bt(r,ce):[]}function W5(r,o){var l=r==null?0:r.length;return l?(o=o===s?1:Le(o),Bt(r,o)):[]}function j5(r){for(var o=-1,l=r==null?0:r.length,d={};++o<l;){var m=r[o];d[m[0]]=m[1]}return d}function ng(r){return r&&r.length?r[0]:s}function z5(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var m=l==null?0:Le(l);return m<0&&(m=At(d+m,0)),zn(r,o,m)}function G5(r){var o=r==null?0:r.length;return o?xs(r,0,-1):[]}var K5=qe(function(r){var o=pt(r,rc);return o.length&&o[0]===r[0]?Gu(o):[]}),Z5=qe(function(r){var o=Ss(r),l=pt(r,rc);return o===Ss(l)?o=s:l.pop(),l.length&&l[0]===r[0]?Gu(l,Oe(o,2)):[]}),Y5=qe(function(r){var o=Ss(r),l=pt(r,rc);return o=typeof o=="function"?o:s,o&&l.pop(),l.length&&l[0]===r[0]?Gu(l,s,o):[]});function J5(r,o){return r==null?"":GN.call(r,o)}function Ss(r){var o=r==null?0:r.length;return o?r[o-1]:s}function Q5(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var m=d;return l!==s&&(m=Le(l),m=m<0?At(d+m,0):qt(m,d-1)),o===o?AN(r,o,m):fa(r,Lp,m,!0)}function X5(r,o){return r&&r.length?mm(r,Le(o)):s}var e4=qe(og);function og(r,o){return r&&r.length&&o&&o.length?Ju(r,o):r}function t4(r,o,l){return r&&r.length&&o&&o.length?Ju(r,o,Oe(l,2)):r}function s4(r,o,l){return r&&r.length&&o&&o.length?Ju(r,o,s,l):r}var r4=Cr(function(r,o){var l=r==null?0:r.length,d=Hu(r,o);return vm(r,pt(o,function(m){return wr(m,l)?+m:m}).sort(Tm)),d});function n4(r,o){var l=[];if(!(r&&r.length))return l;var d=-1,m=[],y=r.length;for(o=Oe(o,3);++d<y;){var E=r[d];o(E,d,r)&&(l.push(E),m.push(d))}return vm(r,m),l}function _c(r){return r==null?r:JN.call(r)}function o4(r,o,l){var d=r==null?0:r.length;return d?(l&&typeof l!="number"&&Qt(r,o,l)?(o=0,l=d):(o=o==null?0:Le(o),l=l===s?d:Le(l)),xs(r,o,l)):[]}function i4(r,o){return Ma(r,o)}function a4(r,o,l){return ec(r,o,Oe(l,2))}function l4(r,o){var l=r==null?0:r.length;if(l){var d=Ma(r,o);if(d<l&&Bs(r[d],o))return d}return-1}function u4(r,o){return Ma(r,o,!0)}function c4(r,o,l){return ec(r,o,Oe(l,2),!0)}function d4(r,o){var l=r==null?0:r.length;if(l){var d=Ma(r,o,!0)-1;if(Bs(r[d],o))return d}return-1}function f4(r){return r&&r.length?bm(r):[]}function h4(r,o){return r&&r.length?bm(r,Oe(o,2)):[]}function p4(r){var o=r==null?0:r.length;return o?xs(r,1,o):[]}function m4(r,o,l){return r&&r.length?(o=l||o===s?1:Le(o),xs(r,0,o<0?0:o)):[]}function g4(r,o,l){var d=r==null?0:r.length;return d?(o=l||o===s?1:Le(o),o=d-o,xs(r,o<0?0:o,d)):[]}function _4(r,o){return r&&r.length?Pa(r,Oe(o,3),!1,!0):[]}function v4(r,o){return r&&r.length?Pa(r,Oe(o,3)):[]}var y4=qe(function(r){return zr(Bt(r,1,bt,!0))}),b4=qe(function(r){var o=Ss(r);return bt(o)&&(o=s),zr(Bt(r,1,bt,!0),Oe(o,2))}),C4=qe(function(r){var o=Ss(r);return o=typeof o=="function"?o:s,zr(Bt(r,1,bt,!0),s,o)});function w4(r){return r&&r.length?zr(r):[]}function E4(r,o){return r&&r.length?zr(r,Oe(o,2)):[]}function O4(r,o){return o=typeof o=="function"?o:s,r&&r.length?zr(r,s,o):[]}function vc(r){if(!(r&&r.length))return[];var o=0;return r=$r(r,function(l){if(bt(l))return o=At(l.length,o),!0}),Vu(o,function(l){return pt(r,Mu(l))})}function ig(r,o){if(!(r&&r.length))return[];var l=vc(r);return o==null?l:pt(l,function(d){return cs(o,s,d)})}var x4=qe(function(r,o){return bt(r)?Xo(r,o):[]}),S4=qe(function(r){return sc($r(r,bt))}),D4=qe(function(r){var o=Ss(r);return bt(o)&&(o=s),sc($r(r,bt),Oe(o,2))}),T4=qe(function(r){var o=Ss(r);return o=typeof o=="function"?o:s,sc($r(r,bt),s,o)}),N4=qe(vc);function I4(r,o){return Om(r||[],o||[],Qo)}function A4(r,o){return Om(r||[],o||[],si)}var M4=qe(function(r){var o=r.length,l=o>1?r[o-1]:s;return l=typeof l=="function"?(r.pop(),l):s,ig(r,l)});function ag(r){var o=v(r);return o.__chain__=!0,o}function P4(r,o){return o(r),r}function qa(r,o){return o(r)}var k4=Cr(function(r){var o=r.length,l=o?r[0]:0,d=this.__wrapped__,m=function(y){return Hu(y,r)};return o>1||this.__actions__.length||!(d instanceof je)||!wr(l)?this.thru(m):(d=d.slice(l,+l+(o?1:0)),d.__actions__.push({func:qa,args:[m],thisArg:s}),new Es(d,this.__chain__).thru(function(y){return o&&!y.length&&y.push(s),y}))});function V4(){return ag(this)}function R4(){return new Es(this.value(),this.__chain__)}function F4(){this.__values__===s&&(this.__values__=Cg(this.value()));var r=this.__index__>=this.__values__.length,o=r?s:this.__values__[this.__index__++];return{done:r,value:o}}function L4(){return this}function U4(r){for(var o,l=this;l instanceof Da;){var d=eg(l);d.__index__=0,d.__values__=s,o?m.__wrapped__=d:o=d;var m=d;l=l.__wrapped__}return m.__wrapped__=r,o}function B4(){var r=this.__wrapped__;if(r instanceof je){var o=r;return this.__actions__.length&&(o=new je(this)),o=o.reverse(),o.__actions__.push({func:qa,args:[_c],thisArg:s}),new Es(o,this.__chain__)}return this.thru(_c)}function $4(){return Em(this.__wrapped__,this.__actions__)}var q4=ka(function(r,o,l){nt.call(r,l)?++r[l]:yr(r,l,1)});function H4(r,o,l){var d=Re(r)?Rp:PI;return l&&Qt(r,o,l)&&(o=s),d(r,Oe(o,3))}function W4(r,o){var l=Re(r)?$r:im;return l(r,Oe(o,3))}var j4=km(tg),z4=km(sg);function G4(r,o){return Bt(Ha(r,o),1)}function K4(r,o){return Bt(Ha(r,o),ce)}function Z4(r,o,l){return l=l===s?1:Le(l),Bt(Ha(r,o),l)}function lg(r,o){var l=Re(r)?Cs:jr;return l(r,Oe(o,3))}function ug(r,o){var l=Re(r)?pN:om;return l(r,Oe(o,3))}var Y4=ka(function(r,o,l){nt.call(r,l)?r[l].push(o):yr(r,l,[o])});function J4(r,o,l,d){r=rs(r)?r:no(r),l=l&&!d?Le(l):0;var m=r.length;return l<0&&(l=At(m+l,0)),Ka(r)?l<=m&&r.indexOf(o,l)>-1:!!m&&zn(r,o,l)>-1}var Q4=qe(function(r,o,l){var d=-1,m=typeof o=="function",y=rs(r)?P(r.length):[];return jr(r,function(E){y[++d]=m?cs(o,E,l):ei(E,o,l)}),y}),X4=ka(function(r,o,l){yr(r,l,o)});function Ha(r,o){var l=Re(r)?pt:fm;return l(r,Oe(o,3))}function eA(r,o,l,d){return r==null?[]:(Re(o)||(o=o==null?[]:[o]),l=d?s:l,Re(l)||(l=l==null?[]:[l]),gm(r,o,l))}var tA=ka(function(r,o,l){r[l?0:1].push(o)},function(){return[[],[]]});function sA(r,o,l){var d=Re(r)?Iu:Bp,m=arguments.length<3;return d(r,Oe(o,4),l,m,jr)}function rA(r,o,l){var d=Re(r)?mN:Bp,m=arguments.length<3;return d(r,Oe(o,4),l,m,om)}function nA(r,o){var l=Re(r)?$r:im;return l(r,za(Oe(o,3)))}function oA(r){var o=Re(r)?tm:JI;return o(r)}function iA(r,o,l){(l?Qt(r,o,l):o===s)?o=1:o=Le(o);var d=Re(r)?TI:QI;return d(r,o)}function aA(r){var o=Re(r)?NI:e5;return o(r)}function lA(r){if(r==null)return 0;if(rs(r))return Ka(r)?Kn(r):r.length;var o=Ht(r);return o==b||o==G?r.size:Zu(r).length}function uA(r,o,l){var d=Re(r)?Au:t5;return l&&Qt(r,o,l)&&(o=s),d(r,Oe(o,3))}var cA=qe(function(r,o){if(r==null)return[];var l=o.length;return l>1&&Qt(r,o[0],o[1])?o=[]:l>2&&Qt(o[0],o[1],o[2])&&(o=[o[0]]),gm(r,Bt(o,1),[])}),Wa=WN||function(){return Ut.Date.now()};function dA(r,o){if(typeof o!="function")throw new ws(u);return r=Le(r),function(){if(--r<1)return o.apply(this,arguments)}}function cg(r,o,l){return o=l?s:o,o=r&&o==null?r.length:o,br(r,ve,s,s,s,s,o)}function dg(r,o){var l;if(typeof o!="function")throw new ws(u);return r=Le(r),function(){return--r>0&&(l=o.apply(this,arguments)),r<=1&&(o=s),l}}var yc=qe(function(r,o,l){var d=se;if(l.length){var m=Hr(l,so(yc));d|=J}return br(r,d,o,l,m)}),fg=qe(function(r,o,l){var d=se|Q;if(l.length){var m=Hr(l,so(fg));d|=J}return br(o,d,r,l,m)});function hg(r,o,l){o=l?s:o;var d=br(r,Y,s,s,s,s,s,o);return d.placeholder=hg.placeholder,d}function pg(r,o,l){o=l?s:o;var d=br(r,be,s,s,s,s,s,o);return d.placeholder=pg.placeholder,d}function mg(r,o,l){var d,m,y,E,S,N,U=0,B=!1,W=!1,ae=!0;if(typeof r!="function")throw new ws(u);o=Ds(o)||0,gt(l)&&(B=!!l.leading,W="maxWait"in l,y=W?At(Ds(l.maxWait)||0,o):y,ae="trailing"in l?!!l.trailing:ae);function _e(Ct){var $s=d,xr=m;return d=m=s,U=Ct,E=r.apply(xr,$s),E}function Se(Ct){return U=Ct,S=oi(He,o),B?_e(Ct):E}function Be(Ct){var $s=Ct-N,xr=Ct-U,kg=o-$s;return W?qt(kg,y-xr):kg}function De(Ct){var $s=Ct-N,xr=Ct-U;return N===s||$s>=o||$s<0||W&&xr>=y}function He(){var Ct=Wa();if(De(Ct))return Ge(Ct);S=oi(He,Be(Ct))}function Ge(Ct){return S=s,ae&&d?_e(Ct):(d=m=s,E)}function ps(){S!==s&&xm(S),U=0,d=N=m=S=s}function Xt(){return S===s?E:Ge(Wa())}function ms(){var Ct=Wa(),$s=De(Ct);if(d=arguments,m=this,N=Ct,$s){if(S===s)return Se(N);if(W)return xm(S),S=oi(He,o),_e(N)}return S===s&&(S=oi(He,o)),E}return ms.cancel=ps,ms.flush=Xt,ms}var fA=qe(function(r,o){return nm(r,1,o)}),hA=qe(function(r,o,l){return nm(r,Ds(o)||0,l)});function pA(r){return br(r,ie)}function ja(r,o){if(typeof r!="function"||o!=null&&typeof o!="function")throw new ws(u);var l=function(){var d=arguments,m=o?o.apply(this,d):d[0],y=l.cache;if(y.has(m))return y.get(m);var E=r.apply(this,d);return l.cache=y.set(m,E)||y,E};return l.cache=new(ja.Cache||vr),l}ja.Cache=vr;function za(r){if(typeof r!="function")throw new ws(u);return function(){var o=arguments;switch(o.length){case 0:return!r.call(this);case 1:return!r.call(this,o[0]);case 2:return!r.call(this,o[0],o[1]);case 3:return!r.call(this,o[0],o[1],o[2])}return!r.apply(this,o)}}function mA(r){return dg(2,r)}var gA=s5(function(r,o){o=o.length==1&&Re(o[0])?pt(o[0],ds(Oe())):pt(Bt(o,1),ds(Oe()));var l=o.length;return qe(function(d){for(var m=-1,y=qt(d.length,l);++m<y;)d[m]=o[m].call(this,d[m]);return cs(r,this,d)})}),bc=qe(function(r,o){var l=Hr(o,so(bc));return br(r,J,s,o,l)}),gg=qe(function(r,o){var l=Hr(o,so(gg));return br(r,he,s,o,l)}),_A=Cr(function(r,o){return br(r,Ie,s,s,s,o)});function vA(r,o){if(typeof r!="function")throw new ws(u);return o=o===s?o:Le(o),qe(r,o)}function yA(r,o){if(typeof r!="function")throw new ws(u);return o=o==null?0:At(Le(o),0),qe(function(l){var d=l[o],m=Kr(l,0,o);return d&&qr(m,d),cs(r,this,m)})}function bA(r,o,l){var d=!0,m=!0;if(typeof r!="function")throw new ws(u);return gt(l)&&(d="leading"in l?!!l.leading:d,m="trailing"in l?!!l.trailing:m),mg(r,o,{leading:d,maxWait:o,trailing:m})}function CA(r){return cg(r,1)}function wA(r,o){return bc(nc(o),r)}function EA(){if(!arguments.length)return[];var r=arguments[0];return Re(r)?r:[r]}function OA(r){return Os(r,C)}function xA(r,o){return o=typeof o=="function"?o:s,Os(r,C,o)}function SA(r){return Os(r,g|C)}function DA(r,o){return o=typeof o=="function"?o:s,Os(r,g|C,o)}function TA(r,o){return o==null||rm(r,o,Rt(o))}function Bs(r,o){return r===o||r!==r&&o!==o}var NA=La(zu),IA=La(function(r,o){return r>=o}),Sn=um(function(){return arguments}())?um:function(r){return _t(r)&&nt.call(r,"callee")&&!Zp.call(r,"callee")},Re=P.isArray,AA=Ip?ds(Ip):UI;function rs(r){return r!=null&&Ga(r.length)&&!Er(r)}function bt(r){return _t(r)&&rs(r)}function MA(r){return r===!0||r===!1||_t(r)&&Jt(r)==oe}var Zr=zN||Ac,PA=Ap?ds(Ap):BI;function kA(r){return _t(r)&&r.nodeType===1&&!ii(r)}function VA(r){if(r==null)return!0;if(rs(r)&&(Re(r)||typeof r=="string"||typeof r.splice=="function"||Zr(r)||ro(r)||Sn(r)))return!r.length;var o=Ht(r);if(o==b||o==G)return!r.size;if(ni(r))return!Zu(r).length;for(var l in r)if(nt.call(r,l))return!1;return!0}function RA(r,o){return ti(r,o)}function FA(r,o,l){l=typeof l=="function"?l:s;var d=l?l(r,o):s;return d===s?ti(r,o,s,l):!!d}function Cc(r){if(!_t(r))return!1;var o=Jt(r);return o==ke||o==me||typeof r.message=="string"&&typeof r.name=="string"&&!ii(r)}function LA(r){return typeof r=="number"&&Jp(r)}function Er(r){if(!gt(r))return!1;var o=Jt(r);return o==rt||o==Ve||o==V||o==H}function _g(r){return typeof r=="number"&&r==Le(r)}function Ga(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=we}function gt(r){var o=typeof r;return r!=null&&(o=="object"||o=="function")}function _t(r){return r!=null&&typeof r=="object"}var vg=Mp?ds(Mp):qI;function UA(r,o){return r===o||Ku(r,o,dc(o))}function BA(r,o,l){return l=typeof l=="function"?l:s,Ku(r,o,dc(o),l)}function $A(r){return yg(r)&&r!=+r}function qA(r){if(x5(r))throw new Me(a);return cm(r)}function HA(r){return r===null}function WA(r){return r==null}function yg(r){return typeof r=="number"||_t(r)&&Jt(r)==O}function ii(r){if(!_t(r)||Jt(r)!=L)return!1;var o=ba(r);if(o===null)return!0;var l=nt.call(o,"constructor")&&o.constructor;return typeof l=="function"&&l instanceof l&&ga.call(l)==BN}var wc=Pp?ds(Pp):HI;function jA(r){return _g(r)&&r>=-we&&r<=we}var bg=kp?ds(kp):WI;function Ka(r){return typeof r=="string"||!Re(r)&&_t(r)&&Jt(r)==X}function hs(r){return typeof r=="symbol"||_t(r)&&Jt(r)==j}var ro=Vp?ds(Vp):jI;function zA(r){return r===s}function GA(r){return _t(r)&&Ht(r)==te}function KA(r){return _t(r)&&Jt(r)==ge}var ZA=La(Yu),YA=La(function(r,o){return r<=o});function Cg(r){if(!r)return[];if(rs(r))return Ka(r)?Ls(r):ss(r);if(Go&&r[Go])return TN(r[Go]());var o=Ht(r),l=o==b?Fu:o==G?ha:no;return l(r)}function Or(r){if(!r)return r===0?r:0;if(r=Ds(r),r===ce||r===-ce){var o=r<0?-1:1;return o*Et}return r===r?r:0}function Le(r){var o=Or(r),l=o%1;return o===o?l?o-l:o:0}function wg(r){return r?wn(Le(r),0,Tt):0}function Ds(r){if(typeof r=="number")return r;if(hs(r))return _s;if(gt(r)){var o=typeof r.valueOf=="function"?r.valueOf():r;r=gt(o)?o+"":o}if(typeof r!="string")return r===0?r:+r;r=$p(r);var l=A3.test(r);return l||P3.test(r)?dN(r.slice(2),l?2:8):I3.test(r)?_s:+r}function Eg(r){return tr(r,ns(r))}function JA(r){return r?wn(Le(r),-we,we):r===0?r:0}function st(r){return r==null?"":fs(r)}var QA=eo(function(r,o){if(ni(o)||rs(o)){tr(o,Rt(o),r);return}for(var l in o)nt.call(o,l)&&Qo(r,l,o[l])}),Og=eo(function(r,o){tr(o,ns(o),r)}),Za=eo(function(r,o,l,d){tr(o,ns(o),r,d)}),XA=eo(function(r,o,l,d){tr(o,Rt(o),r,d)}),e8=Cr(Hu);function t8(r,o){var l=Xn(r);return o==null?l:sm(l,o)}var s8=qe(function(r,o){r=at(r);var l=-1,d=o.length,m=d>2?o[2]:s;for(m&&Qt(o[0],o[1],m)&&(d=1);++l<d;)for(var y=o[l],E=ns(y),S=-1,N=E.length;++S<N;){var U=E[S],B=r[U];(B===s||Bs(B,Yn[U])&&!nt.call(r,U))&&(r[U]=y[U])}return r}),r8=qe(function(r){return r.push(s,$m),cs(xg,s,r)});function n8(r,o){return Fp(r,Oe(o,3),er)}function o8(r,o){return Fp(r,Oe(o,3),ju)}function i8(r,o){return r==null?r:Wu(r,Oe(o,3),ns)}function a8(r,o){return r==null?r:am(r,Oe(o,3),ns)}function l8(r,o){return r&&er(r,Oe(o,3))}function u8(r,o){return r&&ju(r,Oe(o,3))}function c8(r){return r==null?[]:Ia(r,Rt(r))}function d8(r){return r==null?[]:Ia(r,ns(r))}function Ec(r,o,l){var d=r==null?s:En(r,o);return d===s?l:d}function f8(r,o){return r!=null&&Wm(r,o,VI)}function Oc(r,o){return r!=null&&Wm(r,o,RI)}var h8=Rm(function(r,o,l){o!=null&&typeof o.toString!="function"&&(o=_a.call(o)),r[o]=l},Sc(os)),p8=Rm(function(r,o,l){o!=null&&typeof o.toString!="function"&&(o=_a.call(o)),nt.call(r,o)?r[o].push(l):r[o]=[l]},Oe),m8=qe(ei);function Rt(r){return rs(r)?em(r):Zu(r)}function ns(r){return rs(r)?em(r,!0):zI(r)}function g8(r,o){var l={};return o=Oe(o,3),er(r,function(d,m,y){yr(l,o(d,m,y),d)}),l}function _8(r,o){var l={};return o=Oe(o,3),er(r,function(d,m,y){yr(l,m,o(d,m,y))}),l}var v8=eo(function(r,o,l){Aa(r,o,l)}),xg=eo(function(r,o,l,d){Aa(r,o,l,d)}),y8=Cr(function(r,o){var l={};if(r==null)return l;var d=!1;o=pt(o,function(y){return y=Gr(y,r),d||(d=y.length>1),y}),tr(r,uc(r),l),d&&(l=Os(l,g|w|C,h5));for(var m=o.length;m--;)tc(l,o[m]);return l});function b8(r,o){return Sg(r,za(Oe(o)))}var C8=Cr(function(r,o){return r==null?{}:KI(r,o)});function Sg(r,o){if(r==null)return{};var l=pt(uc(r),function(d){return[d]});return o=Oe(o),_m(r,l,function(d,m){return o(d,m[0])})}function w8(r,o,l){o=Gr(o,r);var d=-1,m=o.length;for(m||(m=1,r=s);++d<m;){var y=r==null?s:r[sr(o[d])];y===s&&(d=m,y=l),r=Er(y)?y.call(r):y}return r}function E8(r,o,l){return r==null?r:si(r,o,l)}function O8(r,o,l,d){return d=typeof d=="function"?d:s,r==null?r:si(r,o,l,d)}var Dg=Um(Rt),Tg=Um(ns);function x8(r,o,l){var d=Re(r),m=d||Zr(r)||ro(r);if(o=Oe(o,4),l==null){var y=r&&r.constructor;m?l=d?new y:[]:gt(r)?l=Er(y)?Xn(ba(r)):{}:l={}}return(m?Cs:er)(r,function(E,S,N){return o(l,E,S,N)}),l}function S8(r,o){return r==null?!0:tc(r,o)}function D8(r,o,l){return r==null?r:wm(r,o,nc(l))}function T8(r,o,l,d){return d=typeof d=="function"?d:s,r==null?r:wm(r,o,nc(l),d)}function no(r){return r==null?[]:Ru(r,Rt(r))}function N8(r){return r==null?[]:Ru(r,ns(r))}function I8(r,o,l){return l===s&&(l=o,o=s),l!==s&&(l=Ds(l),l=l===l?l:0),o!==s&&(o=Ds(o),o=o===o?o:0),wn(Ds(r),o,l)}function A8(r,o,l){return o=Or(o),l===s?(l=o,o=0):l=Or(l),r=Ds(r),FI(r,o,l)}function M8(r,o,l){if(l&&typeof l!="boolean"&&Qt(r,o,l)&&(o=l=s),l===s&&(typeof o=="boolean"?(l=o,o=s):typeof r=="boolean"&&(l=r,r=s)),r===s&&o===s?(r=0,o=1):(r=Or(r),o===s?(o=r,r=0):o=Or(o)),r>o){var d=r;r=o,o=d}if(l||r%1||o%1){var m=Qp();return qt(r+m*(o-r+cN("1e-"+((m+"").length-1))),o)}return Qu(r,o)}var P8=to(function(r,o,l){return o=o.toLowerCase(),r+(l?Ng(o):o)});function Ng(r){return xc(st(r).toLowerCase())}function Ig(r){return r=st(r),r&&r.replace(V3,EN).replace(eN,"")}function k8(r,o,l){r=st(r),o=fs(o);var d=r.length;l=l===s?d:wn(Le(l),0,d);var m=l;return l-=o.length,l>=0&&r.slice(l,m)==o}function V8(r){return r=st(r),r&&m3.test(r)?r.replace(ap,ON):r}function R8(r){return r=st(r),r&&C3.test(r)?r.replace(bu,"\\$&"):r}var F8=to(function(r,o,l){return r+(l?"-":"")+o.toLowerCase()}),L8=to(function(r,o,l){return r+(l?" ":"")+o.toLowerCase()}),U8=Pm("toLowerCase");function B8(r,o,l){r=st(r),o=Le(o);var d=o?Kn(r):0;if(!o||d>=o)return r;var m=(o-d)/2;return Fa(Oa(m),l)+r+Fa(Ea(m),l)}function $8(r,o,l){r=st(r),o=Le(o);var d=o?Kn(r):0;return o&&d<o?r+Fa(o-d,l):r}function q8(r,o,l){r=st(r),o=Le(o);var d=o?Kn(r):0;return o&&d<o?Fa(o-d,l)+r:r}function H8(r,o,l){return l||o==null?o=0:o&&(o=+o),YN(st(r).replace(Cu,""),o||0)}function W8(r,o,l){return(l?Qt(r,o,l):o===s)?o=1:o=Le(o),Xu(st(r),o)}function j8(){var r=arguments,o=st(r[0]);return r.length<3?o:o.replace(r[1],r[2])}var z8=to(function(r,o,l){return r+(l?"_":"")+o.toLowerCase()});function G8(r,o,l){return l&&typeof l!="number"&&Qt(r,o,l)&&(o=l=s),l=l===s?Tt:l>>>0,l?(r=st(r),r&&(typeof o=="string"||o!=null&&!wc(o))&&(o=fs(o),!o&&Gn(r))?Kr(Ls(r),0,l):r.split(o,l)):[]}var K8=to(function(r,o,l){return r+(l?" ":"")+xc(o)});function Z8(r,o,l){return r=st(r),l=l==null?0:wn(Le(l),0,r.length),o=fs(o),r.slice(l,l+o.length)==o}function Y8(r,o,l){var d=v.templateSettings;l&&Qt(r,o,l)&&(o=s),r=st(r),o=Za({},o,d,Bm);var m=Za({},o.imports,d.imports,Bm),y=Rt(m),E=Ru(m,y),S,N,U=0,B=o.interpolate||la,W="__p += '",ae=Lu((o.escape||la).source+"|"+B.source+"|"+(B===lp?N3:la).source+"|"+(o.evaluate||la).source+"|$","g"),_e="//# sourceURL="+(nt.call(o,"sourceURL")?(o.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++oN+"]")+`
`;r.replace(ae,function(De,He,Ge,ps,Xt,ms){return Ge||(Ge=ps),W+=r.slice(U,ms).replace(R3,xN),He&&(S=!0,W+=`' +
__e(`+He+`) +
'`),Xt&&(N=!0,W+=`';
`+Xt+`;
__p += '`),Ge&&(W+=`' +
((__t = (`+Ge+`)) == null ? '' : __t) +
'`),U=ms+De.length,De}),W+=`';
`;var Se=nt.call(o,"variable")&&o.variable;if(!Se)W=`with (obj) {
`+W+`
}
`;else if(D3.test(Se))throw new Me(c);W=(N?W.replace(ys,""):W).replace(aa,"$1").replace(h3,"$1;"),W="function("+(Se||"obj")+`) {
`+(Se?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(S?", __e = _.escape":"")+(N?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+W+`return __p
}`;var Be=Mg(function(){return Xe(y,_e+"return "+W).apply(s,E)});if(Be.source=W,Cc(Be))throw Be;return Be}function J8(r){return st(r).toLowerCase()}function Q8(r){return st(r).toUpperCase()}function X8(r,o,l){if(r=st(r),r&&(l||o===s))return $p(r);if(!r||!(o=fs(o)))return r;var d=Ls(r),m=Ls(o),y=qp(d,m),E=Hp(d,m)+1;return Kr(d,y,E).join("")}function e6(r,o,l){if(r=st(r),r&&(l||o===s))return r.slice(0,jp(r)+1);if(!r||!(o=fs(o)))return r;var d=Ls(r),m=Hp(d,Ls(o))+1;return Kr(d,0,m).join("")}function t6(r,o,l){if(r=st(r),r&&(l||o===s))return r.replace(Cu,"");if(!r||!(o=fs(o)))return r;var d=Ls(r),m=qp(d,Ls(o));return Kr(d,m).join("")}function s6(r,o){var l=I,d=Ce;if(gt(o)){var m="separator"in o?o.separator:m;l="length"in o?Le(o.length):l,d="omission"in o?fs(o.omission):d}r=st(r);var y=r.length;if(Gn(r)){var E=Ls(r);y=E.length}if(l>=y)return r;var S=l-Kn(d);if(S<1)return d;var N=E?Kr(E,0,S).join(""):r.slice(0,S);if(m===s)return N+d;if(E&&(S+=N.length-S),wc(m)){if(r.slice(S).search(m)){var U,B=N;for(m.global||(m=Lu(m.source,st(up.exec(m))+"g")),m.lastIndex=0;U=m.exec(B);)var W=U.index;N=N.slice(0,W===s?S:W)}}else if(r.indexOf(fs(m),S)!=S){var ae=N.lastIndexOf(m);ae>-1&&(N=N.slice(0,ae))}return N+d}function r6(r){return r=st(r),r&&p3.test(r)?r.replace(ip,MN):r}var n6=to(function(r,o,l){return r+(l?" ":"")+o.toUpperCase()}),xc=Pm("toUpperCase");function Ag(r,o,l){return r=st(r),o=l?s:o,o===s?DN(r)?VN(r):vN(r):r.match(o)||[]}var Mg=qe(function(r,o){try{return cs(r,s,o)}catch(l){return Cc(l)?l:new Me(l)}}),o6=Cr(function(r,o){return Cs(o,function(l){l=sr(l),yr(r,l,yc(r[l],r))}),r});function i6(r){var o=r==null?0:r.length,l=Oe();return r=o?pt(r,function(d){if(typeof d[1]!="function")throw new ws(u);return[l(d[0]),d[1]]}):[],qe(function(d){for(var m=-1;++m<o;){var y=r[m];if(cs(y[0],this,d))return cs(y[1],this,d)}})}function a6(r){return MI(Os(r,g))}function Sc(r){return function(){return r}}function l6(r,o){return r==null||r!==r?o:r}var u6=Vm(),c6=Vm(!0);function os(r){return r}function Dc(r){return dm(typeof r=="function"?r:Os(r,g))}function d6(r){return hm(Os(r,g))}function f6(r,o){return pm(r,Os(o,g))}var h6=qe(function(r,o){return function(l){return ei(l,r,o)}}),p6=qe(function(r,o){return function(l){return ei(r,l,o)}});function Tc(r,o,l){var d=Rt(o),m=Ia(o,d);l==null&&!(gt(o)&&(m.length||!d.length))&&(l=o,o=r,r=this,m=Ia(o,Rt(o)));var y=!(gt(l)&&"chain"in l)||!!l.chain,E=Er(r);return Cs(m,function(S){var N=o[S];r[S]=N,E&&(r.prototype[S]=function(){var U=this.__chain__;if(y||U){var B=r(this.__wrapped__),W=B.__actions__=ss(this.__actions__);return W.push({func:N,args:arguments,thisArg:r}),B.__chain__=U,B}return N.apply(r,qr([this.value()],arguments))})}),r}function m6(){return Ut._===this&&(Ut._=$N),this}function Nc(){}function g6(r){return r=Le(r),qe(function(o){return mm(o,r)})}var _6=ic(pt),v6=ic(Rp),y6=ic(Au);function Pg(r){return hc(r)?Mu(sr(r)):ZI(r)}function b6(r){return function(o){return r==null?s:En(r,o)}}var C6=Fm(),w6=Fm(!0);function Ic(){return[]}function Ac(){return!1}function E6(){return{}}function O6(){return""}function x6(){return!0}function S6(r,o){if(r=Le(r),r<1||r>we)return[];var l=Tt,d=qt(r,Tt);o=Oe(o),r-=Tt;for(var m=Vu(d,o);++l<r;)o(l);return m}function D6(r){return Re(r)?pt(r,sr):hs(r)?[r]:ss(Xm(st(r)))}function T6(r){var o=++UN;return st(r)+o}var N6=Ra(function(r,o){return r+o},0),I6=ac("ceil"),A6=Ra(function(r,o){return r/o},1),M6=ac("floor");function P6(r){return r&&r.length?Na(r,os,zu):s}function k6(r,o){return r&&r.length?Na(r,Oe(o,2),zu):s}function V6(r){return Up(r,os)}function R6(r,o){return Up(r,Oe(o,2))}function F6(r){return r&&r.length?Na(r,os,Yu):s}function L6(r,o){return r&&r.length?Na(r,Oe(o,2),Yu):s}var U6=Ra(function(r,o){return r*o},1),B6=ac("round"),$6=Ra(function(r,o){return r-o},0);function q6(r){return r&&r.length?ku(r,os):0}function H6(r,o){return r&&r.length?ku(r,Oe(o,2)):0}return v.after=dA,v.ary=cg,v.assign=QA,v.assignIn=Og,v.assignInWith=Za,v.assignWith=XA,v.at=e8,v.before=dg,v.bind=yc,v.bindAll=o6,v.bindKey=fg,v.castArray=EA,v.chain=ag,v.chunk=M5,v.compact=P5,v.concat=k5,v.cond=i6,v.conforms=a6,v.constant=Sc,v.countBy=q4,v.create=t8,v.curry=hg,v.curryRight=pg,v.debounce=mg,v.defaults=s8,v.defaultsDeep=r8,v.defer=fA,v.delay=hA,v.difference=V5,v.differenceBy=R5,v.differenceWith=F5,v.drop=L5,v.dropRight=U5,v.dropRightWhile=B5,v.dropWhile=$5,v.fill=q5,v.filter=W4,v.flatMap=G4,v.flatMapDeep=K4,v.flatMapDepth=Z4,v.flatten=rg,v.flattenDeep=H5,v.flattenDepth=W5,v.flip=pA,v.flow=u6,v.flowRight=c6,v.fromPairs=j5,v.functions=c8,v.functionsIn=d8,v.groupBy=Y4,v.initial=G5,v.intersection=K5,v.intersectionBy=Z5,v.intersectionWith=Y5,v.invert=h8,v.invertBy=p8,v.invokeMap=Q4,v.iteratee=Dc,v.keyBy=X4,v.keys=Rt,v.keysIn=ns,v.map=Ha,v.mapKeys=g8,v.mapValues=_8,v.matches=d6,v.matchesProperty=f6,v.memoize=ja,v.merge=v8,v.mergeWith=xg,v.method=h6,v.methodOf=p6,v.mixin=Tc,v.negate=za,v.nthArg=g6,v.omit=y8,v.omitBy=b8,v.once=mA,v.orderBy=eA,v.over=_6,v.overArgs=gA,v.overEvery=v6,v.overSome=y6,v.partial=bc,v.partialRight=gg,v.partition=tA,v.pick=C8,v.pickBy=Sg,v.property=Pg,v.propertyOf=b6,v.pull=e4,v.pullAll=og,v.pullAllBy=t4,v.pullAllWith=s4,v.pullAt=r4,v.range=C6,v.rangeRight=w6,v.rearg=_A,v.reject=nA,v.remove=n4,v.rest=vA,v.reverse=_c,v.sampleSize=iA,v.set=E8,v.setWith=O8,v.shuffle=aA,v.slice=o4,v.sortBy=cA,v.sortedUniq=f4,v.sortedUniqBy=h4,v.split=G8,v.spread=yA,v.tail=p4,v.take=m4,v.takeRight=g4,v.takeRightWhile=_4,v.takeWhile=v4,v.tap=P4,v.throttle=bA,v.thru=qa,v.toArray=Cg,v.toPairs=Dg,v.toPairsIn=Tg,v.toPath=D6,v.toPlainObject=Eg,v.transform=x8,v.unary=CA,v.union=y4,v.unionBy=b4,v.unionWith=C4,v.uniq=w4,v.uniqBy=E4,v.uniqWith=O4,v.unset=S8,v.unzip=vc,v.unzipWith=ig,v.update=D8,v.updateWith=T8,v.values=no,v.valuesIn=N8,v.without=x4,v.words=Ag,v.wrap=wA,v.xor=S4,v.xorBy=D4,v.xorWith=T4,v.zip=N4,v.zipObject=I4,v.zipObjectDeep=A4,v.zipWith=M4,v.entries=Dg,v.entriesIn=Tg,v.extend=Og,v.extendWith=Za,Tc(v,v),v.add=N6,v.attempt=Mg,v.camelCase=P8,v.capitalize=Ng,v.ceil=I6,v.clamp=I8,v.clone=OA,v.cloneDeep=SA,v.cloneDeepWith=DA,v.cloneWith=xA,v.conformsTo=TA,v.deburr=Ig,v.defaultTo=l6,v.divide=A6,v.endsWith=k8,v.eq=Bs,v.escape=V8,v.escapeRegExp=R8,v.every=H4,v.find=j4,v.findIndex=tg,v.findKey=n8,v.findLast=z4,v.findLastIndex=sg,v.findLastKey=o8,v.floor=M6,v.forEach=lg,v.forEachRight=ug,v.forIn=i8,v.forInRight=a8,v.forOwn=l8,v.forOwnRight=u8,v.get=Ec,v.gt=NA,v.gte=IA,v.has=f8,v.hasIn=Oc,v.head=ng,v.identity=os,v.includes=J4,v.indexOf=z5,v.inRange=A8,v.invoke=m8,v.isArguments=Sn,v.isArray=Re,v.isArrayBuffer=AA,v.isArrayLike=rs,v.isArrayLikeObject=bt,v.isBoolean=MA,v.isBuffer=Zr,v.isDate=PA,v.isElement=kA,v.isEmpty=VA,v.isEqual=RA,v.isEqualWith=FA,v.isError=Cc,v.isFinite=LA,v.isFunction=Er,v.isInteger=_g,v.isLength=Ga,v.isMap=vg,v.isMatch=UA,v.isMatchWith=BA,v.isNaN=$A,v.isNative=qA,v.isNil=WA,v.isNull=HA,v.isNumber=yg,v.isObject=gt,v.isObjectLike=_t,v.isPlainObject=ii,v.isRegExp=wc,v.isSafeInteger=jA,v.isSet=bg,v.isString=Ka,v.isSymbol=hs,v.isTypedArray=ro,v.isUndefined=zA,v.isWeakMap=GA,v.isWeakSet=KA,v.join=J5,v.kebabCase=F8,v.last=Ss,v.lastIndexOf=Q5,v.lowerCase=L8,v.lowerFirst=U8,v.lt=ZA,v.lte=YA,v.max=P6,v.maxBy=k6,v.mean=V6,v.meanBy=R6,v.min=F6,v.minBy=L6,v.stubArray=Ic,v.stubFalse=Ac,v.stubObject=E6,v.stubString=O6,v.stubTrue=x6,v.multiply=U6,v.nth=X5,v.noConflict=m6,v.noop=Nc,v.now=Wa,v.pad=B8,v.padEnd=$8,v.padStart=q8,v.parseInt=H8,v.random=M8,v.reduce=sA,v.reduceRight=rA,v.repeat=W8,v.replace=j8,v.result=w8,v.round=B6,v.runInContext=T,v.sample=oA,v.size=lA,v.snakeCase=z8,v.some=uA,v.sortedIndex=i4,v.sortedIndexBy=a4,v.sortedIndexOf=l4,v.sortedLastIndex=u4,v.sortedLastIndexBy=c4,v.sortedLastIndexOf=d4,v.startCase=K8,v.startsWith=Z8,v.subtract=$6,v.sum=q6,v.sumBy=H6,v.template=Y8,v.times=S6,v.toFinite=Or,v.toInteger=Le,v.toLength=wg,v.toLower=J8,v.toNumber=Ds,v.toSafeInteger=JA,v.toString=st,v.toUpper=Q8,v.trim=X8,v.trimEnd=e6,v.trimStart=t6,v.truncate=s6,v.unescape=r6,v.uniqueId=T6,v.upperCase=n6,v.upperFirst=xc,v.each=lg,v.eachRight=ug,v.first=ng,Tc(v,function(){var r={};return er(v,function(o,l){nt.call(v.prototype,l)||(r[l]=o)}),r}(),{chain:!1}),v.VERSION=i,Cs(["bind","bindKey","curry","curryRight","partial","partialRight"],function(r){v[r].placeholder=v}),Cs(["drop","take"],function(r,o){je.prototype[r]=function(l){l=l===s?1:At(Le(l),0);var d=this.__filtered__&&!o?new je(this):this.clone();return d.__filtered__?d.__takeCount__=qt(l,d.__takeCount__):d.__views__.push({size:qt(l,Tt),type:r+(d.__dir__<0?"Right":"")}),d},je.prototype[r+"Right"]=function(l){return this.reverse()[r](l).reverse()}}),Cs(["filter","map","takeWhile"],function(r,o){var l=o+1,d=l==mt||l==ot;je.prototype[r]=function(m){var y=this.clone();return y.__iteratees__.push({iteratee:Oe(m,3),type:l}),y.__filtered__=y.__filtered__||d,y}}),Cs(["head","last"],function(r,o){var l="take"+(o?"Right":"");je.prototype[r]=function(){return this[l](1).value()[0]}}),Cs(["initial","tail"],function(r,o){var l="drop"+(o?"":"Right");je.prototype[r]=function(){return this.__filtered__?new je(this):this[l](1)}}),je.prototype.compact=function(){return this.filter(os)},je.prototype.find=function(r){return this.filter(r).head()},je.prototype.findLast=function(r){return this.reverse().find(r)},je.prototype.invokeMap=qe(function(r,o){return typeof r=="function"?new je(this):this.map(function(l){return ei(l,r,o)})}),je.prototype.reject=function(r){return this.filter(za(Oe(r)))},je.prototype.slice=function(r,o){r=Le(r);var l=this;return l.__filtered__&&(r>0||o<0)?new je(l):(r<0?l=l.takeRight(-r):r&&(l=l.drop(r)),o!==s&&(o=Le(o),l=o<0?l.dropRight(-o):l.take(o-r)),l)},je.prototype.takeRightWhile=function(r){return this.reverse().takeWhile(r).reverse()},je.prototype.toArray=function(){return this.take(Tt)},er(je.prototype,function(r,o){var l=/^(?:filter|find|map|reject)|While$/.test(o),d=/^(?:head|last)$/.test(o),m=v[d?"take"+(o=="last"?"Right":""):o],y=d||/^find/.test(o);m&&(v.prototype[o]=function(){var E=this.__wrapped__,S=d?[1]:arguments,N=E instanceof je,U=S[0],B=N||Re(E),W=function(He){var Ge=m.apply(v,qr([He],S));return d&&ae?Ge[0]:Ge};B&&l&&typeof U=="function"&&U.length!=1&&(N=B=!1);var ae=this.__chain__,_e=!!this.__actions__.length,Se=y&&!ae,Be=N&&!_e;if(!y&&B){E=Be?E:new je(this);var De=r.apply(E,S);return De.__actions__.push({func:qa,args:[W],thisArg:s}),new Es(De,ae)}return Se&&Be?r.apply(this,S):(De=this.thru(W),Se?d?De.value()[0]:De.value():De)})}),Cs(["pop","push","shift","sort","splice","unshift"],function(r){var o=pa[r],l=/^(?:push|sort|unshift)$/.test(r)?"tap":"thru",d=/^(?:pop|shift)$/.test(r);v.prototype[r]=function(){var m=arguments;if(d&&!this.__chain__){var y=this.value();return o.apply(Re(y)?y:[],m)}return this[l](function(E){return o.apply(Re(E)?E:[],m)})}}),er(je.prototype,function(r,o){var l=v[o];if(l){var d=l.name+"";nt.call(Qn,d)||(Qn[d]=[]),Qn[d].push({name:o,func:l})}}),Qn[Va(s,Q).name]=[{name:"wrapper",func:s}],je.prototype.clone=rI,je.prototype.reverse=nI,je.prototype.value=oI,v.prototype.at=k4,v.prototype.chain=V4,v.prototype.commit=R4,v.prototype.next=F4,v.prototype.plant=U4,v.prototype.reverse=B4,v.prototype.toJSON=v.prototype.valueOf=v.prototype.value=$4,v.prototype.first=v.prototype.head,Go&&(v.prototype[Go]=L4),v},Zn=RN();vn?((vn.exports=Zn)._=Zn,Du._=Zn):Ut._=Zn}).call(qo)}(Xi,Xi.exports);var Ur=Xi.exports;const Ue=async(e,t)=>{const s={methodname:e,args:Object.assign({},t)};try{return await Rg.call([s])[0]}catch(i){throw Fg.exception(i),i}};async function Yy(e={}){try{return await Ue("local_offermanager_fetch",{search_string:e.search||"",type:e.type||null,only_active:e.onlyActive===!0,page:e.page||1,per_page:e.perPage||25,sort_by:e.sortBy||"name",sort_direction:e.sortDesc?"DESC":"ASC"})}catch(t){throw t}}async function Jy(e){try{return await Ue("local_offermanager_get",{id:e})}catch(t){throw t}}async function Qh(e){try{return await Ue("local_offermanager_save",{id:e.id||0,name:e.name,description:e.description||"",type:e.type||"",status:e.status||0,audienceids:e.audiences||[]})}catch(t){throw t}}async function Qy(e){try{return await Ue("local_offermanager_delete",{id:e})}catch(t){throw t}}async function Xy(){try{return await Ue("local_offermanager_get_type_options",{})}catch(e){throw e}}async function eb(e,t){try{return await Ue("local_offermanager_delete_course",{offercourseid:t})}catch(s){throw s}}async function tb(e,t,s){try{return await Ue("local_offermanager_set_course_status",{id:t,status:s?1:0})}catch(i){throw i}}async function sb(e){try{return(await Ue("local_offermanager_get_audiences",{offerid:0})).all_audiences.filter(i=>i.name.toLowerCase().includes(e.toLowerCase())).map(i=>({id:i.id,name:i.name}))}catch(t){throw t}}async function rb(e,t){try{return await Ue("local_offermanager_set_status",{id:e,status:!t})}catch(s){throw s}}async function hu(e="",t=0){try{return await Ue("local_offermanager_get_categories",{search_string:e,offerid:t})}catch(s){throw s}}async function nb(e,t,s="",i=1,n=20){try{return await Ue("local_offermanager_fetch_potential_courses",{offerid:e,categoryid:t,search_string:s||"",page:i,per_page:n,exclude_courseids:[]})}catch(a){throw a}}async function ob(e,t,s="",i=[],n=!1){try{return await Ue("local_offermanager_fetch_current_courses",{offerid:e,categoryid:t,search_string:s,exclude_courseids:i||[],only_active:n})}catch(a){throw a}}async function ib(e,t){try{return await Ue("local_offermanager_add_courses",{offerid:e,courseids:t})}catch(s){throw s}}async function Xh(e,t={}){try{return t.sortBy==="name"&&(t.sortBy="fullname"),t.sortBy==="courseClassCount"&&(t.sortBy="class_counter"),await Ue("local_offermanager_get_current_courses",{offerid:e,only_active:t.onlyActive||!1,courseids:t.courseIds||[],page:t.page||1,per_page:t.perPage||100,sort_by:t.sortBy||"id",sort_direction:t.sortDesc?"DESC":"ASC",course_search:t.courseSearch||"",category_search:t.categorySearch||""})}catch(s){throw s}}async function ab(e){try{const t=["enableenddate","enddate","enablepreenrolment","preenrolmentstartdate","preenrolmentenddate","description","enableenrolperiod","enrolperiod","minusers","maxusers","roleid","enablereenrol","reenrolmentsituations","enableextension","extensionperiod","extensiondaysavailable","extensionmaxrequests"],s={optional_fields:{}};s.classname=e.classname,s.startdate=e.startdate,s.offercourseid=parseInt(e.offercourseid),s.teachers=[...e.teachers],s.enrol=e.enrol,e.optional_fields&&t.forEach(u=>{if(u in e.optional_fields){const c=e.optional_fields[u];["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].includes(u)?c!==0&&c!==null&&c!==void 0&&c!==""&&(s.optional_fields[u]=c):typeof c=="boolean"?s.optional_fields[u]=c:Array.isArray(c)?c.length>0&&(s.optional_fields[u]=c):c!=null&&c!==""&&(s.optional_fields[u]=c)}});const n=["offercourseid","classname","startdate","enrol"].filter(u=>!s[u]);if(n.length>0)throw console.error("Campos obrigatórios ausentes no serviço:",n),new Error(`Campos obrigatórios ausentes: ${n.join(", ")}`);return await Ue("local_offermanager_add_class",s)}catch(t){throw console.error("Erro ao criar turma:",t),t}}async function pu(e){try{return await Ue("local_offermanager_get_class",{offerclassid:e})}catch(t){throw t}}async function lb(e){try{return await Ue("local_offermanager_get_course",{offercourseid:e})}catch(t){throw t}}async function ub(e){try{return await Ue("local_offermanager_get_classes",{offercourseid:e})}catch(t){throw console.error("Error fetching:",t),t}}async function cb(e){try{const t=["enableenddate","enddate","enablepreenrolment","preenrolmentstartdate","preenrolmentenddate","description","enableenrolperiod","enrolperiod","minusers","maxusers","roleid","enablereenrol","reenrolmentsituations","enableextension","extensionperiod","extensiondaysavailable","extensionmaxrequests","enablehirearchyrestriction","hirearchyrestrictiondivisions","hirearchyrestrictionsectors","hirearchyrestrictiongroups","hirearchyrestrictiondealerships","modality"],s={offerclassid:e.offerclassid,classname:e.classname,startdate:e.startdate,teachers:e.teachers,optional_fields:{}};return t.forEach(n=>{n in e.optional_fields&&(s.optional_fields[n]=e.optional_fields[n])}),"enrol"in s&&delete s.enrol,await Ue("local_offermanager_update_class",s)}catch(t){throw t}}async function db(e){try{return await Ue("local_offermanager_delete_class",{offerclassid:e})}catch(t){throw t}}async function ep(e,t=0,s="",i=[]){try{return await Ue("local_offermanager_get_potential_teachers",{offercourseid:e,search_string:s,offerclassid:t,excluded_userids:i})}catch(n){throw n}}async function fb(){try{return await Ue("local_offermanager_get_situation_list",{})}catch(e){throw e}}async function hb(e,t){try{if(!t)throw new Error("É necessário especificar um curso de destino para duplicar a turma");const s=parseInt(e,10),i=parseInt(t,10);if(isNaN(s)||isNaN(i))throw new Error("IDs inválidos para duplicação de turma");return await Ue("local_offermanager_duplicate_class",{offerclassid:s,targetoffercourseid:i})}catch(s){throw s}}async function pb(e){try{return await Ue("local_offermanager_get_duplication_courses",{offerclassid:e})}catch(t){throw t}}async function mu(e){try{return await Ue("local_offermanager_get_course_roles",{offercourseid:e})}catch(t){throw t}}async function mb(e=!0){try{return await Ue("local_offermanager_get_class_methods",{enabled:e})}catch(t){throw t}}async function gb(e,t){try{return await Ue("local_offermanager_set_class_status",{id:e,status:t?1:0})}catch(s){throw s}}async function _b(){try{return await Ue("local_offermanager_get_hierarchy_divisions",{})}catch(e){throw e}}async function vb(e){try{return await Ue("local_offermanager_get_hierarchy_sectors",{divisionids:e})}catch(t){throw t}}async function yb(e){try{return await Ue("local_offermanager_get_hierarchy_groups",{sectorids:e})}catch(t){throw t}}async function bb(e){try{return await Ue("local_offermanager_get_hierarchy_dealerships",{groupids:e})}catch(t){throw t}}const K6="",Cb={name:"CustomTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1}},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})}}},wb={class:"table-responsive"},Eb={class:"table"},Ob=["data-value"],xb=["onClick"],Sb=["data-column"];function Db(e,t,s,i,n,a){return x(),D("div",wb,[f("table",Eb,[f("thead",null,[f("tr",null,[(x(!0),D(Ne,null,it(s.headers,u=>(x(),D("th",{key:u.value,class:de({"text-right":u.align==="right"}),style:is(u.width?{width:u.width}:{}),"data-value":u.value},[u.value==="select"?Mt(e.$slots,"header-select",{key:0},()=>[We(q(u.text),1)],!0):(x(),D(Ne,{key:1},[We(q(u.text)+" ",1),u.sortable?(x(),D("span",{key:0,onClick:c=>u.sortable?a.handleSort(u.value):null,class:"sort-icon"},[f("i",{class:de(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)],8,xb)):Z("",!0)],64))],14,Ob))),128))])]),f("tbody",null,[(x(!0),D(Ne,null,it(s.items,u=>(x(),D("tr",{key:u.id},[(x(!0),D(Ne,null,it(s.headers,c=>(x(),D("td",{key:c.value,class:de({"text-right":c.align==="right"}),"data-column":c.value},[Mt(e.$slots,"item-"+c.value,{item:u},()=>[We(q(u[c.value]),1)],!0)],10,Sb))),128))]))),128))])])])}const mn=Pe(Cb,[["render",Db],["__scopeId","data-v-4ad20657"]]),Z6="",Tb={name:"CustomSelect",props:{modelValue:{type:[Number,String],default:""},options:{type:Array,required:!0},label:{type:String,default:""},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}}},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate"]},Nb={class:"select-wrapper"},Ib=["value","disabled"],Ab=["value"],Mb={key:1,class:"error-message"};function Pb(e,t,s,i,n,a){return x(),D("div",{ref:"selectContainer",class:"custom-select-container",style:is(a.customWidth)},[s.label?(x(),D("div",{key:0,class:de(["select-label",{disabled:s.disabled}])},q(s.label),3)):Z("",!0),f("div",Nb,[f("select",{value:s.modelValue,onChange:t[0]||(t[0]=(...u)=>a.handleChange&&a.handleChange(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),class:de(["form-control",{error:s.hasError}]),disabled:s.disabled},[(x(!0),D(Ne,null,it(s.options,u=>(x(),D("option",{key:u.value,value:u.value},q(u.label),9,Ab))),128))],42,Ib)]),s.hasError&&s.errorMessage?(x(),D("div",Mb,q(s.errorMessage),1)):Z("",!0)],4)}const Qs=Pe(Tb,[["render",Pb],["__scopeId","data-v-7563f1a9"]]),Y6="",kb={name:"CustomInput",props:{modelValue:{type:[String,Number],default:""},id:{type:String,required:!1,default:"custom-input-"+Math.random().toString(36).substring(2,9)},label:{type:String,default:""},placeholder:{type:String,default:"Digite aqui..."},type:{type:String,default:"text"},hasSearchIcon:{type:Boolean,default:!1},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1},max:{type:[String,Number],default:null}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}},isDateType(){return this.type==="date"},isNumberType(){return this.type==="number"}},methods:{handleInput(e){let t=e.target.value;if(this.isNumberType&&(t.includes("-")&&(t=t.replace(/-/g,""),e.target.value=t),t!=="")){const s=parseFloat(t);s<0||isNaN(s)?(t="",e.target.value=t):this.max!==null&&s>parseFloat(this.max)&&(t=this.max.toString(),e.target.value=t,this.$emit("validate"))}this.$emit("update:modelValue",t),this.hasError&&t&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate"]},Vb={key:0,class:"input-label"},Rb=["type","placeholder","value","disabled","min","max","id"],Fb={key:0,class:"search-icon"},Lb={key:2,class:"error-message"};function Ub(e,t,s,i,n,a){return x(),D("div",{class:"custom-input-container",style:is(a.customWidth)},[s.label?(x(),D("div",Vb,q(s.label),1)):Z("",!0),f("div",{class:de(["input-wrapper",{"with-icon":s.hasSearchIcon||a.isDateType}])},[f("input",{type:s.type,placeholder:s.placeholder,value:s.modelValue,onInput:t[0]||(t[0]=(...u)=>a.handleInput&&a.handleInput(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),disabled:s.disabled,class:de(["form-control custom-input",{error:s.hasError}]),min:a.isNumberType?0:null,max:s.max,id:s.id},null,42,Rb),s.hasSearchIcon?(x(),D("div",Fb,t[2]||(t[2]=[f("i",{class:"fas fa-search"},null,-1)]))):Z("",!0),a.isDateType?(x(),D("div",{key:1,class:de(["calendar-icon",{disabled:s.disabled}])},t[3]||(t[3]=[f("i",{class:"fas fa-calendar-alt"},null,-1)]),2)):Z("",!0),s.hasError&&s.errorMessage?(x(),D("div",Lb,q(s.errorMessage),1)):Z("",!0)],2)],4)}const qn=Pe(kb,[["render",Ub],["__scopeId","data-v-d592becf"]]),J6="",Bb={name:"HelpIcon",props:{title:{type:String,default:"Ajuda"},text:{type:String,required:!0},postion:{type:String,default:"right"}},computed:{content(){return`<div class="no-overflow"><p class="mb-0">${this.text}</p></div>`}}},$b=["data-content","aria-label"],qb=["title","aria-label"];function Hb(e,t,s,i,n,a){return x(),D("a",{class:"btn btn-link p-0","data-container":"body","data-toggle":"popover","data-placement":"auto","data-content":a.content,"data-html":"true",tabindex:"0","data-trigger":"focus","aria-label":s.title,role:"button"},[f("i",{class:"icon fa fa-question-circle text-info fa-fw",title:s.title,"aria-label":s.title,role:"img"},null,8,qb)],8,$b)}const ea=Pe(Bb,[["render",Hb],["__scopeId","data-v-6eb219ea"]]),Q6="",Wb={name:"CustomCheckbox",components:{HelpIcon:ea},props:{modelValue:{type:Boolean,default:!1},label:{type:String,default:""},help:{type:String,default:""},id:{type:String,required:!0},disabled:{type:Boolean,default:!1},confirmBeforeChange:{type:Boolean,default:!1}},emits:["update:modelValue","request-change"],methods:{handleClick(e){const t=!this.modelValue;this.confirmBeforeChange?this.$emit("request-change",t):this.$emit("update:modelValue",t)}}},jb=["id","checked","disabled"],zb=["for"];function Gb(e,t,s,i,n,a){const u=z("HelpIcon");return x(),D("div",{class:de(["checkbox-container",{disabled:s.disabled}])},[(x(),D("input",{type:"checkbox",id:s.id,key:s.modelValue,checked:s.modelValue,onClick:t[0]||(t[0]=Pt((...c)=>a.handleClick&&a.handleClick(...c),["prevent"])),class:"custom-checkbox",disabled:s.disabled},null,8,jb)),f("label",{for:s.id,class:de(["checkbox-label",{disabled:s.disabled}])},[Mt(e.$slots,"default",{},()=>[We(q(s.label),1)],!0)],10,zb),s.help?(x(),dt(u,{key:0,title:`Ajuda com ${s.label.toLowerCase()}`,text:s.help},null,8,["title","text"])):Z("",!0)],2)}const Ho=Pe(Wb,[["render",Gb],["__scopeId","data-v-3ece3f9b"]]),X6="",Kb={name:"CustomButton",props:{variant:{type:String,default:"primary",validator:e=>["primary","secondary","success","danger","warning","info"].includes(e)},label:{type:String,default:""},icon:{type:String,default:""},disabled:{type:Boolean,default:!1},isLoading:{type:Boolean,default:!1}},emits:["click"]},Zb=["disabled"],Yb={key:1,class:"spinner-border spinner-border-sm"},Jb={key:2};function Qb(e,t,s,i,n,a){return x(),D("button",{class:de(["btn custom-button",[`btn-${s.variant}`]]),disabled:s.disabled||s.isLoading,onClick:t[0]||(t[0]=u=>e.$emit("click",u))},[s.icon?(x(),D("i",{key:0,class:de(s.icon)},null,2)):Z("",!0),s.isLoading?(x(),D("i",Yb)):Z("",!0),s.label?(x(),D("span",Jb,q(s.label),1)):Z("",!0),Mt(e.$slots,"default",{},void 0,!0)],10,Zb)}const mr=Pe(Kb,[["render",Qb],["__scopeId","data-v-482c6327"]]),eM="",Xb={name:"FilterSection",props:{title:{type:String,default:"FILTRO"},hasActiveTags:{type:Boolean,default:!1}}},eC={class:"filter-section"},tC={key:0},sC={class:"filter-content"},rC={key:1,class:"filter-tags"};function nC(e,t,s,i,n,a){return x(),D("div",eC,[s.title?(x(),D("h2",tC,q(s.title),1)):Z("",!0),f("div",sC,[Mt(e.$slots,"default",{},void 0,!0)]),s.hasActiveTags?(x(),D("div",rC,[Mt(e.$slots,"tags",{},void 0,!0)])):Z("",!0)])}const tp=Pe(Xb,[["render",nC],["__scopeId","data-v-1ece8e84"]]),tM="",oC={name:"FilterRow",props:{inline:{type:Boolean,default:!1}}};function iC(e,t,s,i,n,a){return x(),D("div",{class:de(["filter-row",{"filter-row-inline":s.inline}])},[Mt(e.$slots,"default",{},void 0,!0)],2)}const ta=Pe(oC,[["render",iC],["__scopeId","data-v-83bdb425"]]),sM="",aC={name:"FilterGroup",props:{label:{type:String,default:""},isCheckbox:{type:Boolean,default:!1}}},lC={key:0,class:"filter-label"},uC={class:"filter-input"};function cC(e,t,s,i,n,a){return x(),D("div",{class:de(["filter-group",{"checkbox-group":s.isCheckbox}])},[s.label?(x(),D("div",lC,q(s.label),1)):Z("",!0),f("div",uC,[Mt(e.$slots,"default",{},void 0,!0)])],2)}const sa=Pe(aC,[["render",cC],["__scopeId","data-v-d7bf1926"]]),rM="",dC={name:"FilterActions"},fC={class:"filter-actions"};function hC(e,t,s,i,n,a){return x(),D("div",fC,[Mt(e.$slots,"default",{},void 0,!0)])}const sp=Pe(dC,[["render",hC],["__scopeId","data-v-68346c90"]]),nM="",pC={name:"LFLoading",props:{isLoading:{type:Boolean,default:!1}}},mC={key:0};function gC(e,t,s,i,n,a){return x(),dt(Vf,null,{default:Te(()=>[s.isLoading?(x(),D("div",mC,t[0]||(t[0]=[f("div",{class:"modal-overlay"},null,-1),f("div",{class:"loader-wrapper"},[f("span",{class:"loader",role:"status"},[f("span",{class:"sr-only"},"Carregando...")])],-1)]))):Z("",!0)]),_:1})}const ra=Pe(pC,[["render",gC],["__scopeId","data-v-b3cb5b4c"]]),oM="",_C={name:"Toast",props:{show:{type:Boolean,required:!0},message:{type:String,required:!0},type:{type:String,default:"success",validator:function(e){return["success","error","warning","info"].includes(e)}},duration:{type:Number,default:3e3}},computed:{icon(){return{success:"fas fa-check-circle",error:"fas fa-exclamation-circle",warning:"fas fa-exclamation-triangle",info:"fas fa-info-circle"}[this.type]},progressStyle(){return{animation:`progress ${this.duration}ms linear`}}}},vC={class:"toast-content"};function yC(e,t,s,i,n,a){return x(),dt(Q_,{to:"body"},[A(Vf,{name:"toast"},{default:Te(()=>[s.show?(x(),D("div",{key:0,class:de(["toast",s.type])},[f("div",vC,[f("i",{class:de(a.icon)},null,2),f("span",null,q(s.message),1)]),f("div",{class:"toast-progress",style:is(a.progressStyle)},null,4)],2)):Z("",!0)]),_:1})])}const Br=Pe(_C,[["render",yC],["__scopeId","data-v-4440998c"]]),iM="",bC={name:"Pagination",props:{currentPage:{type:Number,required:!0},perPage:{type:Number,required:!0},total:{type:Number,required:!0},perPageOptions:{type:Array,default:()=>[5,10,25,50,100]}},emits:["update:currentPage","update:perPage"],computed:{totalPages(){return Math.ceil(this.total/this.perPage)},visiblePages(){const t=Math.floor(2.5);let s=Math.max(1,this.currentPage-t),i=Math.min(this.totalPages,s+5-1);i-s+1<5&&(s=Math.max(1,i-5+1));const n=[];for(let a=s;a<=i;a++)n.push(a);return n},from(){return this.total===0?0:(this.currentPage-1)*this.perPage+1},to(){return Math.min(this.from+this.perPage-1,this.total)},perPageModel:{get(){return this.perPage},set(e){this.$emit("update:perPage",e)}}},methods:{handlePageChange(e){e>=1&&e<=this.totalPages&&this.$emit("update:currentPage",e)},handlePerPageChange(){this.$emit("update:currentPage",1)}}},CC={class:"pagination-container mt-3"},wC={class:"pagination-info"},EC=["value"],OC={class:"pagination-text"},xC={class:"pagination-controls"},SC=["disabled"],DC=["onClick"],TC=["disabled"];function NC(e,t,s,i,n,a){return x(),D("div",CC,[f("div",wC,[lt(f("select",{"onUpdate:modelValue":t[0]||(t[0]=u=>a.perPageModel=u),class:"per-page-select",onChange:t[1]||(t[1]=(...u)=>a.handlePerPageChange&&a.handlePerPageChange(...u))},[(x(!0),D(Ne,null,it(s.perPageOptions,u=>(x(),D("option",{key:u,value:u},q(u),9,EC))),128))],544),[[Jl,a.perPageModel]]),f("span",OC," Mostrando de "+q(a.from)+" até "+q(a.to)+" de "+q(s.total)+" resultados ",1)]),f("div",xC,[f("button",{class:"page-item",disabled:s.currentPage<=1,onClick:t[2]||(t[2]=u=>a.handlePageChange(s.currentPage-1))},t[4]||(t[4]=[f("i",{class:"fas fa-chevron-left"},null,-1)]),8,SC),(x(!0),D(Ne,null,it(a.visiblePages,u=>(x(),D("button",{key:u,class:de(["page-item",{active:u===s.currentPage}]),onClick:c=>a.handlePageChange(u)},q(u),11,DC))),128)),f("button",{class:"page-item",disabled:s.currentPage>=a.totalPages,onClick:t[3]||(t[3]=u=>a.handlePageChange(s.currentPage+1))},t[5]||(t[5]=[f("i",{class:"fas fa-chevron-right"},null,-1)]),8,TC)])])}const gn=Pe(bC,[["render",NC],["__scopeId","data-v-b3aa038d"]]),aM="",IC={name:"PageHeader",props:{title:{type:String,required:!0}}},AC={class:"page-header"},MC={class:"header-actions"};function PC(e,t,s,i,n,a){return x(),D("div",AC,[f("h2",null,q(s.title),1),f("div",MC,[Mt(e.$slots,"actions",{},void 0,!0)])])}const Hn=Pe(IC,[["render",PC],["__scopeId","data-v-5d6d687f"]]),lM="",kC={name:"Modal",components:{CustomButton:mr},props:{show:{type:Boolean,default:!1},size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},showDefaultFooter:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1}},emits:["close","confirm"],mounted(){document.addEventListener("keydown",this.handleKeyDown),this.show&&(document.body.style.overflow="hidden")},unmounted(){document.removeEventListener("keydown",this.handleKeyDown),document.body.style.overflow=""},watch:{show(e){document.body.style.overflow=e?"hidden":""}},methods:{handleKeyDown(e){this.show&&e.key==="Escape"&&this.$emit("close")}}},VC={class:"modal-body"},RC={key:0,class:"modal-footer"},FC={key:1,class:"modal-footer"};function LC(e,t,s,i,n,a){const u=z("custom-button");return s.show?(x(),D("div",{key:0,class:"modal-backdrop",onClick:t[3]||(t[3]=c=>s.closeOnBackdrop?e.$emit("close"):null)},[f("div",{class:de(["modal-container",[`modal-${s.size}`]]),onClick:t[2]||(t[2]=Pt(()=>{},["stop"]))},[f("div",VC,[Mt(e.$slots,"default",{},void 0,!0)]),e.$slots.footer?(x(),D("div",RC,[Mt(e.$slots,"footer",{},void 0,!0)])):s.showDefaultFooter?(x(),D("div",FC,[A(u,{variant:"secondary",label:s.cancelButtonText,onClick:t[0]||(t[0]=c=>e.$emit("close"))},null,8,["label"]),A(u,{variant:"primary",label:s.confirmButtonText,onClick:t[1]||(t[1]=c=>e.$emit("confirm")),disabled:s.confirmDisabled},null,8,["label","disabled"])])):Z("",!0)],2)])):Z("",!0)}const UC=Pe(kC,[["render",LC],["__scopeId","data-v-784205f2"]]),uM="",BC={name:"ConfirmationModal",components:{Modal:UC},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Confirmação"},message:{type:String,default:""},listTitle:{type:String,default:""},listItems:{type:Array,default:()=>[]},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1},icon:{type:String,default:"warning",validator:e=>["warning","info","error","success","question",""].includes(e)},size:{type:String,default:"sm"}},emits:["close","confirm"],computed:{iconClass(){return{warning:"fas fa-exclamation-triangle text-warning",info:"fas fa-info-circle text-info",error:"fas fa-times-circle text-danger",success:"fas fa-check-circle text-success",question:"fas fa-question-circle text-primary"}[this.icon]||""},hasListContent(){return this.listItems&&this.listItems.length>0}}},$C={key:0,class:"icon-container"},qC={class:"modal-custom-title"},HC={key:1,class:"message-list"},WC={key:0,class:"list-title"},jC={key:2,class:"message"},zC={class:"modal-custom-footer"},GC=["disabled"];function KC(e,t,s,i,n,a){const u=z("modal");return x(),dt(u,{show:s.show,"confirm-button-text":s.confirmButtonText,"cancel-button-text":s.cancelButtonText,"confirm-disabled":s.confirmDisabled,size:s.size,"show-default-footer":!1,onClose:t[2]||(t[2]=c=>e.$emit("close")),onConfirm:t[3]||(t[3]=c=>e.$emit("confirm"))},{default:Te(()=>[f("div",{class:de(["confirmation-content",{"has-list":a.hasListContent}])},[s.icon?(x(),D("div",$C,[f("i",{class:de(a.iconClass)},null,2)])):Z("",!0),f("h3",qC,q(s.title),1),a.hasListContent?(x(),D("div",HC,[s.listTitle?(x(),D("p",WC,q(s.listTitle),1)):Z("",!0),f("ul",null,[(x(!0),D(Ne,null,it(s.listItems,(c,h)=>(x(),D("li",{key:h},q(c),1))),128))])])):(x(),D("div",jC,q(s.message),1)),f("div",zC,[f("button",{class:"btn-cancel",onClick:t[0]||(t[0]=c=>e.$emit("close"))},q(s.cancelButtonText),1),f("button",{class:"btn-danger",disabled:s.confirmDisabled,onClick:t[1]||(t[1]=c=>e.$emit("confirm"))},q(s.confirmButtonText),9,GC)])],2)]),_:1},8,["show","confirm-button-text","cancel-button-text","confirm-disabled","size"])}const na=Pe(BC,[["render",KC],["__scopeId","data-v-2e1eb4fd"]]),cM="",dM="",ZC={name:"OfferList",components:{CustomTable:mn,CustomSelect:Qs,CustomInput:qn,CustomCheckbox:Ho,CustomButton:mr,FilterSection:tp,FilterRow:ta,FilterGroup:sa,FilterActions:sp,Pagination:gn,PageHeader:Hn,ConfirmationModal:na,LFLoading:ra,Toast:Br},setup(){return{router:Jh()}},mounted(){if(!document.querySelector('link[href*="font-awesome"]')){const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)}},data(){return{inputFilters:{search:"",type:"",hideInactive:!1},appliedFilters:{search:"",type:"",hideInactive:!1},typeOptions:[],typeOptionsEnabled:!1,tableHeaders:[{text:"OFERTA",value:"name",sortable:!0},{text:"STATUS",value:"status",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}],offers:[],totalOffers:0,loading:!1,error:null,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,currentPage:1,perPage:10,sortBy:"name",sortDesc:!1,showDeleteModal:!1,offerToDelete:null,showStatusModal:!1,selectedOffer:null}},computed:{typeSelectOptions(){return[{value:"",label:"Todos"},...this.typeOptions]},hasActiveFilters(){return this.appliedFilters.search||this.appliedFilters.hideInactive}},watch:{perPage(e,t){e!==t&&(this.currentPage=1,this.loadOffers())},currentPage(e,t){e!==t&&this.loadOffers()}},async created(){this.debouncedSearch=Ur.debounce(this.handleSearchInput,300),this.loadTypeOptions(),this.loadOffers()},methods:{getTypeLabel(e){if(!e)return"";const t=this.typeOptions.find(s=>s.value===e||s.label===e);return t?t.label:e},async loadTypeOptions(){const e=await Xy();e.types&&(this.typeOptionsEnabled=e.enabled,e.default&&(this.inputFilters.type=e.default),this.typeOptions=e.types.map(t=>({value:t,label:t})))},async loadOffers(){try{this.loading=!0,this.error=null;const e={search:this.appliedFilters.search||"",type:this.appliedFilters.type||null,onlyActive:this.appliedFilters.hideInactive===!0,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc},t=await Yy(e);this.offers=t.offers||[],this.totalOffers=t.total_items||0}catch(e){this.error=e.message}finally{this.loading=!1}},async handlePageChange(e){e!==this.currentPage&&(this.currentPage=e,await this.loadOffers())},async handlePerPageChange(e){e!==this.perPage&&(this.perPage=e,this.currentPage=1,await this.loadOffers())},async clearFilters(){this.inputFilters.type,this.inputFilters={search:"",type:"",hideInactive:!1},this.appliedFilters={search:"",type:"",hideInactive:!1},this.currentPage=1,await this.loadOffers()},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.loadOffers()},createNewOffer(){this.router.push({name:"offer.create"})},navigateToEditOffer(e){this.router.push({name:"offer.edit",params:{id:e.id.toString()}})},navigateToShowOffer(e){this.router.push({name:"offer.show",params:{id:e.id.toString()}})},deleteOffer(e){e.can_delete&&(this.offerToDelete=e,this.showDeleteModal=!0)},async confirmDeleteOffer(){if(this.offerToDelete)try{this.loading=!0,await Qy(this.offerToDelete.id),await this.loadOffers(),this.showSuccessMessage(`Oferta "${this.offerToDelete.name}" excluída com sucesso`),this.offerToDelete=null,this.showDeleteModal=!1}catch(e){this.error=e.message,this.showErrorMessage(`Erro ao excluir oferta "${this.offerToDelete.name}"`)}finally{this.loading=!1}},toggleOfferStatus(e){e.status===0&&!e.can_activate||(this.selectedOffer=e,this.showStatusModal=!0)},async confirmToggleStatus(){if(this.selectedOffer)try{this.loading=!0,await rb(this.selectedOffer.id,this.selectedOffer.status===1),await this.loadOffers(),this.showSuccessMessage(this.selectedOffer.status===1?`Oferta "${this.selectedOffer.name}" inativada com sucesso`:`Oferta "${this.selectedOffer.name}" inativada com sucesso`),this.selectedOffer=null,this.showStatusModal=!1}catch(e){this.error=e.message,this.showErrorMessage(this.selectedOffer.status===1?`Erro ao inativar oferta "${this.selectedOffer.name}"`:`Erro ao ativar oferta "${this.selectedOffer.name}"`)}finally{this.loading=!1}},getStatusButtonTitle(e){return e.status===1?"Desativar":e.can_activate?"Ativar":"Não é possível ativar esta oferta"},async handleTypeChange(e){this.appliedFilters.type=e,this.currentPage=1,await this.loadOffers()},async handleHideInactiveChange(e){const t=e===!0;this.inputFilters.hideInactive=t,this.appliedFilters.hideInactive=t,this.currentPage=1,await this.loadOffers()},async handleSearchInput(){(this.inputFilters.search.length>=3||this.inputFilters.search==="")&&(this.appliedFilters.search=this.inputFilters.search,this.currentPage=1,await this.loadOffers())},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},YC={id:"offer-manager-view",class:"offer-manager"},JC={class:"new-offer-container"},QC={key:0,class:"alert alert-danger"},XC={class:"table-container"},ew=["title"],tw={key:0},sw={key:1},rw={class:"action-buttons"},nw=["onClick"],ow=["onClick"],iw=["onClick","disabled","title"],aw={key:0,class:"fas fa-eye text-white"},lw={key:1,class:"fas fa-eye-slash"},uw=["onClick","disabled","title"];function cw(e,t,s,i,n,a){var be,J,he,ve,Ie,ie;const u=z("CustomButton"),c=z("PageHeader"),h=z("CustomInput"),_=z("FilterGroup"),p=z("CustomSelect"),g=z("CustomCheckbox"),w=z("FilterActions"),C=z("FilterRow"),M=z("FilterSection"),F=z("CustomTable"),se=z("Pagination"),Q=z("ConfirmationModal"),ne=z("LFLoading"),Y=z("Toast");return x(),D("div",YC,[A(c,{title:"Gerenciamento de ofertas"},{actions:Te(()=>[f("div",JC,[A(u,{variant:"primary",icon:"fa fa-plus",label:"Adicionar",onClick:a.createNewOffer},null,8,["onClick"])])]),_:1}),A(M,{title:"FILTRO"},{default:Te(()=>[A(C,{inline:!0},{default:Te(()=>[A(_,{label:"Oferta"},{default:Te(()=>[A(h,{modelValue:n.inputFilters.search,"onUpdate:modelValue":t[0]||(t[0]=I=>n.inputFilters.search=I),placeholder:"Buscar...",width:339,"has-search-icon":!0,onInput:e.debouncedSearch},null,8,["modelValue","onInput"])]),_:1}),n.typeOptionsEnabled?(x(),dt(_,{key:0,label:"Tipo"},{default:Te(()=>[A(p,{modelValue:n.inputFilters.type,"onUpdate:modelValue":[t[1]||(t[1]=I=>n.inputFilters.type=I),a.handleTypeChange],options:a.typeSelectOptions,width:144},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1})):Z("",!0),A(_,{"is-checkbox":!0},{default:Te(()=>[A(g,{modelValue:n.inputFilters.hideInactive,"onUpdate:modelValue":[t[2]||(t[2]=I=>n.inputFilters.hideInactive=I),a.handleHideInactiveChange],id:"hideInactive",label:"Não exibir inativas"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),A(w,null,{default:Te(()=>[A(u,{variant:"secondary",label:"Limpar",onClick:a.clearFilters},null,8,["onClick"])]),_:1})]),_:1})]),_:1}),n.error?(x(),D("div",QC,[t[7]||(t[7]=f("i",{class:"fas fa-exclamation-circle"},null,-1)),We(" "+q(n.error),1)])):Z("",!0),f("div",XC,[A(F,{headers:n.tableHeaders,items:n.offers,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-description":Te(({item:I})=>[f("span",{title:I.description},q(I.description.length>50?I.description.slice(0,50)+"...":I.description),9,ew)]),"item-type":Te(({item:I})=>[We(q(I.type.charAt(0).toUpperCase()+I.type.slice(1)),1)]),"item-status":Te(({item:I})=>[I.status===1?(x(),D("span",tw,t[8]||(t[8]=[f("svg",{class:"mr-1",width:"22",height:"23",viewBox:"0 0 22 23",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",fill:"white"}),f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",stroke:"var(--success)","stroke-width":"2"}),f("path",{d:"M11.0024 1.40109C13.6546 1.40109 16.1984 2.45444 18.0737 4.3298C19.9491 6.20517 21.0024 8.74893 21.0024 11.4011C21.0024 14.0533 19.9491 16.597 18.0737 18.4724C16.1984 20.3477 13.6546 21.4011 11.0024 21.4011C8.35028 21.4011 5.80652 20.3477 3.93115 18.4724C2.05579 16.597 1.00244 14.0533 1.00244 11.4011C1.00244 8.74893 2.05579 6.20517 3.93115 4.3298C5.80652 2.45444 8.35028 1.40109 11.0024 1.40109ZM15.3589 7.34055C15.2329 7.34314 15.1086 7.37093 14.9937 7.42258C14.8788 7.47425 14.7755 7.54884 14.6899 7.64133L10.3491 13.1726L7.73291 10.5544C7.55519 10.3888 7.31954 10.2992 7.07666 10.3034C6.83383 10.3078 6.60191 10.4061 6.43018 10.5779C6.25849 10.7496 6.16005 10.9815 6.15576 11.2243C6.15152 11.4672 6.24215 11.7019 6.40771 11.8796L9.71533 15.1882C9.80438 15.2771 9.91016 15.3472 10.0269 15.3943C10.1436 15.4413 10.2691 15.4649 10.395 15.4626C10.5206 15.4602 10.6446 15.4327 10.7593 15.3816C10.8742 15.3302 10.9782 15.256 11.064 15.1638L16.0532 8.92648C16.2233 8.74961 16.3183 8.51269 16.3159 8.2673C16.3136 8.02207 16.2147 7.78755 16.0415 7.61398H16.0396C15.9503 7.52501 15.844 7.45488 15.7271 7.40793C15.6101 7.36102 15.4849 7.33798 15.3589 7.34055Z",fill:"var(--success)"})],-1),We(" Ativa ")]))):(x(),D("span",sw,t[9]||(t[9]=[f("svg",{class:"mr-1",width:"22",height:"23",viewBox:"0 0 22 23",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[f("g",{"clip-path":"url(#clip0_572_6021)"},[f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",fill:"white"}),f("path",{d:"M11.0024 1.40109C13.6546 1.40109 16.1984 2.45444 18.0737 4.3298C19.9491 6.20517 21.0024 8.74893 21.0024 11.4011C21.0024 14.0533 19.9491 16.597 18.0737 18.4724C16.1984 20.3477 13.6546 21.4011 11.0024 21.4011C8.35028 21.4011 5.80652 20.3477 3.93115 18.4724C2.05579 16.597 1.00244 14.0533 1.00244 11.4011C1.00244 8.74893 2.05579 6.20517 3.93115 4.3298C5.80652 2.45444 8.35028 1.40109 11.0024 1.40109ZM14.7524 7.02512C14.6703 7.02512 14.5891 7.04157 14.5132 7.07297C14.4373 7.10442 14.3682 7.1506 14.3101 7.20871L11.0024 10.5173L7.69482 7.20871C7.57747 7.09135 7.41841 7.02512 7.25244 7.02512C7.08647 7.02512 6.92742 7.09135 6.81006 7.20871C6.6927 7.32607 6.62646 7.48512 6.62646 7.65109C6.62646 7.81706 6.6927 7.97612 6.81006 8.09348L10.1187 11.4011L6.81006 14.7087C6.75195 14.7668 6.70577 14.8359 6.67432 14.9118C6.64292 14.9877 6.62646 15.069 6.62646 15.1511C6.62646 15.2332 6.64292 15.3145 6.67432 15.3904C6.70577 15.4663 6.75195 15.5354 6.81006 15.5935C6.92742 15.7108 7.08647 15.7771 7.25244 15.7771C7.33456 15.7771 7.41583 15.7606 7.4917 15.7292C7.56762 15.6978 7.63671 15.6516 7.69482 15.5935L11.0024 12.2849L14.3101 15.5935C14.3682 15.6516 14.4373 15.6978 14.5132 15.7292C14.5891 15.7606 14.6703 15.7771 14.7524 15.7771C14.8346 15.7771 14.9158 15.7606 14.9917 15.7292C15.0676 15.6978 15.1367 15.6516 15.1948 15.5935C15.2529 15.5354 15.2991 15.4663 15.3306 15.3904C15.362 15.3145 15.3784 15.2332 15.3784 15.1511C15.3784 15.069 15.362 14.9877 15.3306 14.9118C15.2991 14.8359 15.2529 14.7668 15.1948 14.7087L11.8862 11.4011L15.1948 8.09348C15.2529 8.03537 15.2991 7.96627 15.3306 7.89035C15.362 7.81448 15.3784 7.73321 15.3784 7.65109C15.3784 7.56898 15.362 7.48771 15.3306 7.41183C15.2991 7.33591 15.2529 7.26682 15.1948 7.20871C15.1367 7.1506 15.0676 7.10442 14.9917 7.07297C14.9158 7.04157 14.8346 7.02512 14.7524 7.02512Z",fill:"var(--danger)"})]),f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",stroke:"var(--danger)","stroke-width":"2"}),f("defs",null,[f("clipPath",{id:"clip0_572_6021"},[f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",fill:"white"})])])],-1),We(" Inativo ")])))]),"item-actions":Te(({item:I})=>[f("div",rw,[f("button",{class:"btn-action btn-edit",onClick:Ce=>a.navigateToShowOffer(I),title:"Visualizar"},t[10]||(t[10]=[f("svg",{width:"38",height:"39",viewBox:"0 0 38 39",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[f("path",{d:"M18.1875 25.5897C20.04 25.5897 21.5417 24.0629 21.5417 22.1795C21.5417 20.296 20.04 18.7692 18.1875 18.7692C16.3351 18.7692 14.8334 20.296 14.8334 22.1795C14.8334 24.0629 16.3351 25.5897 18.1875 25.5897Z",stroke:"white","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),f("path",{d:"M11 29.4872L15.7917 24.6154M11 22.6667V11.9487C11 11.4319 11.2019 10.9362 11.5614 10.5708C11.9208 10.2053 12.4083 10 12.9167 10H20.5833L26.3333 15.8462V27.5385C26.3333 28.0553 26.1314 28.551 25.772 28.9164C25.4125 29.2819 24.925 29.4872 24.4167 29.4872H17.7083",stroke:"white","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)]),8,nw),f("button",{class:"btn-action btn-edit",onClick:Ce=>a.navigateToEditOffer(I),title:"Editar"},t[11]||(t[11]=[f("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[f("g",{"clip-path":"url(#clip0_9_197955)"},[f("path",{d:"M12.854 0.145905C12.7602 0.0521694 12.6331 -0.000488281 12.5005 -0.000488281C12.3679 -0.000488281 12.2408 0.0521694 12.147 0.145905L10.5 1.7929L14.207 5.49991L15.854 3.8539C15.9006 3.80746 15.9375 3.75228 15.9627 3.69154C15.9879 3.63079 16.0009 3.56567 16.0009 3.4999C16.0009 3.43414 15.9879 3.36902 15.9627 3.30827C15.9375 3.24753 15.9006 3.19235 15.854 3.1459L12.854 0.145905ZM13.5 6.2069L9.793 2.4999L3.293 8.9999H3.5C3.63261 8.9999 3.75978 9.05258 3.85355 9.14635C3.94732 9.24012 4 9.3673 4 9.4999V9.9999H4.5C4.63261 9.9999 4.75978 10.0526 4.85355 10.1464C4.94732 10.2401 5 10.3673 5 10.4999V10.9999H5.5C5.63261 10.9999 5.75978 11.0526 5.85355 11.1464C5.94732 11.2401 6 11.3673 6 11.4999V11.9999H6.5C6.63261 11.9999 6.75978 12.0526 6.85355 12.1464C6.94732 12.2401 7 12.3673 7 12.4999V12.7069L13.5 6.2069ZM6.032 13.6749C6.01095 13.619 6.00012 13.5597 6 13.4999V12.9999H5.5C5.36739 12.9999 5.24021 12.9472 5.14644 12.8535C5.05268 12.7597 5 12.6325 5 12.4999V11.9999H4.5C4.36739 11.9999 4.24021 11.9472 4.14644 11.8535C4.05268 11.7597 4 11.6325 4 11.4999V10.9999H3.5C3.36739 10.9999 3.24021 10.9472 3.14644 10.8535C3.05268 10.7597 3 10.6325 3 10.4999V9.9999H2.5C2.44022 9.99981 2.38094 9.98897 2.325 9.96791L2.146 10.1459C2.09835 10.1939 2.06093 10.251 2.036 10.3139L0.0359968 15.3139C-0.000373859 15.4048 -0.00927736 15.5043 0.0103901 15.6002C0.0300575 15.6961 0.077431 15.7841 0.146638 15.8533C0.215844 15.9225 0.30384 15.9698 0.399716 15.9895C0.495593 16.0092 0.595133 16.0003 0.685997 15.9639L5.686 13.9639C5.74886 13.939 5.80601 13.9016 5.854 13.8539L6.032 13.6759V13.6749Z",fill:"var(--white)"})]),f("defs",null,[f("clipPath",{id:"clip0_9_197955"},[f("rect",{width:"16",height:"16",fill:"white"})])])],-1)]),8,ow),f("button",{class:de(["btn-action",I.status===1?"btn-deactivate":"btn-activate"]),onClick:Ce=>a.toggleOfferStatus(I),disabled:I.status===0&&!I.can_activate,title:a.getStatusButtonTitle(I)},[I.status===1?(x(),D("i",aw)):(x(),D("i",lw))],10,iw),f("button",{class:"btn-action btn-delete",onClick:Ce=>a.deleteOffer(I),disabled:!I.can_delete,title:I.can_delete?"Excluir":"Não é possível excluir esta oferta"},t[12]||(t[12]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,uw)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"])]),A(se,{"current-page":n.currentPage,"onUpdate:currentPage":t[3]||(t[3]=I=>n.currentPage=I),"per-page":n.perPage,"onUpdate:perPage":t[4]||(t[4]=I=>n.perPage=I),total:n.totalOffers,loading:n.loading},null,8,["current-page","per-page","total","loading"]),A(Q,{show:n.showDeleteModal,size:"md",title:"A exclusão desta instância de oferta é uma ação irreversível.",message:"Todos os cursos vinculados serão desassociados e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Oferta","cancel-button-text":"Cancelar",icon:"warning",onClose:t[5]||(t[5]=I=>n.showDeleteModal=!1),onConfirm:a.confirmDeleteOffer},null,8,["show","onConfirm"]),A(Q,{show:n.showStatusModal,size:"md",title:((be=n.selectedOffer)==null?void 0:be.status)===1?"Ao inativar esta oferta, os cursos e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:((J=n.selectedOffer)==null?void 0:J.status)===1?"":"Tem certeza que deseja ativar esta oferta?","list-title":((he=n.selectedOffer)==null?void 0:he.status)===1?"Comportamento para os cursos, turmas e matrículas:":"","list-items":((ve=n.selectedOffer)==null?void 0:ve.status)===1?["Todos os cursos contidos na oferta não serão mais disponibilizados para os usuários da oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":((Ie=n.selectedOffer)==null?void 0:Ie.status)===1?"Inativar oferta":"Ativar","cancel-button-text":"Cancelar",icon:((ie=n.selectedOffer)==null?void 0:ie.status)===1?"warning":"question",onClose:t[6]||(t[6]=I=>n.showStatusModal=!1),onConfirm:a.confirmToggleStatus},null,8,["show","title","message","list-title","list-items","confirm-button-text","icon","onConfirm"]),A(ne,{"is-loading":n.loading},null,8,["is-loading"]),A(Y,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])])}const dw=Pe(ZC,[["render",cw],["__scopeId","data-v-545af5a0"]]);async function fw(e={}){try{return await Ue("local_offermanager_fetch_enrolments",{offerclassid:e.offerclassid,userids:e.userids||[],page:e.page||1,perpage:e.perpage||20,orderby:e.orderby||"fullname",direction:e.direction||"ASC"})}catch(t){throw t}}async function gu(e={}){try{return await Ue("local_offermanager_get_enroled_users",{offerclassid:e.offerclassid,searchstring:e.searchstring||"",fieldstring:e.fieldstring||"name",excludeduserids:e.excludeduserids||[]})}catch(t){throw t}}async function hw(e={}){try{return await Ue("local_offermanager_enrol_users",{offerclassid:e.offerclassid,userids:e.userids||[],roleid:e.roleid||5})}catch(t){throw t}}async function pw(e,t="",s){try{return await Ue("local_offermanager_get_potential_users_to_enrol",{offerclassid:e,search_string:t,excluded_userids:s})}catch(i){throw i}}async function mw(e={}){try{return await Ue("local_offermanager_edit_offer_user_enrol",{offeruserenrolid:e.offeruserenrolid,status:e.status,timestart:e.timestart,timeend:e.timeend,roleid:e.roleid})}catch(t){throw t}}async function gw(e={}){try{return await Ue("local_offermanager_edit_offer_user_enrol_bulk",{offeruserenrolids:e.offeruserenrolids||[],status:e.status,timestart:e.timestart,timeend:e.timeend})}catch(t){throw t}}async function _w(e){try{return await Ue("local_offermanager_delete_offer_user_enrol_bulk",{offeruserenrolids:e})===!0?e.map(i=>({id:i,operation_status:!0})):[]}catch(t){throw t}}async function vw(e){try{return await Ue("local_offermanager_get_roles",{offeruserenrolid:e})}catch(t){throw t}}async function yw(e,t){try{return await Ue("local_offermanager_update_roles",{offeruserenrolid:e,roleids:Array.isArray(t)?t:[t]})}catch(s){throw s}}const fM="",bw={name:"HierarchicalSelect",props:{modelValue:{type:String,default:""},options:{type:Array,required:!0},label:{type:String,default:""},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}}},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.$emit("navigate",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate","navigate"]},Cw={class:"select-wrapper"},ww=["value","disabled"],Ew=["label"],Ow=["value"],xw={key:1,class:"error-message"};function Sw(e,t,s,i,n,a){return x(),D("div",{ref:"selectContainer",class:"hierarchical-select-container",style:is(a.customWidth)},[s.label?(x(),D("div",{key:0,class:de(["select-label",{disabled:s.disabled}])},q(s.label),3)):Z("",!0),f("div",Cw,[f("select",{value:s.modelValue,onChange:t[0]||(t[0]=(...u)=>a.handleChange&&a.handleChange(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),class:de(["hierarchical-select",{error:s.hasError}]),disabled:s.disabled},[(x(!0),D(Ne,null,it(s.options,u=>(x(),D("optgroup",{key:u.value,label:u.label},[(x(!0),D(Ne,null,it(u.children,c=>(x(),D("option",{key:c.value,value:c.value,class:"child-option"},q(c.label),9,Ow))),128))],8,Ew))),128))],42,ww),f("div",{class:de(["select-arrow",{disabled:s.disabled}])},null,2)]),s.hasError&&s.errorMessage?(x(),D("div",xw,q(s.errorMessage),1)):Z("",!0)],4)}const Dw=Pe(bw,[["render",Sw],["__scopeId","data-v-b5d38077"]]),hM="",Tw={name:"FilterTag",props:{readonly:{type:Boolean,default:!1}},emits:["remove"]},Nw={key:0,class:"fas fa-times"};function Iw(e,t,s,i,n,a){return x(),D("div",{class:"tag badge badge-primary",onClick:t[0]||(t[0]=()=>!s.readonly&&e.$emit("remove"))},[s.readonly?Z("",!0):(x(),D("i",Nw)),Mt(e.$slots,"default",{},void 0,!0)])}const Wo=Pe(Tw,[["render",Iw],["__scopeId","data-v-1d857df7"]]),pM="",Aw={name:"FilterTags"},Mw={class:"filter-tags"};function Pw(e,t,s,i,n,a){return x(),D("div",Mw,[Mt(e.$slots,"default",{},void 0,!0)])}const oa=Pe(Aw,[["render",Pw],["__scopeId","data-v-d8e54e5f"]]),mM="",kw={name:"Autocomplete",components:{FilterTag:Wo,FilterTags:oa},props:{modelValue:{type:[Array,Object,String,Number],default:()=>[]},items:{type:Array,default:()=>[]},placeholder:{type:String,default:""},label:{type:String,default:""},width:{type:[Number,String],default:"auto"},required:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},minChars:{type:Number,default:3},showAllOption:{type:Boolean,default:!1},inputMaxWidth:{type:[String,Number],default:null},autoOpen:{type:Boolean,default:!0},noResultsText:{type:String,default:"Nenhum item disponível"},hasSearchIcon:{type:Boolean,default:!1},showFilterTags:{type:Boolean,default:!0},showSelectedInInput:{type:Boolean,default:!1},maxLabelLength:{type:Number,default:30},loading:{type:Boolean,default:!1},keepOpenOnSelect:{type:Boolean,default:!1}},emits:["update:modelValue","select","select-all","load-more","search"],data(){return{searchQuery:"",isOpen:!1,selectedIndex:-1,internalItems:[],uniqueId:`autocomplete-${Math.random().toString(36).substring(2,9)}`,focusedOptionIndex:-1,blurTimeout:null,debouncedSearch:null}},computed:{displayItems(){let e=this.internalItems;if(this.searchQuery){const t=this.searchQuery.toLowerCase();e=this.internalItems.filter(s=>s.label.toLowerCase().includes(t))}return this.showAllOption&&Array.isArray(this.modelValue)?[{label:"Todos",value:"__ALL__"},...e]:e},inputMaxWidthStyle(){return this.inputMaxWidth?typeof this.inputMaxWidth=="number"?`${this.inputMaxWidth}px`:this.inputMaxWidth:null},getSelectedItemLabel(){return this.modelValue?this.modelValue.label:""},selectedItems(){return Array.isArray(this.modelValue)?this.modelValue.map(e=>{if(e.value&&e.label!=="")return e;const t=this.items.find(i=>i.value===(e.value||e)),s=(t==null?void 0:t.label)||"";return{value:e.value||e,label:s}}):[]}},created(){this.debouncedSearch=Ur.debounce(e=>{this.$emit("search",e)},300)},watch:{items:{handler(e){this.internalItems=Array.isArray(e)?[...e]:[],this.autoOpen&&this.keepOpenOnSelect&&!this.disabled&&this.internalItems.length>0&&this.$refs.inputElement===document.activeElement&&(this.isOpen=!0)},immediate:!0,deep:!0},searchQuery(e){this.isOpen=!0,this.selectedIndex=-1,(e.length===0||e.length>=this.minChars)&&this.debouncedSearch(e)}},methods:{handleFocus(){this.autoOpen&&!this.disabled&&(this.isOpen=!0,this.selectedIndex=-1,this.searchQuery&&(this.searchQuery="",this.$emit("search",""))),this.blurTimeout&&(clearTimeout(this.blurTimeout),this.blurTimeout=null)},openDropdown(){this.disabled||(this.isOpen=!0)},handleBlur(){this.blurTimeout=setTimeout(()=>{this.$el.contains(document.activeElement)||(this.isOpen=!1,this.selectedIndex=-1)},150)},handleInput(){this.disabled||(this.isOpen=!0)},selectItem(e){if(e.value==="__ALL__"){if(Array.isArray(this.modelValue)){if(this.modelValue.length===this.items.length){this.$emit("update:modelValue",[]);return}this.$emit("update:modelValue",this.items),this.$emit("select-all")}this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()});return}if(Array.isArray(this.modelValue)){const t=[...this.modelValue],s=t.findIndex(i=>i.value===e.value);s===-1?t.push(e):t.splice(s,1),this.$emit("update:modelValue",t)}else this.$emit("update:modelValue",e),this.$emit("select",e);this.searchQuery="",this.isOpen=!!this.keepOpenOnSelect,this.selectedIndex=-1,this.$nextTick(()=>{this.autoOpen&&this.focusInput()})},removeItem(e){if(Array.isArray(this.modelValue)){const t=this.modelValue.filter(s=>s.value!==e.value);this.$emit("update:modelValue",t)}else this.$emit("update:modelValue","");Array.isArray(this.modelValue)?(this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1):(this.selectedIndex=-1,this.$nextTick(()=>{this.isOpen=!1})),this.$nextTick(()=>{this.focusInput()})},removeSelectedItem(){this.$emit("update:modelValue",""),this.searchQuery="",this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()})},handleKeydown(e){if(!this.isOpen&&e.key!=="Tab"){this.isOpen=!0;return}switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.min(this.selectedIndex+1,this.displayItems.length-1),this.focusOption(this.selectedIndex);break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.max(this.selectedIndex-1,-1),this.selectedIndex===-1?this.focusInput():this.focusOption(this.selectedIndex);break;case"Enter":e.preventDefault(),this.selectedIndex>=0?this.selectItem(this.displayItems[this.selectedIndex]):this.searchQuery&&this.searchQuery.length>=this.minChars&&this.$emit("search",this.searchQuery);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1;break;case"Tab":this.isOpen&&!e.shiftKey&&this.displayItems.length>0&&(e.preventDefault(),e.stopPropagation(),this.selectedIndex=0,this.focusOption(0));break}},handleOptionKeydown(e,t,s){switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1&&(this.selectedIndex=s+1,this.focusOption(this.selectedIndex));break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput());break;case"Enter":case" ":e.preventDefault(),this.selectItem(t);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1,this.focusInput();break;case"Tab":e.shiftKey?(e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput())):(e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1?(this.selectedIndex=s+1,this.focusOption(this.selectedIndex)):(this.selectedIndex=0,this.focusOption(0)));break}},focusInput(){this.$refs.inputElement&&this.$refs.inputElement.focus()},focusOption(e){requestAnimationFrame(()=>{var s;const t=(s=this.$refs.optionElements)==null?void 0:s[e];t&&t.focus()})},handleClickOutside(e){this.$el.contains(e.target)||(this.isOpen=!1,this.selectedIndex=-1)},truncateLabel(e){return e?e.length<=this.maxLabelLength?e:e.substring(0,this.maxLabelLength)+"...":""},handleScroll(e){if(!e||!e.target)return;const t=e.target;t.scrollHeight&&t.scrollTop!==void 0&&t.clientHeight&&t.scrollHeight-t.scrollTop-t.clientHeight<50&&this.$emit("load-more")}},mounted(){document.addEventListener("click",this.handleClickOutside),this.autoOpen&&!this.disabled&&this.internalItems.length>0&&this.$nextTick(()=>{this.isOpen=!0})},beforeUnmount(){document.removeEventListener("click",this.handleClickOutside)}},Vw={class:"autocomplete-container"},Rw=["id"],Fw={class:"autocomplete-wrapper"},Lw=["placeholder","disabled","aria-expanded","aria-owns","aria-labelledby","aria-controls"],Uw={key:0,class:"selected-item"},Bw=["title"],$w=["id"],qw=["id","data-index","aria-selected","tabindex","onClick","onKeydown","title"],Hw={class:"item-label"},Ww={key:0,class:"fas fa-check"},jw={key:0,class:"dropdown-item loading-item"},zw={key:1,class:"dropdown-item no-results"},Gw={key:0,class:"tags-container"};function Kw(e,t,s,i,n,a){const u=z("FilterTag"),c=z("FilterTags");return x(),D("div",Vw,[s.label?(x(),D("label",{key:0,class:de(["filter-label",{required:s.required}]),id:`${n.uniqueId}-label`},q(s.label),11,Rw)):Z("",!0),f("div",Fw,[f("div",{class:"input-container",style:is({maxWidth:a.inputMaxWidthStyle})},[f("div",{class:de(["input-wrapper",{"has-search-icon":s.hasSearchIcon,"has-selected-item":s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery}])},[lt(f("input",{type:"text",class:"form-control",placeholder:s.placeholder,"onUpdate:modelValue":t[0]||(t[0]=h=>n.searchQuery=h),disabled:s.disabled,"aria-expanded":n.isOpen,"aria-owns":`${n.uniqueId}-listbox`,"aria-labelledby":s.label?`${n.uniqueId}-label`:void 0,"aria-autocomplete":"list","aria-controls":`${n.uniqueId}-listbox`,role:"combobox",tabindex:"0",onKeydown:t[1]||(t[1]=(...h)=>a.handleKeydown&&a.handleKeydown(...h)),onFocus:t[2]||(t[2]=h=>!s.disabled&&a.handleFocus),onInput:t[3]||(t[3]=(...h)=>a.handleInput&&a.handleInput(...h)),onClick:t[4]||(t[4]=h=>!s.disabled&&a.openDropdown()),onBlur:t[5]||(t[5]=(...h)=>a.handleBlur&&a.handleBlur(...h)),ref:"inputElement"},null,40,Lw),[[Zt,n.searchQuery]]),s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery?(x(),D("div",Uw,[f("span",{class:"selected-text",title:a.getSelectedItemLabel},q(a.truncateLabel(a.getSelectedItemLabel)),9,Bw),f("i",{class:"fas fa-times remove-selected",onClick:t[6]||(t[6]=Pt((...h)=>a.removeSelectedItem&&a.removeSelectedItem(...h),["stop"]))})])):Z("",!0),s.hasSearchIcon&&!(s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery)?(x(),D("i",{key:1,class:de(["search-icon",{"fas fa-search":!s.loading,"spinner-border spinner-border-sm":s.loading}])},null,2)):Z("",!0)],2),n.isOpen?(x(),D("div",{key:0,class:"dropdown-menu show",id:`${n.uniqueId}-listbox`,role:"listbox",tabindex:"-1",ref:"dropdownMenu",onScroll:t[7]||(t[7]=(...h)=>a.handleScroll&&a.handleScroll(...h))},[a.displayItems.length>0?(x(),D(Ne,{key:0},[(x(!0),D(Ne,null,it(a.displayItems,(h,_)=>(x(),D("div",{key:h.value==="__ALL__"?"__ALL__":h.value,class:de(["dropdown-item",{active:n.selectedIndex===_,selected:h.value!=="__ALL__"&&(Array.isArray(a.selectedItems)?a.selectedItems.some(p=>p.value===h.value):a.selectedItems===h.value)}]),id:`${n.uniqueId}-option-${_}`,role:"option","data-index":_,"aria-selected":n.selectedIndex===_,tabindex:n.selectedIndex===_?0:-1,onClick:p=>a.selectItem(h),onKeydown:p=>a.handleOptionKeydown(p,h,_),ref_for:!0,ref:"optionElements",title:h.label},[f("span",Hw,q(a.truncateLabel(h.label)),1),h.value!=="__ALL__"&&Array.isArray(a.selectedItems)&&a.selectedItems.some(p=>p.value===h.value)?(x(),D("i",Ww)):Z("",!0)],42,qw))),128)),s.loading?(x(),D("div",jw,t[8]||(t[8]=[f("span",null,"Carregando mais itens...",-1)]))):Z("",!0)],64)):(x(),D("div",zw,q(s.noResultsText||"Nenhum item disponível"),1))],40,$w)):Z("",!0)],4),s.showFilterTags&&Array.isArray(s.modelValue)&&s.modelValue.length>0?(x(),D("div",Gw,[A(c,null,{default:Te(()=>[(x(!0),D(Ne,null,it(a.selectedItems,h=>(x(),dt(u,{key:h.value,readonly:s.disabled,onRemove:_=>a.removeItem(h)},{default:Te(()=>[We(q(h.label),1)]),_:2},1032,["readonly","onRemove"]))),128))]),_:1})])):Z("",!0)])])}const _n=Pe(kw,[["render",Kw],["__scopeId","data-v-105c7bfb"]]),gM="",Zw={name:"EnrolmentModalNew",components:{Toast:Br,CustomSelect:Qs},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Matricular usuários na turma"},size:{type:String,default:"lg",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Salvar"},cancelButtonText:{type:String,default:"Cancelar"},offerclassid:{type:Number,required:!0},roles:{type:Array,required:!0}},emits:["close","success"],data(){return{enrolmentMethod:"manual",enrolmentMethodOptions:[{value:"manual",label:"Manual"},{value:"batch",label:"Em lote"}],selectedRoleId:"",searchQuery:"",isOpen:!1,userOptions:[],selectedUsers:[],debounceTimer:null,selectedFile:null,csvUsers:[],isDragging:!1,csvDelimiter:",",csvEncoding:"UTF-8",delimiterOptions:[{value:",",label:","},{value:";",label:";"},{value:":",label:":"},{value:"	",label:"\\t"},{value:" ",label:"Espaço"}],encodingOptions:[{value:"UTF-8",label:"UTF-8"},{value:"WINDOWS-1252",label:"WINDOWS-1252"},{value:"ISO-8859-1",label:"ISO-8859-1"},{value:"ASCII",label:"ASCII"},{value:"ISO-8859-2",label:"ISO-8859-2"},{value:"ISO-8859-3",label:"ISO-8859-3"},{value:"ISO-8859-4",label:"ISO-8859-4"},{value:"ISO-8859-5",label:"ISO-8859-5"},{value:"ISO-8859-6",label:"ISO-8859-6"},{value:"ISO-8859-7",label:"ISO-8859-7"},{value:"ISO-8859-8",label:"ISO-8859-8"},{value:"ISO-8859-9",label:"ISO-8859-9"},{value:"ISO-8859-10",label:"ISO-8859-10"},{value:"ISO-8859-13",label:"ISO-8859-13"},{value:"ISO-8859-14",label:"ISO-8859-14"},{value:"ISO-8859-15",label:"ISO-8859-15"},{value:"ISO-8859-16",label:"ISO-8859-16"},{value:"WINDOWS-874",label:"WINDOWS-874"},{value:"WINDOWS-1250",label:"WINDOWS-1250"},{value:"WINDOWS-1251",label:"WINDOWS-1251"},{value:"WINDOWS-1253",label:"WINDOWS-1253"},{value:"WINDOWS-1254",label:"WINDOWS-1254"},{value:"WINDOWS-1255",label:"WINDOWS-1255"},{value:"WINDOWS-1256",label:"WINDOWS-1256"},{value:"WINDOWS-1257",label:"WINDOWS-1257"},{value:"WINDOWS-1258",label:"WINDOWS-1258"},{value:"KOI8-R",label:"KOI8-R"},{value:"MACINTOSH",label:"MACINTOSH"},{value:"IBM866",label:"IBM866"},{value:"BIG5",label:"BIG5"},{value:"EUC-JP",label:"EUC-JP"},{value:"SHIFT_JIS",label:"SHIFT_JIS"},{value:"EUC-KR",label:"EUC-KR"},{value:"UTF-7",label:"UTF-7"},{value:"UTF-16",label:"UTF-16"},{value:"UTF-32",label:"UTF-32"},{value:"UCS-2",label:"UCS-2"},{value:"UCS-4",label:"UCS-4"}],loadingUsers:!1,isSubmitting:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,showResultAlerts:!1,batchMessage:"",batchMessageType:"success",failedMessages:[],reenrolMessages:[]}},computed:{isFormValid(){return this.roles.length===0?!1:this.enrolmentMethod==="manual"?this.selectedUsers.length>0&&this.selectedRoleId:this.enrolmentMethod==="batch"?this.csvUsers.length>0&&this.selectedRoleId:!1}},watch:{show(e){document.body.style.overflow=e?"hidden":"",e&&this.initializeForm()}},mounted(){document.addEventListener("keydown",this.handleKeyDown),document.addEventListener("click",this.handleClickOutside),this.show&&(document.body.style.overflow="hidden",this.initializeForm())},unmounted(){document.removeEventListener("keydown",this.handleKeyDown),document.removeEventListener("click",this.handleClickOutside),document.body.style.overflow=""},methods:{handleKeyDown(e){this.show&&e.key==="Escape"&&this.$emit("close")},handleClickOutside(e){if(this.show===!1)return;const t=document.querySelector(".custom-autocomplete-wrapper");t&&!t.contains(e.target)&&(this.isOpen=!1)},async initializeForm(){this.resetForm()},resetForm(){let e=this.roles.find(t=>t.value==5);this.enrolmentMethod="manual",this.selectedRoleId=e.value,this.searchQuery="",this.selectedUsers=[],this.selectedFile=null,this.csvUsers=[],this.csvDelimiter=",",this.csvEncoding="UTF-8",this.showResultAlerts=!1,this.batchMessage="",this.batchMessageType="success",this.failedMessages=[],this.reenrolMessages=[]},async fetchPotentialUsersToEnrol(e){this.loadingUsers=!0;let t=this.selectedUsers.map(i=>i.value);const s=await pw(this.offerclassid,e,t);this.userOptions=s.data.map(i=>({value:i.id,label:i.fullname})),this.loadingUsers=!1},handleInput(){const e=this.searchQuery.trim();this.debounceTimer&&clearTimeout(this.debounceTimer),e.length>=3?this.debounceTimer=setTimeout(async()=>{await this.fetchPotentialUsersToEnrol(e),this.userOptions&&(this.isOpen=!0)},500):(this.isOpen=!1,this.userOptions=[])},selectUser(e){const t=this.selectedUsers.findIndex(s=>s.value===e.value);t===-1?this.selectedUsers.push(e):this.selectedUsers.splice(t,1),this.searchQuery="",this.isOpen=!1},removeUser(e){this.selectedUsers=this.selectedUsers.filter(t=>t.value!==e.value)},onDragOver(){this.isDragging=!0},onDragLeave(){this.isDragging=!1},onDrop(e){this.isDragging=!1;const t=e.dataTransfer.files;t.length>0&&this.processFile(t[0])},handleFileSelect(e){const t=e.target.files;t.length>0&&this.processFile(t[0])},removeFile(){this.selectedFile=null,this.csvUsers=[],this.$refs.fileInput&&(this.$refs.fileInput.value="")},processFile(e){if(e.type!=="text/csv"&&!e.name.endsWith(".csv")){this.showErrorMessage("Por favor, selecione um arquivo CSV válido.");return}this.selectedFile=e,this.readCSVFile(e)},readCSVFile(e){const t=new FileReader;t.onload=s=>{const i=s.target.result;this.parseCSV(i)},t.onerror=s=>{if(console.error("Erro ao ler o arquivo:",s),this.csvEncoding!=="UTF-8"){console.log("Tentando ler com UTF-8 como fallback...");const i=new FileReader;i.onload=n=>{const a=n.target.result;this.parseCSV(a)},i.onerror=()=>{this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato e a codificação estão corretos.")},i.readAsText(e,"UTF-8")}else this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato está correto.")};try{t.readAsText(e,this.csvEncoding)}catch(s){console.error("Erro ao tentar ler o arquivo com a codificação selecionada:",s),this.showErrorMessage(`Erro ao ler o arquivo com a codificação ${this.csvEncoding}. Tente selecionar outra codificação.`)}},parseCSV(e){try{const t=this.csvDelimiter,s=/�/.test(e);s&&console.warn("O arquivo contém caracteres inválidos. Pode haver um problema com a codificação selecionada.");const i=e.split(/\r?\n/),n=[];if(i.length<2){console.log("EnrolmentModalNew - Linhas do CSV:",i),this.showErrorMessage("O arquivo CSV deve conter pelo menos uma linha de cabeçalho e uma linha de dados.");return}const a=(_,p)=>{if(p==="\\t")return _.split("	");if(p===" ")return _.split(/\s+/);{const g=p.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");return _.split(new RegExp(g))}},u=a(i[0].toLowerCase(),t);if(u.length<2||!u.some(_=>_.includes("userid"))||!u.some(_=>_.includes("firstname"))){this.showErrorMessage("O arquivo CSV deve conter colunas para UserID e firstname do usuário.");return}const c=u.findIndex(_=>_.includes("userid")),h=u.findIndex(_=>_.includes("firstname"));for(let _=1;_<i.length;_++){const p=i[_].trim();if(!p)continue;const g=a(p,t);if(g.length>Math.max(c,h)){const w=g[c].trim(),C=g[h].trim();if(w&&C){if(!/^\d+$/.test(w)){console.warn(`Linha ${_+1}: ID inválido '${w}'. Deve ser um número.`);continue}n.push({id:w,name:C})}}}if(n.length===0){s?this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Pode haver um problema com a codificação selecionada. Tente selecionar outra codificação."):this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Verifique o formato do arquivo.");return}this.csvUsers=n}catch(t){console.error("Erro ao processar arquivo CSV:",t),this.showErrorMessage("Erro ao processar o arquivo CSV. Verifique o formato e a codificação e tente novamente.")}},formatFileSize(e){if(e===0)return"0 Bytes";const t=1024,s=["Bytes","KB","MB","GB","TB"],i=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,i)).toFixed(2))+" "+s[i]},async handleSubmit(){if(this.isFormValid)try{this.isSubmitting=!0;let e=[];this.enrolmentMethod==="manual"?e=this.selectedUsers.map(s=>s.value):this.enrolmentMethod==="batch"&&(e=this.csvUsers.map(s=>parseInt(s.id))),e||this.showErrorMessage("Nenhum usuário selecionado para efetuar a matrícula");const t=await hw({offerclassid:this.offerclassid,userids:e,roleid:parseInt(this.selectedRoleId)});if(t.data){this.showResultAlerts=!0;const s=t.data.filter(u=>u.success),i=s.length,n=i>0?s.filter(u=>u.reenrol):[],a=t.data.filter(u=>u.success==!1);this.batchMessage=i>0?`${i} de ${e.length} usuário(s) matriculado(s) com sucesso.`:"Nenhuma inscrição foi realizada",this.batchMessageType=i>0?"success":"danger",this.reenrolMessages=n.length>0?n.map(u=>u.message):[],this.failedMessages=a.length>0?a.map(u=>u.message):[],i>0&&this.$emit("success",{count:i,total:e.length})}}catch(e){this.showErrorMessage(e.message||"Erro ao matricular usuários. Tente novamente.")}finally{this.isSubmitting=!1}},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},Yw={class:"modal-header"},Jw={class:"modal-title"},Qw={class:"modal-body"},Xw={key:0,class:"loading-overlay"},eE={key:1,class:"result-alerts"},tE={key:1,class:"failed-messages"},sE={key:2,class:"reenrol-messages"},rE={key:2,class:"enrolment-modal"},nE={class:"form-row"},oE={class:"form-group"},iE={class:"limited-width-input"},aE={class:"form-group"},lE={class:"limited-width-input"},uE={key:0,class:"error-message"},cE={key:0,class:"form-group"},dE={class:"user-select-container"},fE={class:"custom-autocomplete-wrapper"},hE={key:0,class:"dropdown-menu show"},pE=["onClick"],mE={key:0,class:"fas fa-check"},gE={key:0,class:"selected-users-container"},_E={class:"filter-tags"},vE=["onClick"],yE={key:1,class:"form-group"},bE={class:"file-name"},CE={class:"file-size"},wE={key:0,class:"csv-users-preview"},EE={class:"preview-header"},OE={class:"selected-users-container"},xE={class:"filter-tags"},SE={key:0,class:"more-users"},DE={class:"csv-info"},TE={class:"csv-example"},NE=["href"],IE={class:"csv-options-row"},AE={class:"csv-option"},ME={class:"csv-option"},PE={key:0,class:"modal-footer"},kE=["disabled"];function VE(e,t,s,i,n,a){const u=z("CustomSelect"),c=z("Toast");return x(),D(Ne,null,[s.show?(x(),D("div",{key:0,class:"modal-backdrop",onClick:t[15]||(t[15]=h=>s.closeOnBackdrop?e.$emit("close"):null)},[f("div",{class:de(["modal-container",[`modal-${s.size}`]]),onClick:t[14]||(t[14]=Pt(()=>{},["stop"]))},[f("div",Yw,[f("h3",Jw,q(s.title),1),f("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[16]||(t[16]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",Qw,[n.isSubmitting?(x(),D("div",Xw,t[17]||(t[17]=[f("div",{class:"loading-content"},[f("div",{class:"spinner-border text-primary",role:"status"},[f("span",{class:"sr-only"},"Carregando...")]),f("p",{class:"loading-text mt-3"},"Processando matrículas...")],-1)]))):Z("",!0),n.showResultAlerts?(x(),D("div",eE,[n.batchMessage?(x(),D("div",{key:0,class:de(["alert",n.batchMessageType==="success"?"alert-success":"alert-danger"])},[f("i",{class:de(n.batchMessageType==="success"?"fas fa-check-circle":"fas fa-exclamation-triangle")},null,2),We(" "+q(n.batchMessage),1)],2)):Z("",!0),n.failedMessages.length>0?(x(),D("div",tE,[(x(!0),D(Ne,null,it(n.failedMessages,(h,_)=>(x(),D("div",{key:_,class:"alert alert-warning"},[t[18]||(t[18]=f("i",{class:"fas fa-exclamation-triangle"},null,-1)),We(" "+q(h),1)]))),128))])):Z("",!0),n.reenrolMessages.length>0?(x(),D("div",sE,[(x(!0),D(Ne,null,it(n.reenrolMessages,(h,_)=>(x(),D("div",{key:_,class:"alert alert-info"},[t[19]||(t[19]=f("i",{class:"fas fa-exclamation-triangle"},null,-1)),We(" "+q(h),1)]))),128))])):Z("",!0)])):(x(),D("div",rE,[t[34]||(t[34]=f("h3",{class:"section-title"},"OPÇÕES DE MATRÍCULA",-1)),f("div",nE,[f("div",oE,[t[20]||(t[20]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Forma de matrícula"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",iE,[A(u,{modelValue:n.enrolmentMethod,"onUpdate:modelValue":t[1]||(t[1]=h=>n.enrolmentMethod=h),options:n.enrolmentMethodOptions,style:{width:"100%"},required:""},null,8,["modelValue","options"])])]),f("div",aE,[t[21]||(t[21]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Papel para atribuir"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",lE,[A(u,{modelValue:n.selectedRoleId,"onUpdate:modelValue":t[2]||(t[2]=h=>n.selectedRoleId=h),options:s.roles,class:"w-100",required:""},null,8,["modelValue","options"]),s.roles.length===0?(x(),D("div",uE," Não foi possível carregar os papéis disponíveis para esta turma. ")):Z("",!0)])])]),n.enrolmentMethod==="manual"?(x(),D("div",cE,[t[24]||(t[24]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Selecionar usuários"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",dE,[f("div",fE,[lt(f("input",{type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[3]||(t[3]=h=>n.searchQuery=h),onInput:t[4]||(t[4]=(...h)=>a.handleInput&&a.handleInput(...h))},null,544),[[Zt,n.searchQuery]]),t[22]||(t[22]=f("div",{class:"select-arrow"},null,-1)),n.isOpen?(x(),D("div",hE,[(x(!0),D(Ne,null,it(n.userOptions,(h,_)=>(x(),D("div",{key:h.value,class:"dropdown-item",onClick:p=>a.selectUser(h)},[We(q(h.label)+" ",1),n.selectedUsers.some(p=>p.value===h.value)?(x(),D("i",mE)):Z("",!0)],8,pE))),128))])):Z("",!0)])]),n.selectedUsers.length>0?(x(),D("div",gE,[f("div",_E,[(x(!0),D(Ne,null,it(n.selectedUsers,h=>(x(),D("div",{key:h.value,class:"tag badge badge-primary",onClick:_=>a.removeUser(h)},[t[23]||(t[23]=f("i",{class:"fas fa-times"},null,-1)),We(" "+q(h.label),1)],8,vE))),128))])])):Z("",!0)])):Z("",!0),n.enrolmentMethod==="batch"?(x(),D("div",yE,[t[33]||(t[33]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Matricular usuários a partir de um arquivo CSV"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",{class:de(["csv-upload-area",{"drag-over":n.isDragging}]),onDragover:t[6]||(t[6]=Pt((...h)=>a.onDragOver&&a.onDragOver(...h),["prevent"])),onDragleave:t[7]||(t[7]=Pt((...h)=>a.onDragLeave&&a.onDragLeave(...h),["prevent"])),onDrop:t[8]||(t[8]=Pt((...h)=>a.onDrop&&a.onDrop(...h),["prevent"])),onClick:t[9]||(t[9]=h=>e.$refs.fileInput.click())},[f("input",{type:"file",ref:"fileInput",accept:".csv",style:{display:"none"},onChange:t[5]||(t[5]=(...h)=>a.handleFileSelect&&a.handleFileSelect(...h))},null,544),n.selectedFile?(x(),D(Ne,{key:1},[t[27]||(t[27]=f("div",{class:"file-icon"},[f("i",{class:"fas fa-file-alt"})],-1)),f("p",bE,q(n.selectedFile.name),1),f("p",CE," ("+q(a.formatFileSize(n.selectedFile.size))+") ",1),t[28]||(t[28]=f("p",{class:"file-replace-text"}," Clique ou arraste outro arquivo para substituir ",-1))],64)):(x(),D(Ne,{key:0},[t[25]||(t[25]=f("div",{class:"upload-icon"},[f("i",{class:"fas fa-arrow-down"})],-1)),t[26]||(t[26]=f("p",{class:"upload-text"}," Você pode arrastar e soltar arquivos aqui para adicioná-los. ",-1))],64))],34),n.csvUsers.length>0?(x(),D("div",wE,[f("div",EE,[f("span",null,"Usuários encontrados no arquivo ("+q(n.csvUsers.length)+"):",1)]),f("div",OE,[f("div",xE,[(x(!0),D(Ne,null,it(n.csvUsers.slice(0,5),h=>(x(),D("div",{key:h.id,class:"tag badge badge-primary"},q(h.name),1))),128)),n.csvUsers.length>5?(x(),D("span",SE,"+"+q(n.csvUsers.length-5)+" mais",1)):Z("",!0)])])])):Z("",!0),f("div",DE,[t[32]||(t[32]=f("p",{class:"csv-format-text"},"Formatos aceitos: CSV",-1)),f("div",TE,[t[29]||(t[29]=f("span",{class:"example-label"},"Exemplo CSV",-1)),f("a",{href:`/local/offermanager/export_potential_users.php?offerclassid=${s.offerclassid}`,class:"example-csv"},"example.csv",8,NE)]),f("div",IE,[f("div",AE,[t[30]||(t[30]=f("label",null,"Delimitador do CSV",-1)),A(u,{modelValue:n.csvDelimiter,"onUpdate:modelValue":t[10]||(t[10]=h=>n.csvDelimiter=h),options:n.delimiterOptions,width:160},null,8,["modelValue","options"])]),f("div",ME,[t[31]||(t[31]=f("label",null,"Codificação",-1)),A(u,{modelValue:n.csvEncoding,"onUpdate:modelValue":t[11]||(t[11]=h=>n.csvEncoding=h),options:n.encodingOptions,width:160},null,8,["modelValue","options"])])])])])):Z("",!0),t[35]||(t[35]=f("div",{class:"form-info"},[f("span",{style:{color:"#f8f9fa","font-size":"15px"}},"Este formulário contém campos obrigatórios marcados com"),f("i",{class:"fas fa-exclamation-circle",style:{color:"#dc3545","font-size":"0.85rem","vertical-align":"middle"}})],-1))]))]),n.showResultAlerts?Z("",!0):(x(),D("div",PE,[f("button",{class:"btn btn-primary",onClick:t[12]||(t[12]=(...h)=>a.handleSubmit&&a.handleSubmit(...h)),disabled:n.isSubmitting||!a.isFormValid},q(s.confirmButtonText),9,kE),f("button",{class:"btn btn-secondary",onClick:t[13]||(t[13]=h=>e.$emit("close"))},q(s.cancelButtonText),1)]))],2)])):Z("",!0),A(c,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])],64)}const RE=Pe(Zw,[["render",VE],["__scopeId","data-v-cb610ebd"]]),_M="",FE={name:"EnrollmentDetailsModal",props:{show:{type:Boolean,default:!1},user:{type:Object,default:null},courseName:{type:String,default:""}},emits:["close"],methods:{getEnrolmentMethod(e){if(console.log("EnrollmentDetailsModal - Método de inscrição recebido:",e),!e)return"Não disponível";switch(e){case"offer_manual":return"Inscrição manual";case"offer_self":return"Autoinscrição";default:return e}}}},LE={class:"modal-header"},UE={key:0,class:"modal-body"},BE={class:"details-container"},$E={class:"detail-row"},qE={class:"detail-value"},HE={class:"detail-row"},WE={class:"detail-value"},jE={class:"detail-row"},zE={class:"detail-value"},GE={class:"detail-row"},KE={class:"detail-value"},ZE={class:"detail-row"},YE={class:"detail-value"},JE={key:1,class:"modal-body no-data"},QE={class:"modal-footer"};function XE(e,t,s,i,n,a){return s.show?(x(),D("div",{key:0,class:"modal-backdrop",onClick:t[3]||(t[3]=u=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[2]||(t[2]=Pt(()=>{},["stop"]))},[f("div",LE,[t[5]||(t[5]=f("h3",{class:"modal-title"},"Informações da matrícula",-1)),f("button",{class:"modal-close",onClick:t[0]||(t[0]=u=>e.$emit("close"))},t[4]||(t[4]=[f("i",{class:"fas fa-times"},null,-1)]))]),s.user?(x(),D("div",UE,[f("div",BE,[f("div",$E,[t[6]||(t[6]=f("div",{class:"detail-label"},"Nome completo",-1)),f("div",qE,q(s.user.fullName),1)]),f("div",HE,[t[7]||(t[7]=f("div",{class:"detail-label"},"Curso",-1)),f("div",WE,q(s.courseName),1)]),f("div",jE,[t[8]||(t[8]=f("div",{class:"detail-label"},"Método de inscrição",-1)),f("div",zE,q(a.getEnrolmentMethod(s.user.enrol)),1)]),f("div",GE,[t[9]||(t[9]=f("div",{class:"detail-label"},"Estado",-1)),f("div",KE,[f("span",{class:de(["status-tag",s.user.status===0?"status-ativo":"status-inativo"])},q(s.user.statusName),3)])]),f("div",ZE,[t[10]||(t[10]=f("div",{class:"detail-label"},"Matrícula criada",-1)),f("div",YE,q(s.user.createdDate),1)])])])):(x(),D("div",JE,"Nenhum dado disponível")),f("div",QE,[f("button",{class:"btn btn-secondary",onClick:t[1]||(t[1]=u=>e.$emit("close"))}," Cancelar ")])])])):Z("",!0)}const eO=Pe(FE,[["render",XE],["__scopeId","data-v-030365c3"]]),vM="",tO={name:"EditEnrollmentModal",components:{CustomSelect:Qs},props:{show:{type:Boolean,default:!1},user:{type:Object,default:null},offerclassid:{type:[Number,String],required:!0}},emits:["close","success","error"],data(){return{isSubmitting:!1,formData:{status:"1",enableStartDate:!0,startDateStr:"",startTimeStr:"00:00",enableEndDate:!1,endDateStr:"",endTimeStr:"00:00",validityPeriod:"unlimited"},statusOptions:[{value:0,label:"Ativo"},{value:1,label:"Suspenso"}],validityPeriodOptions:[{value:"unlimited",label:"Ilimitado"},...Array.from({length:365},(e,t)=>{const s=t+1;return{value:s.toString(),label:s===1?"1 dia":`${s} dias`}})]}},watch:{show(e){e&&this.user&&this.initializeForm()},user(e){e&&this.show&&this.initializeForm()}},methods:{getEnrolmentMethod(e){if(!e)return"Não disponível";switch(e){case"offer_manual":return"Inscrição manual";case"offer_self":return"Autoinscrição";default:return e}},initializeForm(){if(!this.user)return;this.formData.status=this.user.status;const e=this.user.timestart,t=e?new Date(e*1e3):new Date;this.formData.startDateStr=this.formatDateForInput(t),this.formData.startTimeStr=this.formatTimeForInput(t),this.formData.enableStartDate=!0;const s=this.validityPeriodOptions.filter(i=>i.value!=="unlimited");if(this.user.timeend){const i=new Date(this.user.timeend*1e3);this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.enableEndDate=this.user.timeend>0;const a=i-t,u=Math.ceil(a/(1e3*60*60*24)),c=s.find(h=>parseInt(h.value)===u);this.formData.validityPeriod=c?c.value:"unlimited"}else{const i=new Date;i.setMonth(i.getMonth()+3),this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.validityPeriod="unlimited",this.formData.enableEndDate=!1}},handleValidityPeriodChange(){if(this.formData.validityPeriod!=="unlimited"){this.formData.enableEndDate=!1;const e=this.formData.enableStartDate&&this.formData.startDateStr?new Date(this.formData.startDateStr):new Date,t=parseInt(this.formData.validityPeriod),s=new Date(e);s.setDate(s.getDate()+t),this.formData.endDateStr=this.formatDateForInput(s),this.formData.endTimeStr=this.formData.startTimeStr}},handleEnableEndDateChange(){this.formData.enableEndDate&&(this.formData.validityPeriod="unlimited")},formatDateForInput(e){const t=e.getFullYear(),s=String(e.getMonth()+1).padStart(2,"0"),i=String(e.getDate()).padStart(2,"0");return`${t}-${s}-${i}`},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},async saveChanges(){var e;if((e=this.user)!=null&&e.offeruserenrolid)try{this.isSubmitting=!0;const t=Number(this.formData.status)||0,s=this.getStartTimestamp(),i=this.getEndTimestamp(s);if(s>i&&i!==0){this.$emit("error","A data de início da matrícula deve ser menor que a data de fim da matrícula.");return}await mw({offeruserenrolid:this.user.offeruserenrolid,status:t,timestart:s,timeend:i})?(this.$emit("success",{userId:this.user.id,offeruserenrolid:this.user.offeruserenrolid,status:t,timestart:s,timeend:i}),this.$emit("close")):this.$emit("error","Não foi possível editar a matrícula. Por favor, tente novamente.")}catch{this.$emit("error","Ocorreu um erro ao editar a matrícula. Por favor, tente novamente.")}finally{this.isSubmitting=!1}},getStartTimestamp(){if(this.formData.enableStartDate&&this.formData.startDateStr){const e=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr);return Math.floor(e.getTime()/1e3)}return 0},getEndTimestamp(e){if(this.formData.enableEndDate&&this.formData.endDateStr){const t=this.parseDateTime(this.formData.endDateStr,this.formData.endTimeStr);return Math.floor(t.getTime()/1e3)}if(this.formData.validityPeriod!=="unlimited"){const t=parseInt(this.formData.validityPeriod);if(this.formData.enableStartDate&&this.formData.startDateStr){const s=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr),i=new Date(s);return i.setDate(i.getDate()+t),Math.floor(i.getTime()/1e3)}}return 0},parseDateTime(e,t){const[s,i,n]=e.split("-").map(Number),[a,u]=t.split(":").map(Number);return new Date(s,i-1,n,a,u,0,0)}}},sO={class:"modal-header"},rO={class:"modal-title"},nO={class:"modal-body"},oO={class:"enrollment-form"},iO={class:"form-row"},aO={class:"form-value"},lO={class:"form-row"},uO={class:"form-field"},cO={class:"select-wrapper"},dO={class:"form-row"},fO={class:"form-field date-time-field"},hO={class:"date-field"},pO={class:"time-field"},mO={class:"enable-checkbox"},gO={class:"form-row"},_O={class:"form-field"},vO={class:"select-wrapper"},yO={class:"form-row"},bO={class:"date-field"},CO=["disabled"],wO={class:"time-field"},EO=["disabled"],OO={class:"enable-checkbox"},xO={class:"form-row"},SO={class:"form-value"},DO={class:"modal-footer"},TO={class:"footer-buttons"},NO=["disabled"];function IO(e,t,s,i,n,a){const u=z("CustomSelect");return s.show?(x(),D("div",{key:0,class:"modal-backdrop",onClick:t[15]||(t[15]=c=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[14]||(t[14]=Pt(()=>{},["stop"]))},[f("div",sO,[f("h3",rO," Editar matrícula de "+q(s.user?s.user.fullName:""),1),f("button",{class:"modal-close",onClick:t[0]||(t[0]=c=>e.$emit("close"))},t[16]||(t[16]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",nO,[f("div",oO,[f("div",iO,[t[17]||(t[17]=f("div",{class:"form-label"},"Método de inscrição",-1)),f("div",aO,q(a.getEnrolmentMethod(s.user&&s.user.enrol?s.user.enrol:"")),1)]),f("div",lO,[t[18]||(t[18]=f("div",{class:"form-label"},"Estado",-1)),f("div",uO,[f("div",cO,[A(u,{modelValue:n.formData.status,"onUpdate:modelValue":t[1]||(t[1]=c=>n.formData.status=c),options:n.statusOptions,width:120,class:"smaller-select"},null,8,["modelValue","options"])])])]),f("div",dO,[t[20]||(t[20]=f("div",{class:"form-label"},"Matrícula começa",-1)),f("div",fO,[f("div",hO,[lt(f("input",{type:"date","onUpdate:modelValue":t[2]||(t[2]=c=>n.formData.startDateStr=c),class:"form-control",onChange:t[3]||(t[3]=(...c)=>e.handleStartDateChange&&e.handleStartDateChange(...c))},null,544),[[Zt,n.formData.startDateStr]])]),f("div",pO,[lt(f("input",{type:"time","onUpdate:modelValue":t[4]||(t[4]=c=>n.formData.startTimeStr=c),class:"form-control",onChange:t[5]||(t[5]=(...c)=>e.handleStartTimeChange&&e.handleStartTimeChange(...c))},null,544),[[Zt,n.formData.startTimeStr]])]),f("div",mO,[lt(f("input",{type:"checkbox",id:"enable-start-date","onUpdate:modelValue":t[6]||(t[6]=c=>n.formData.enableStartDate=c),class:"custom-checkbox"},null,512),[[Hi,n.formData.enableStartDate]]),t[19]||(t[19]=f("label",{for:"enable-start-date"},"Habilitar",-1))])])]),f("div",gO,[t[21]||(t[21]=f("div",{class:"form-label"},"Período de validade da matrícula",-1)),f("div",_O,[f("div",vO,[A(u,{modelValue:n.formData.validityPeriod,"onUpdate:modelValue":t[7]||(t[7]=c=>n.formData.validityPeriod=c),options:n.validityPeriodOptions,width:120,class:"smaller-select",onChange:a.handleValidityPeriodChange,disabled:n.formData.enableEndDate},null,8,["modelValue","options","onChange","disabled"])])])]),f("div",yO,[t[23]||(t[23]=f("div",{class:"form-label"},"Matrícula termina",-1)),f("div",{class:de(["form-field date-time-field",{"disabled-inputs-only":!n.formData.enableEndDate}])},[f("div",bO,[lt(f("input",{type:"date","onUpdate:modelValue":t[8]||(t[8]=c=>n.formData.endDateStr=c),class:"form-control",disabled:!n.formData.enableEndDate},null,8,CO),[[Zt,n.formData.endDateStr]])]),f("div",wO,[lt(f("input",{type:"time","onUpdate:modelValue":t[9]||(t[9]=c=>n.formData.endTimeStr=c),class:"form-control",disabled:!n.formData.enableEndDate},null,8,EO),[[Zt,n.formData.endTimeStr]])]),f("div",OO,[lt(f("input",{type:"checkbox",id:"enable-enddate","onUpdate:modelValue":t[10]||(t[10]=c=>n.formData.enableEndDate=c),class:"custom-checkbox",onChange:t[11]||(t[11]=(...c)=>a.handleEnableEndDateChange&&a.handleEnableEndDateChange(...c))},null,544),[[Hi,n.formData.enableEndDate]]),t[22]||(t[22]=f("label",{for:"enable-enddate"},"Habilitar",-1))])],2)]),f("div",xO,[t[24]||(t[24]=f("div",{class:"form-label"},"Matrícula criada",-1)),f("div",SO,q(s.user&&s.user.createdDate?s.user.createdDate:"Não disponível"),1)])])]),f("div",DO,[t[25]||(t[25]=f("div",{class:"footer-spacer"},null,-1)),f("div",TO,[f("button",{class:"btn btn-primary",onClick:t[12]||(t[12]=(...c)=>a.saveChanges&&a.saveChanges(...c)),disabled:n.isSubmitting},q(n.isSubmitting?"Salvando...":"Salvar mudanças"),9,NO),f("button",{class:"btn btn-secondary",onClick:t[13]||(t[13]=c=>e.$emit("close"))}," Cancelar ")])])])])):Z("",!0)}const AO=Pe(tO,[["render",IO],["__scopeId","data-v-24ba0708"]]),yM="",bM="",MO={name:"BulkEditEnrollmentModal",components:{Pagination:gn,CustomTable:mn,CustomSelect:Qs},props:{show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]},offerclassid:{type:[Number,String],required:!0}},emits:["close","success","error"],data(){return{isSubmitting:!1,formData:{status:"1",startDateStr:"",startTimeStr:"00:00",enableStartDate:!1,endDateStr:"",endTimeStr:"23:59",enableEndDate:!1},statusOptions:[{value:1,label:"Ativo"},{value:0,label:"Suspenso"}],currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"statusName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}},watch:{show(e){e&&this.initializeForm()}},methods:{initializeForm(){const e=new Date;this.formData={status:"1",startDateStr:this.formatDateForInput(e),startTimeStr:"00:00",enableStartDate:!1,endDateStr:this.formatDateForInput(e),endTimeStr:"23:59",enableEndDate:!1}},formatDateForInput(e){return e.toISOString().split("T")[0]},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},handleStartDateChange(){},handleStartTimeChange(){},handleEndDateChange(){},handleEndTimeChange(){},async saveChanges(){if(!this.users||this.users.length===0){console.error("Nenhum usuário selecionado"),this.$emit("error","Nenhum usuário selecionado para edição em lote.");return}try{this.isSubmitting=!0;const e=parseInt(this.formData.status);let t=0;if(this.formData.enableStartDate&&this.formData.startDateStr){const[a,u,c]=this.formData.startDateStr.split("-").map(Number),[h,_]=this.formData.startTimeStr.split(":").map(Number),p=new Date(a,u-1,c,h,_,0,0);t=Math.floor(p.getTime()/1e3);const g=p.getTimezoneOffset()*60;t+=g}let s=0;if(this.formData.enableEndDate&&this.formData.endDateStr){const[a,u,c]=this.formData.endDateStr.split("-").map(Number),[h,_]=this.formData.endTimeStr.split(":").map(Number),p=new Date(a,u-1,c,h,_,0,0);s=Math.floor(p.getTime()/1e3);const g=p.getTimezoneOffset()*60;s+=g}const i=this.users.filter(a=>a.offeruserenrolid).map(a=>a.offeruserenrolid);if(i.length===0){console.error("Nenhum ID de matrícula encontrado"),this.$emit("error","Não foi possível encontrar os IDs das matrículas dos usuários selecionados.");return}const n=await gw({offeruserenrolids:i,status:e,timestart:t,timeend:s});if(Array.isArray(n)&&n.length>0){const a=n.filter(h=>h.operation_status).length,u=n.length-a;let c="";if(a===n.length)c=`${a} matrícula(s) editada(s) com sucesso.`;else if(a>0)c=`${a} de ${n.length} matrícula(s) editada(s) com sucesso. ${u} matrícula(s) não puderam ser editadas.`;else{c="Nenhuma matrícula pôde ser editada.",this.$emit("error",c);return}this.$emit("success",{message:c,count:a,total:n.length}),this.$emit("close")}else console.error("Resposta inválida da API:",n),this.$emit("error","Não foi possível editar as matrículas. Por favor, tente novamente.")}catch(e){console.error("Erro ao salvar alterações:",e),this.$emit("error","Ocorreu um erro ao editar as matrículas. Por favor, tente novamente.")}finally{this.isSubmitting=!1}}}},PO={class:"modal-header"},kO={class:"modal-body"},VO={class:"enrollment-form"},RO={class:"table-container"},FO={class:"form-row"},LO={class:"form-field"},UO={class:"select-wrapper"},BO={class:"form-row"},$O={class:"form-field date-time-field"},qO={class:"date-field"},HO=["disabled"],WO={class:"time-field"},jO=["disabled"],zO={class:"enable-checkbox"},GO={class:"form-row"},KO={class:"form-field date-time-field"},ZO={class:"date-field"},YO=["disabled"],JO={class:"time-field"},QO=["disabled"],XO={class:"enable-checkbox"},ex={class:"modal-footer"},tx={class:"footer-buttons"},sx=["disabled"];function rx(e,t,s,i,n,a){const u=z("CustomTable"),c=z("Pagination"),h=z("CustomSelect");return s.show?(x(),D("div",{key:0,class:"modal-backdrop",onClick:t[17]||(t[17]=_=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[16]||(t[16]=Pt(()=>{},["stop"]))},[f("div",PO,[t[19]||(t[19]=f("h3",{class:"modal-title"},"Edição de Matrículas em Lote",-1)),f("button",{class:"modal-close",onClick:t[0]||(t[0]=_=>e.$emit("close"))},t[18]||(t[18]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",kO,[f("div",VO,[f("div",null,[f("div",RO,[A(u,{headers:n.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?lt((x(),dt(c,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[1]||(t[1]=_=>n.currentPage=_),"per-page":n.perPage,"onUpdate:perPage":t[2]||(t[2]=_=>n.perPage=_),total:s.users.length},null,8,["current-page","per-page","total"])),[[Kl,s.users.length>n.perPage]]):Z("",!0),t[20]||(t[20]=f("span",{class:"d-block w-100 border-bottom mt-4"},null,-1))]),f("div",FO,[t[21]||(t[21]=f("div",{class:"form-label"},"Alterar o status",-1)),f("div",LO,[f("div",UO,[A(h,{modelValue:n.formData.status,"onUpdate:modelValue":t[3]||(t[3]=_=>n.formData.status=_),options:n.statusOptions,width:235,class:"smaller-select"},null,8,["modelValue","options"])])])]),f("div",BO,[t[23]||(t[23]=f("div",{class:"form-label"},"Alterar data de início",-1)),f("div",$O,[f("div",qO,[lt(f("input",{type:"date","onUpdate:modelValue":t[4]||(t[4]=_=>n.formData.startDateStr=_),class:"form-control",onChange:t[5]||(t[5]=(..._)=>a.handleStartDateChange&&a.handleStartDateChange(..._)),disabled:!n.formData.enableStartDate},null,40,HO),[[Zt,n.formData.startDateStr]])]),f("div",WO,[lt(f("input",{type:"time","onUpdate:modelValue":t[6]||(t[6]=_=>n.formData.startTimeStr=_),class:"form-control",onChange:t[7]||(t[7]=(..._)=>a.handleStartTimeChange&&a.handleStartTimeChange(..._)),disabled:!n.formData.enableStartDate},null,40,jO),[[Zt,n.formData.startTimeStr]])]),f("div",zO,[lt(f("input",{type:"checkbox",id:"enable-start-date","onUpdate:modelValue":t[8]||(t[8]=_=>n.formData.enableStartDate=_),class:"custom-checkbox"},null,512),[[Hi,n.formData.enableStartDate]]),t[22]||(t[22]=f("label",{for:"enable-start-date"},"Habilitar",-1))])])]),f("div",GO,[t[25]||(t[25]=f("div",{class:"form-label"},"Alterar data de fim",-1)),f("div",KO,[f("div",ZO,[lt(f("input",{type:"date","onUpdate:modelValue":t[9]||(t[9]=_=>n.formData.endDateStr=_),class:"form-control",onChange:t[10]||(t[10]=(..._)=>a.handleEndDateChange&&a.handleEndDateChange(..._)),disabled:!n.formData.enableEndDate},null,40,YO),[[Zt,n.formData.endDateStr]])]),f("div",JO,[lt(f("input",{type:"time","onUpdate:modelValue":t[11]||(t[11]=_=>n.formData.endTimeStr=_),class:"form-control",onChange:t[12]||(t[12]=(..._)=>a.handleEndTimeChange&&a.handleEndTimeChange(..._)),disabled:!n.formData.enableEndDate},null,40,QO),[[Zt,n.formData.endTimeStr]])]),f("div",XO,[lt(f("input",{type:"checkbox",id:"enable-end-date","onUpdate:modelValue":t[13]||(t[13]=_=>n.formData.enableEndDate=_),class:"custom-checkbox"},null,512),[[Hi,n.formData.enableEndDate]]),t[24]||(t[24]=f("label",{for:"enable-end-date"},"Habilitar",-1))])])])])]),f("div",ex,[t[26]||(t[26]=f("div",{class:"footer-spacer"},null,-1)),f("div",tx,[f("button",{class:"btn btn-primary",onClick:t[14]||(t[14]=(..._)=>a.saveChanges&&a.saveChanges(..._)),disabled:n.isSubmitting},q(n.isSubmitting?"Salvando...":"Salvar mudanças"),9,sx),f("button",{class:"btn btn-secondary",onClick:t[15]||(t[15]=_=>e.$emit("close"))}," Cancelar ")])])])])):Z("",!0)}const nx=Pe(MO,[["render",rx],["__scopeId","data-v-92e8899f"]]),CM="",ox={name:"BulkDeleteEnrollmentModal",components:{Pagination:gn,CustomSelect:Qs,CustomTable:mn},props:{show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]},offerclassid:{type:[Number,String],required:!0}},emits:["close","confirm","error"],data(){return{isSubmitting:!1,currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"statusName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}}},ix={class:"modal-header"},ax={class:"modal-body"},lx={class:"enrollment-form"},ux={class:"table-container"},cx={class:"modal-footer"},dx={class:"footer-buttons"},fx=["disabled"];function hx(e,t,s,i,n,a){const u=z("CustomTable"),c=z("Pagination");return s.show?(x(),D("div",{key:0,class:"modal-backdrop",onClick:t[6]||(t[6]=h=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[5]||(t[5]=Pt(()=>{},["stop"]))},[f("div",ix,[t[8]||(t[8]=f("h3",{class:"modal-title"},"Remoção de Matrículas",-1)),f("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[7]||(t[7]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",ax,[f("div",lx,[f("div",ux,[A(u,{headers:n.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?lt((x(),dt(c,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[1]||(t[1]=h=>n.currentPage=h),"per-page":n.perPage,"onUpdate:perPage":t[2]||(t[2]=h=>n.perPage=h),total:s.users.length},null,8,["current-page","per-page","total"])),[[Kl,s.users.length>n.perPage]]):Z("",!0)]),t[9]||(t[9]=f("div",{class:"text-center mt-5"},[f("h5",{class:"mt-1"}," Tem certeza de que deseja excluir essas inscrições de usuário? ")],-1))]),f("div",cx,[f("div",dx,[f("button",{class:"btn btn-primary",onClick:t[3]||(t[3]=h=>e.$emit("confirm")),disabled:n.isSubmitting},q(n.isSubmitting?"Removendo...":"Remover matrículas"),9,fx),f("button",{class:"btn btn-secondary",onClick:t[4]||(t[4]=h=>e.$emit("close"))}," Cancelar ")])])])])):Z("",!0)}const px=Pe(ox,[["render",hx],["__scopeId","data-v-37ea04c6"]]),wM="",mx={name:"BackButton",props:{label:{type:String,default:"Voltar"},route:{type:String,default:"/local/offermanager/"}},methods:{goBack(){this.$emit("click")}}};function gx(e,t,s,i,n,a){return x(),D("button",{class:"btn-back",onClick:t[0]||(t[0]=(...u)=>a.goBack&&a.goBack(...u))},[t[1]||(t[1]=f("i",{class:"fas fa-angle-left"},null,-1)),We(" "+q(s.label),1)])}const jo=Pe(mx,[["render",gx],["__scopeId","data-v-c577f103"]]),EM="",_x={name:"UserAvatar",props:{imageUrl:{type:String,default:""},fullName:{type:String,required:!0},size:{type:Number,default:32}},computed:{hasImage(){return!!this.imageUrl},initials(){if(!this.fullName)return"";const e=this.fullName.split(" ").filter(i=>i.length>0);if(e.length===0)return"";if(e.length===1)return e[0].substring(0,2).toUpperCase();const t=e[0].charAt(0),s=e[e.length-1].charAt(0);return(t+s).toUpperCase()},backgroundColor(){const e=["#1976D2","#388E3C","#D32F2F","#7B1FA2","#FFA000","#0097A7","#E64A19","#5D4037","#455A64","#616161"];let t=0;for(let i=0;i<this.fullName.length;i++)t=this.fullName.charCodeAt(i)+((t<<5)-t);const s=Math.abs(t)%e.length;return e[s]},avatarStyle(){return{width:`${this.size}px`,height:`${this.size}px`,minWidth:`${this.size}px`,minHeight:`${this.size}px`}}}},vx=["src"];function yx(e,t,s,i,n,a){return x(),D("div",{class:"user-avatar",style:is(a.avatarStyle)},[a.hasImage?(x(),D("img",{key:0,src:s.imageUrl,alt:"Foto de perfil",class:"avatar-image"},null,8,vx)):(x(),D("div",{key:1,class:"avatar-initials",style:is({backgroundColor:a.backgroundColor})},q(a.initials),5))],4)}const bx=Pe(_x,[["render",yx],["__scopeId","data-v-eed19d8a"]]),OM="",Cx={name:"RoleSelector",props:{userId:{type:[Number,String],required:!0},offeruserenrolid:{type:[Number,String],required:!0},currentRole:{type:[String,Array],required:!0},offerclassid:{type:[Number,String],required:!0}},data(){return{isEditing:!1,selectedRoles:[],roles:[],loading:!1,initialLoading:!0}},computed:{displayRoleNames(){return Array.isArray(this.currentRole)?this.currentRole.join(", "):String(this.currentRole||"")}},mounted(){this.loadRoles()},methods:{async loadRoles(){var e;this.initialLoading||(this.loading=!0);try{const t=await pu(parseInt(this.offerclassid)),s=((e=t==null?void 0:t.data)==null?void 0:e.offercourseid)||t.offercourseid;if(!s)throw new Error("offercourseid não encontrado");const i=await mu(s);this.roles=Array.isArray(i==null?void 0:i.data)?i.data:Array.isArray(i)?i:[];const n=await vw(this.offeruserenrolid);if(Array.isArray(n)&&n.length)this.selectedRoles=n.map(a=>a.id);else if(Array.isArray(this.currentRole))this.selectedRoles=this.roles.filter(a=>this.currentRole.includes(a.name)).map(a=>a.id);else if(this.currentRole){const a=this.roles.find(u=>u.name.toLowerCase()===String(this.currentRole).toLowerCase());a&&(this.selectedRoles=[a.id])}}catch{this.$emit("error","Não foi possível carregar papéis.")}finally{this.loading=!1,this.initialLoading=!1}},startEditing(){this.isEditing=!0,this.$nextTick(()=>{var e;return(e=this.$refs.roleSelect)==null?void 0:e.focus()})},cancelEdit(){this.isEditing=!1},close(){this.isEditing&&(this.isEditing=!1)},async saveRoles(){if(!this.selectedRoles.length){this.$emit("error","Selecione ao menos um papel.");return}this.loading=!0;try{const e=await yw(this.offeruserenrolid,this.selectedRoles.map(t=>parseInt(t)));if(e===!0||e&&e.error===!1||e&&e.success===!0){const t=this.roles.filter(s=>this.selectedRoles.includes(s.id)).map(s=>s.name);this.$emit("success",{userId:this.userId,offeruserenrolid:this.offeruserenrolid,roleids:this.selectedRoles,roleNames:t}),this.isEditing=!1,this.$emit("reload-table")}else throw new Error("Resposta inesperada do servidor: "+JSON.stringify(e))}catch(e){console.error("Erro ao salvar papéis:",e),this.$emit("error","Não foi possível salvar papéis.")}finally{this.loading=!1}}}},wx={class:"role-selector"},Ex={key:1,class:"role-edit-wrapper"},Ox={class:"role-edit-container"},xx={class:"select-wrapper"},Sx=["value"],Dx={class:"role-actions"},Tx={key:2,class:"loading-overlay"};function Nx(e,t,s,i,n,a){return x(),D("div",wx,[n.isEditing?(x(),D("div",Ex,[f("div",Ox,[f("div",xx,[lt(f("select",{"onUpdate:modelValue":t[1]||(t[1]=u=>n.selectedRoles=u),class:"role-select",ref:"roleSelect",multiple:"",onClick:t[2]||(t[2]=Pt(()=>{},["stop"])),style:is({height:Math.max(4,n.roles.length)*25+"px"})},[(x(!0),D(Ne,null,it(n.roles,u=>(x(),D("option",{key:u.id,value:u.id},q(u.name),9,Sx))),128))],4),[[Jl,n.selectedRoles]])]),f("div",Dx,[f("button",{class:"btn-save",onClick:t[3]||(t[3]=Pt((...u)=>a.saveRoles&&a.saveRoles(...u),["stop"])),title:"Salvar"},t[6]||(t[6]=[f("i",{class:"fas fa-check"},null,-1)])),f("button",{class:"btn-cancel",onClick:t[4]||(t[4]=Pt((...u)=>a.cancelEdit&&a.cancelEdit(...u),["stop"])),title:"Cancelar"},t[7]||(t[7]=[f("i",{class:"fas fa-times"},null,-1)]))])])])):(x(),D("div",{key:0,class:"role-display",onClick:t[0]||(t[0]=Pt((...u)=>a.startEditing&&a.startEditing(...u),["stop"]))},[f("span",null,q(a.displayRoleNames),1),t[5]||(t[5]=f("i",{class:"fas fa-pencil-alt edit-icon","aria-hidden":"true"},null,-1))])),n.loading&&n.isEditing?(x(),D("div",Tx,t[8]||(t[8]=[f("div",{class:"spinner"},null,-1)]))):Z("",!0)])}const Ix=Pe(Cx,[["render",Nx],["__scopeId","data-v-217c6284"]]),xM="",Ax={name:"Enrollments",components:{CustomTable:mn,CustomSelect:Qs,HierarchicalSelect:Dw,CustomInput:qn,CustomCheckbox:Ho,CustomButton:mr,FilterSection:tp,FilterRow:ta,FilterGroup:sa,FilterActions:sp,FilterTag:Wo,FilterTags:oa,Pagination:gn,PageHeader:Hn,ConfirmationModal:na,Autocomplete:_n,EnrolmentModalNew:RE,EnrollmentDetailsModal:eO,Toast:Br,EditEnrollmentModal:AO,BulkEditEnrollmentModal:nx,BulkDeleteEnrollmentModal:px,BackButton:jo,UserAvatar:bx,RoleSelector:Ix,LFLoading:ra},data(){return{offerid:null,offerclassid:null,offercourseid:null,courseid:null,courseContextId:null,filteredUsers:[],nameOptions:[],cpfOptions:[],emailOptions:[],nameSearchInput:"",cpfSearchInput:"",emailSearchInput:"",showNameDropdown:!1,showCpfDropdown:!1,showEmailDropdown:!1,nameDebounceTimer:null,cpfDebounceTimer:null,emailDebounceTimer:null,tableHeaders:[{text:"",value:"select",sortable:!1,width:"50px"},{text:"NOME/SOBRENOME",value:"fullName",sortable:!0,width:"220px"},{text:"E-MAIL",value:"email",sortable:!0},{text:"CPF",value:"cpf",sortable:!0},{text:"PAPÉIS",value:"roles",sortable:!1},{text:"GRUPOS",value:"groups",sortable:!1},{text:"DATA INÍCIO DA MATRÍCULA",value:"startDate",sortable:!0},{text:"DATA FIM DA MATRÍCULA",value:"endDate",sortable:!0},{text:"PRAZO DE CONCLUSÃO",value:"deadline",sortable:!0},{text:"PROGRESSO",value:"progress",sortable:!1},{text:"SITUAÇÃO DE MATRÍCULA",value:"situation",sortable:!0},{text:"NOTA",value:"grade",sortable:!1},{text:"ESTADO",value:"status",sortable:!0}],enrolments:[],totalEnrolments:0,loading:!1,error:null,currentPage:1,perPage:10,sortBy:"fullName",sortDesc:!1,showBulkDeleteEnrollmentModal:!1,showEnrollmentModal:!1,selectedUser:null,showEnrolmentModal:!1,roleOptions:[],showEditEnrollmentModal:!1,showBulkEditEnrollmentModal:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,classDetails:{},selectedUsers:[],selectedBulkAction:"",selectedPageView:"usuarios_matriculados",pageViewOptions:[{value:"matriculas",label:"Matrículas",children:[{value:"usuarios_matriculados",label:"Usuários matriculados"}]},{value:"grupos",label:"Grupos",children:[{value:"grupos",label:"Grupos"},{value:"agrupamentos",label:"Agrupamentos"},{value:"visao_geral",label:"Visão geral"}]},{value:"permissoes",label:"Permissões",children:[{value:"permissoes",label:"Permissões"},{value:"outros_usuarios",label:"Outros usuários"},{value:"verificar_permissoes",label:"Verificar permissões"}]}]}},setup(){return{router:Jh()}},async created(){var t,s,i,n;if(this.offerclassid=this.offerclassid??this.$route.params.offerclassid,!this.offerclassid)throw new Error("ID da turma não foi definido.");this.offerclassid=parseInt(this.offerclassid);const e=await pu(this.offerclassid);if(e.error)throw new Error("Erro ao requisitar informações da turma");this.classDetails=e.data,this.offerid=parseInt((t=this.classDetails)==null?void 0:t.offerid),this.offercourseid=parseInt((s=this.classDetails)==null?void 0:s.offercourseid),this.corseid=(i=this.classDetails)==null?void 0:i.courseid,this.courseContextId=(n=this.classDetails)==null?void 0:n.course_context_id,await this.loadRoles(),await this.loadRegisteredUsers()},beforeUnmount(){this.nameDebounceTimer&&clearTimeout(this.nameDebounceTimer),this.cpfDebounceTimer&&clearTimeout(this.cpfDebounceTimer),this.emailDebounceTimer&&clearTimeout(this.emailDebounceTimer)},computed:{allSelected(){return this.enrolments.length>0&&this.selectedUsers.length===this.enrolments.length},someSelected(){return this.selectedUsers.length>0&&!this.allSelected},excludedUserIds(){return this.filteredUsers.map(e=>e.id||e.value)}},watch:{perPage(e,t){e!==t&&(this.currentPage=1,this.selectedUsers=[],this.loadRegisteredUsers())},currentPage(e,t){e!==t&&this.loadRegisteredUsers()}},methods:{async loadRegisteredUsers(){this.loading=!0,this.error=null;let e=[];this.filteredUsers.length>0&&(e=this.excludedUserIds);const t={offerclassid:this.offerclassid,userids:e,page:this.currentPage,perpage:this.perPage,orderby:this.mapSortFieldToBackend(this.sortBy||"fullName"),direction:this.sortDesc?"DESC":"ASC"},s=await fw(t);if(s.data){const i=s.data;Array.isArray(i.enrolments)&&(this.enrolments=i.enrolments.map(n=>({id:n.userid,offeruserenrolid:n.offeruserenrolid,fullName:n.fullname,email:n.email,cpf:n.cpf,enrol:n.enrol,roles:this.formatRoles(n.roles),groups:n.groups,timecreated:n.timecreated,createdDate:this.formatDateTime(n.timecreated),timestart:n.timestart,timeend:n.timeend,startDate:this.formatDate(n.timestart),endDate:this.formatDate(n.timeend),deadline:n.enrolperiod,progress:this.formatProgress(n.progress),situation:n.situation,situationName:n.situation_name,grade:n.grade||"-",status:n.status,statusName:n.status!==void 0?n.status===0?"Ativo":"Suspenso":"-"})),this.totalEnrolments=i.total||this.enrolments.length)}else this.enrolments=[],this.totalEnrolments=0;this.loading=!1},formatDate(e){return!e||e===0?"-":new Date(e*1e3).toLocaleDateString("pt-BR")},formatDateTime(e,t={}){return!e||e===0?"-":(Object.keys(t).length===0&&(t={day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}),new Date(e*1e3).toLocaleString("pt-BR",t))},formatProgress(e){return e==null?"-":Math.round(e)+"%"},formatRoles(e){return!e||e==="-"?"-":typeof e=="string"?e.split(",").join(", "):Array.isArray(e)&&e.length>0&&typeof e[0]=="object"&&e[0].name?e.map(t=>t.name).join(", "):Array.isArray(e)?e.join(", "):"-"},async loadNameOptions(e){if(!e||e.length<3){this.nameOptions=[],this.showNameDropdown=!1;return}try{const t=await gu({offerclassid:this.offerclassid,fieldstring:"name",searchstring:e,excludeduserids:this.excludedUserIds});t.data?(this.nameOptions=t.data.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showNameDropdown=this.nameOptions.length>0):(this.nameOptions=[],this.showNameDropdown=!1)}catch{this.nameOptions=[],this.showNameDropdown=!1}},async loadCpfOptions(e){if(!e||e.length<3){this.cpfOptions=[],this.showCpfDropdown=!1;return}try{const t=await gu({offerclassid:this.offerclassid,fieldstring:"username",searchstring:e,excludeduserids:this.excludedUserIds});t.data?(this.cpfOptions=t.data.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showCpfDropdown=this.cpfOptions.length>0):(this.cpfOptions=[],this.showCpfDropdown=!1)}catch{this.cpfOptions=[],this.showCpfDropdown=!1}},async loadEmailOptions(e){if(!e||e.length<3){this.emailOptions=[],this.showEmailDropdown=!1;return}try{const t=await gu({offerclassid:this.offerclassid,fieldstring:"email",searchstring:e,excludeduserids:this.excludedUserIds});!t.error&&t.data?(this.emailOptions=t.data.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showEmailDropdown=this.emailOptions.length>0):(this.emailOptions=[],this.showEmailDropdown=!1)}catch{this.emailOptions=[],this.showEmailDropdown=!1}},handleNameInput(){this.nameDebounceTimer&&clearTimeout(this.nameDebounceTimer),this.nameSearchInput.length>=3?this.nameDebounceTimer=setTimeout(()=>{this.loadNameOptions(this.nameSearchInput)},500):this.showNameDropdown=!1},handleCpfInput(){this.cpfDebounceTimer&&clearTimeout(this.cpfDebounceTimer),this.cpfSearchInput.length>=3?this.cpfDebounceTimer=setTimeout(()=>{this.loadCpfOptions(this.cpfSearchInput)},500):this.showCpfDropdown=!1},handleEmailInput(){this.emailDebounceTimer&&clearTimeout(this.emailDebounceTimer),this.emailSearchInput.length>=3?this.emailDebounceTimer=setTimeout(()=>{this.loadEmailOptions(this.emailSearchInput)},500):this.showEmailDropdown=!1},selectNameOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"name"}),this.nameSearchInput="",this.showNameDropdown=!1,this.clearOptions(),this.loadRegisteredUsers()},selectCpfOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"cpf"}),this.cpfSearchInput="",this.showCpfDropdown=!1,this.clearOptions(),this.loadRegisteredUsers()},selectEmailOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"email"}),this.emailSearchInput="",this.showEmailDropdown=!1,this.clearOptions(),this.loadRegisteredUsers()},clearOptions(e){setTimeout(()=>{switch(e){case"name":this.nameOptions=[];break;case"cpf":this.cpfOptions=[];break;case"email":this.emailOptions=[];break;default:this.nameOptions=[],this.cpfOptions=[],this.emailOptions=[];break}},500)},removeFilter(e){const t=this.filteredUsers.findIndex(s=>s.id===e||s.value===e);t!==-1&&this.filteredUsers.splice(t,1),this.loadRegisteredUsers()},clearFilteredUsers(){this.filteredUsers=[],this.loadRegisteredUsers()},toggleSelectAll(){this.allSelected?this.selectedUsers=[]:this.selectedUsers=this.enrolments.map(e=>e.id)},toggleSelectUser(e){const t=this.selectedUsers.indexOf(e);t===-1?this.selectedUsers.push(e):this.selectedUsers.splice(t,1)},isSelected(e){return this.selectedUsers.includes(e)},async handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,await this.loadRegisteredUsers()},mapSortFieldToBackend(e){return{fullName:"fullname",email:"email",cpf:"cpf",startDate:"startdate",endDate:"enddate",deadline:"enrolperiod",situation:"situation",status:"status"}[e]||"fullname"},addNewUser(){var e;if(this.classDetails&&((e=this.classDetails)==null?void 0:e.operational_cycle)===2){this.error="Não é possível matricular usuários em uma turma com ciclo operacional encerrado.";return}this.showEnrolmentModal=!0},closeEnrolmentModal(){this.showEnrolmentModal=!1},async goBack(){this.router.push({name:"offer.edit",params:{id:this.offerid}})},viewUserProfile(e){if(!e)return;const t=`/user/view.php?id=${e}&course=${this.courseid}`;window.location.href=t},async handlePageViewChange(e){let t=this.offerclassid,s=this.courseid,i=this.courseContextId;const n={usuarios_matriculados:`/local/offermanager/new-subscribed-users/${t}`,grupos:`/group/index.php?id=${s}`,agrupamentos:`/group/groupings.php?id=${s}`,visao_geral:`/user/index.php?id=${s}`,permissoes:`/admin/roles/permissions.php?contextid=${i}`,outros_usuarios:`/enrol/otherusers.php?id=${s}`,verificar_permissoes:`/admin/roles/check.php?contextid=${i}`};n[e]&&(window.location.href=n[e])},async handleEnrolmentSuccess(){await this.loadRegisteredUsers()},async loadRoles(){const e=await mu(this.offercourseid);if(e.error)throw new Error("Erro ao requisitar papéis do curso");this.roleOptions=e.data.map(t=>({value:t.id,label:t.name}))},showEnrollmentDetails(e){this.selectedUser={fullName:e.fullName,enrol:e.enrol||"Inscrições manuais",status:e.status||0,statusName:e.statusName||"Ativo",startDate:e.startDate||"Não disponível",createdDate:e.createdDate||e.startDate||"Não disponível"},this.showEnrollmentModal=!0},closeEnrollmentModal(){this.showEnrollmentModal=!1,this.selectedUser=null},closeEditEnrollmentModal(){this.showEditEnrollmentModal=!1,this.selectedUser=null},async handleEditEnrollmentSuccess(e){if(this.showSuccessMessage("Matrícula editada com sucesso."),e.roleid){let t=null;if(this.roleOptions&&this.roleOptions.length>0){const i=this.roleOptions.find(n=>n.value===String(e.roleid));i&&(t=i.name)}if(!t){await this.loadRegisteredUsers(),this.showEditEnrollmentModal=!1,this.selectedUser=null;return}const s=this.enrolments.findIndex(i=>i.id===e.userId);if(s!==-1){if(t&&(this.enrolments[s].roles=t),e.status!==void 0&&(this.enrolments[s].status=e.status,e.status===1?this.enrolments[s].statusName="Ativo":e.status===0&&(this.enrolments[s].statusName="Suspenso")),e.timestart){const i=new Date(e.timestart*1e3);this.enrolments[s].startDate=i.toLocaleDateString("pt-BR")}if(e.timeend){const i=new Date(e.timeend*1e3);this.enrolments[s].endDate=i.toLocaleDateString("pt-BR")}}else await this.loadRegisteredUsers()}else await this.loadRegisteredUsers();this.showEditEnrollmentModal=!1,this.selectedUser=null},handleEditEnrollmentError(e){this.showErrorMessage(e||"Não foi possível editar a matrícula. Por favor, tente novamente.")},handleRoleUpdateSuccess(e){this.showSuccessMessage("Papel atualizado com sucesso.");const t=this.enrolments.findIndex(s=>s.id===e.userId);t!==-1?this.enrolments[t].roles=e.roleName:this.reloadTable()},handleRoleUpdateError(e){this.showErrorMessage(e||"Ocorreu um erro ao atualizar o papel do usuário.")},reloadTable(){this.loadRegisteredUsers()},editUser(e){let t=null;e.roles&&e.roleid&&(t=e.roleid),this.selectedUser={id:e.id,offeruserenrolid:e.offeruserenrolid,fullName:e.fullName,enrol:e.enrol,status:e.status,statusName:e.statusName,roles:e.roles,roleid:t,startDate:e.startDate,timestart:e.timestart,timeend:e.timeend,createdDate:e.createdDate||"-"},this.showEditEnrollmentModal=!0},async confirmeBulkDeleteEnrollment(){this.loading=!0;const e=[];for(const i of this.selectedUsers){const n=this.enrolments.find(a=>a.id===i);n&&n.offeruserenrolid&&e.push(n.offeruserenrolid)}if(e.length===0){this.showErrorMessage("Não foi possível encontrar os IDs das matrículas. Por favor, tente novamente."),this.loading=!1;return}const t=`Processando exclusão de ${e.length} matrícula(s)...`;this.showSuccessMessage(t);const s=await _w(e);if(s&&s.length>0){const i=s.filter(a=>a.operation_status).length,n=s.length-i;i>0?(this.showSuccessMessage(`${i} matrícula(s) cancelada(s) com sucesso.${n>0?` ${n} matrícula(s) não puderam ser canceladas.`:""}`),await this.loadRegisteredUsers(),this.selectedUsers=[]):this.showErrorMessage("Não foi possível cancelar as matrículas. Por favor, tente novamente.")}else this.showSuccessMessage(`${e.length} matrícula(s) cancelada(s) com sucesso.`),await this.loadRegisteredUsers(),this.selectedUsers=[];this.showBulkDeleteEnrollmentModal=!1,this.loading=!1},handleBulkAction(){if(this.selectedBulkAction){if(this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para realizar esta ação."),this.selectedBulkAction="";return}switch(this.selectedBulkAction){case"message":this.sendMessage();break;case"note":this.writeNote();break;case"download_csv":this.downloadData("csv");break;case"download_xlsx":this.downloadData("xlsx");break;case"download_html":this.downloadData("html");break;case"download_json":this.downloadData("json");break;case"download_ods":this.downloadData("ods");break;case"download_pdf":this.downloadData("pdf");break;case"edit_enrolment":this.editEnrolments();break;case"delete_enrolment":this.bulkDeleteEnrollment();break}this.selectedBulkAction=""}},sendMessage(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para enviar mensagem.");return}this.showSendMessageModal(this.selectedUsers)},showSendMessageModal(e){if(typeof window.require!="function"){this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}window.require(["core_message/message_send_bulk"],t=>{if(typeof t.showModal!="function"){this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}t.showModal(e,()=>{this.selectedBulkAction=""})},t=>{this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.")})},writeNote(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para escrever anotação.");return}this.showAddNoteModal(this.courseid,this.selectedUsers)},showAddNoteModal(e,t){if(typeof window.require!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}window.require(["core_user/local/participants/bulkactions"],s=>{if(typeof s.showAddNote!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}const i={personal:"Pessoal",course:"Curso",site:"Site"};s.showAddNote(e,t,i,"").then(n=>(n.getRoot().on("hidden.bs.modal",()=>{this.selectedBulkAction=""}),n)).catch(n=>{this.showErrorMessage("Ocorreu um erro ao abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},s=>{this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},downloadData(e){this.selectedUsers.length!==0&&this.prepareLocalDownload(e)},prepareLocalDownload(e){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Nenhum usuário selecionado para download.");return}const t=[];if(Array.isArray(this.selectedUsers)&&this.selectedUsers.every(s=>typeof s=="number"))for(const s of this.selectedUsers){const i=this.enrolments.find(n=>n.id===s);if(i){const n={ID:i.id||"",Nome:i.fullName||i.name||"",Email:i.email||"",CPF:i.cpf||"",Papéis:i.roles||"",Grupos:i.groups||"","Data de Início":i.startDate||"","Data de Término":i.endDate||"",Prazo:i.deadline||"",Progresso:i.progress||"",Situação:i.situationName||i.situation||"",Nota:i.grade||"",Estado:i.statusName||""};t.push(n)}}else for(const s of this.selectedUsers)if(typeof s=="number"){const i=this.enrolments.find(n=>n.id===s);if(i){const n={ID:i.id||"",Nome:i.fullName||i.name||"",Email:i.email||"",CPF:i.cpf||"",Papéis:i.roles||"",Grupos:i.groups||"","Data de Início":i.startDate||"","Data de Término":i.endDate||"",Prazo:i.deadline||"",Progresso:i.progress||"",Situação:i.situationName||i.situation||"",Nota:i.grade||"",Estado:i.statusName||""};t.push(n)}}else if(typeof s=="object"&&s!==null){const i={ID:s.id||"",Nome:s.fullName||s.name||"",Email:s.email||"",CPF:s.cpf||"",Papéis:s.roles||"",Grupos:s.groups||"","Data de Início":s.startDate||"","Data de Término":s.endDate||"",Prazo:s.deadline||"",Progresso:s.progress||"",Situação:s.situationName||s.situation||"",Nota:s.grade||"",Estado:s.statusName||""};t.push(i)}if(t.length===0){this.showErrorMessage("Nenhum dado disponível para download.");return}switch(e){case"csv":this.downloadCSV(t);break;case"xlsx":this.downloadXLSX(t);break;case"html":this.downloadHTML(t);break;case"json":this.downloadJSON(t);break;case"ods":this.downloadODS(t);break;case"pdf":this.downloadPDF(t);break;default:this.showErrorMessage("Formato de download não suportado.");break}},downloadCSV(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=s.map(h=>h.replace(/([A-Z])/g," $1").replace(/^./,_=>_.toUpperCase()).trim()),n=t+[i.join(","),...e.map(h=>s.map(_=>{const p=h[_]||"";return`"${String(p).replace(/"/g,'""')}"`}).join(","))].join(`
`),a=new Blob([n],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),c=document.createElement("a");c.setAttribute("href",u),c.setAttribute("download","usuarios_matriculados.csv"),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(u)},downloadXLSX(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=t+[s.join(","),...e.map(c=>s.map(h=>{const _=c[h]||"";return`"${String(_).replace(/"/g,'""')}"`}).join(","))].join(`
`),n=new Blob([i],{type:"text/csv;charset=utf-8;"}),a=URL.createObjectURL(n),u=document.createElement("a");u.setAttribute("href",a),u.setAttribute("download","usuarios_matriculados.csv"),u.style.visibility="hidden",document.body.appendChild(u),u.click(),document.body.removeChild(u),URL.revokeObjectURL(a),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser aberto no Excel.")},downloadHTML(e){if(e.length===0)return;const t=Object.keys(e[0]),s=[];for(let Q=0;Q<t.length;Q++){const ne=t[Q].replace(/([A-Z])/g," $1").replace(/^./,Y=>Y.toUpperCase()).trim();s.push(ne)}let i="";for(let Q=0;Q<s.length;Q++)i+="<th>"+s[Q]+"</th>";let n="";for(let Q=0;Q<e.length;Q++){let ne="<tr>";for(let Y=0;Y<t.length;Y++)ne+="<td>"+(e[Q][t[Y]]||"")+"</td>";ne+="</tr>",n+=ne}const a='<!DOCTYPE html><html><head><meta charset="utf-8"><title>Usuários Matriculados</title>',u="<style>body{font-family:Arial,sans-serif;margin:20px;color:#333}h1{color:#2c3e50;text-align:center;margin-bottom:20px}table{border-collapse:collapse;width:100%;margin-bottom:20px;box-shadow:0 0 20px rgba(0,0,0,.1)}th,td{border:1px solid #ddd;padding:12px;text-align:left}th{background-color:#3498db;color:white;font-weight:bold;text-transform:uppercase;font-size:14px}tr:nth-child(even){background-color:#f2f2f2}tr:hover{background-color:#e9f7fe}.footer{text-align:center;margin-top:20px;font-size:12px;color:#7f8c8d}</style>",c="</head><body><h1>Usuários Matriculados</h1>",h="<table><thead><tr>",_="</tr></thead><tbody>",p="</tbody></table>",g='<div class="footer">Gerado em '+new Date().toLocaleString()+"</div>",w="</body></html>",C=a+u+c+h+i+_+n+p+g+w,M=new Blob([C],{type:"text/html;charset=utf-8;"}),F=URL.createObjectURL(M),se=document.createElement("a");se.setAttribute("href",F),se.setAttribute("download","usuarios_matriculados.html"),se.style.visibility="hidden",document.body.appendChild(se),se.click(),document.body.removeChild(se),URL.revokeObjectURL(F),this.showSuccessMessage("Download concluído. O arquivo HTML foi salvo com sucesso.")},downloadJSON(e){if(e.length===0)return;const t=JSON.stringify(e,null,2),s=new Blob([t],{type:"application/json;charset=utf-8;"}),i=URL.createObjectURL(s),n=document.createElement("a");n.setAttribute("href",i),n.setAttribute("download","usuarios_matriculados.json"),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(i)},downloadODS(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]);let i=[];i.push(s.join(",")),e.forEach(h=>{const _=s.map(p=>{const g=h[p]||"";return'"'+String(g).replace(/"/g,'""')+'"'});i.push(_.join(","))});const n=t+i.join(`
`),a=new Blob([n],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),c=document.createElement("a");c.setAttribute("href",u),c.setAttribute("download","usuarios_matriculados.csv"),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(u),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser importado no LibreOffice Calc para salvar como ODS.")},downloadPDF(e){e.length!==0&&(this.downloadHTML(e),this.showSuccessMessage("Página HTML aberta. Use a função de impressão do navegador (Ctrl+P) para salvar como PDF."))},editEnrolments(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para editar matrícula.");return}if(this.selectedUsers.length===1){const e=this.selectedUsers[0],t=this.enrolments.find(s=>s.id===e);t?this.editUser(t):this.showErrorMessage("Usuário não encontrado. Por favor, tente novamente.")}else this.showBulkEditEnrollmentModal=!0},async handleBulkEditEnrollmentSuccess(e){this.showSuccessMessage(e.message||"Matrículas editadas com sucesso."),await this.loadRegisteredUsers(),this.selectedUsers=[],this.showBulkEditEnrollmentModal=!1},handleBulkEditEnrollmentError(e){const t="Não foi possível editar as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)},bulkDeleteEnrollment(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para excluir matrícula.");return}this.showBulkDeleteEnrollmentModal=!0},handleBulkDeleteEnrollmentError(e){const t="Não foi possível excluir as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showWarningMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="warning",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},Mx={id:"offer-manager-component",class:"offer-manager"},Px={style:{display:"flex","align-items":"center","margin-bottom":"20px",gap:"10px"}},kx={style:{width:"240px"}},Vx={class:"filters-section mb-3"},Rx={class:"row"},Fx={class:"col-md-3"},Lx={class:"filter-input-container position-relative"},Ux={key:0,class:"dropdown-menu show position-absolute w-100",style:{top:"100%","z-index":"1000","max-height":"200px","overflow-y":"auto"}},Bx=["onClick"],$x={class:"col-md-3"},qx={class:"filter-input-container position-relative"},Hx={key:0,class:"dropdown-menu show position-absolute w-100",style:{top:"100%","z-index":"1000","max-height":"200px","overflow-y":"auto"}},Wx=["onClick"],jx={class:"col-md-3"},zx={class:"filter-input-container position-relative"},Gx={key:0,class:"dropdown-menu show position-absolute w-100",style:{top:"100%","z-index":"1000","max-height":"200px","overflow-y":"auto"}},Kx=["onClick"],Zx={key:0,class:"my-4"},Yx={key:1,class:"alert alert-danger"},Jx={class:"table-container"},Qx={class:"checkbox-container"},Xx=["checked","indeterminate"],e2={class:"checkbox-container"},t2=["checked","onChange"],s2=["href","title"],r2={class:"user-name-link"},n2={class:"progress-container"},o2={class:"progress-text"},i2={class:"status-container"},a2={class:"status-actions"},l2=["onClick"],u2=["onClick"],c2={class:"selected-users-actions"},d2={class:"bulk-actions-container"},f2={key:2,class:"bottom-enroll-button"};function h2(e,t,s,i,n,a){var ve,Ie,ie;const u=z("BackButton"),c=z("PageHeader"),h=z("HierarchicalSelect"),_=z("CustomButton"),p=z("FilterTag"),g=z("FilterTags"),w=z("UserAvatar"),C=z("RoleSelector"),M=z("CustomTable"),F=z("Pagination"),se=z("EnrollmentDetailsModal"),Q=z("EnrolmentModalNew"),ne=z("EditEnrollmentModal"),Y=z("BulkEditEnrollmentModal"),be=z("BulkDeleteEnrollmentModal"),J=z("LFLoading"),he=z("Toast");return x(),D("div",Mx,[A(c,{title:"Usuários matriculados"},{actions:Te(()=>[A(u,{onClick:a.goBack},null,8,["onClick"])]),_:1}),f("div",Px,[f("div",kx,[A(h,{modelValue:n.selectedPageView,"onUpdate:modelValue":t[0]||(t[0]=I=>n.selectedPageView=I),options:n.pageViewOptions,onNavigate:a.handlePageViewChange},null,8,["modelValue","options","onNavigate"])]),!n.classDetails||((ve=n.classDetails)==null?void 0:ve.operational_cycle)!==2?(x(),dt(_,{key:0,variant:"primary",label:"Matricular usuários",onClick:a.addNewUser},null,8,["onClick"])):Z("",!0)]),f("div",Vx,[f("div",Rx,[f("div",Fx,[f("div",Lx,[t[21]||(t[21]=f("label",{for:"name-filter",class:"form-label text-muted small"},"Filtrar por nome",-1)),lt(f("input",{id:"name-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[1]||(t[1]=I=>n.nameSearchInput=I),onInput:t[2]||(t[2]=(...I)=>a.handleNameInput&&a.handleNameInput(...I)),onFocus:t[3]||(t[3]=I=>n.showNameDropdown=n.nameOptions.length>0),onBlur:t[4]||(t[4]=I=>a.clearOptions("name"))},null,544),[[Zt,n.nameSearchInput]]),n.showNameDropdown&&n.nameOptions.length>0?(x(),D("div",Ux,[(x(!0),D(Ne,null,it(n.nameOptions,I=>(x(),D("button",{key:I.id,type:"button",class:"dropdown-item",onClick:Ce=>a.selectNameOption(I)},q(I.label),9,Bx))),128))])):Z("",!0)])]),f("div",$x,[f("div",qx,[t[22]||(t[22]=f("label",{for:"cpf-filter",class:"form-label text-muted small"},"Filtrar por CPF",-1)),lt(f("input",{id:"cpf-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[5]||(t[5]=I=>n.cpfSearchInput=I),onInput:t[6]||(t[6]=(...I)=>a.handleCpfInput&&a.handleCpfInput(...I)),onFocus:t[7]||(t[7]=I=>n.showCpfDropdown=n.cpfOptions.length>0),onBlur:t[8]||(t[8]=I=>a.clearOptions("cpf"))},null,544),[[Zt,n.cpfSearchInput]]),n.showCpfDropdown&&n.cpfOptions.length>0?(x(),D("div",Hx,[(x(!0),D(Ne,null,it(n.cpfOptions,I=>(x(),D("button",{key:I.id,type:"button",class:"dropdown-item",onClick:Ce=>a.selectCpfOption(I)},q(I.label),9,Wx))),128))])):Z("",!0)])]),f("div",jx,[f("div",zx,[t[23]||(t[23]=f("label",{for:"email-filter",class:"form-label text-muted small"},"Filtrar por E-mail",-1)),lt(f("input",{id:"email-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[9]||(t[9]=I=>n.emailSearchInput=I),onInput:t[10]||(t[10]=(...I)=>a.handleEmailInput&&a.handleEmailInput(...I)),onFocus:t[11]||(t[11]=I=>n.showEmailDropdown=n.emailOptions.length>0),onBlur:t[12]||(t[12]=I=>a.clearOptions("email"))},null,544),[[Zt,n.emailSearchInput]]),n.showEmailDropdown&&n.emailOptions.length>0?(x(),D("div",Gx,[(x(!0),D(Ne,null,it(n.emailOptions,I=>(x(),D("button",{key:I.id,type:"button",class:"dropdown-item",onClick:Ce=>a.selectEmailOption(I)},q(I.label),9,Kx))),128))])):Z("",!0)])])])]),A(g,null,{default:Te(()=>[(x(!0),D(Ne,null,it(n.filteredUsers,I=>(x(),dt(p,{key:I.id,onRemove:Ce=>a.removeFilter(I.id||I.value)},{default:Te(()=>[We(q(I.label),1)]),_:2},1032,["onRemove"]))),128))]),_:1}),n.filteredUsers.length>0?(x(),D("div",Zx,[f("button",{type:"button",class:"btn btn-secondary",onClick:t[13]||(t[13]=(...I)=>a.clearFilteredUsers&&a.clearFilteredUsers(...I))}," Limpar ")])):Z("",!0),n.error?(x(),D("div",Yx,[t[24]||(t[24]=f("i",{class:"fas fa-exclamation-circle"},null,-1)),We(" "+q(n.error),1)])):Z("",!0),f("div",Jx,[A(M,{headers:n.tableHeaders,items:n.enrolments,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"header-select":Te(()=>[f("div",Qx,[f("input",{type:"checkbox",checked:a.allSelected,indeterminate:a.someSelected&&!a.allSelected,onChange:t[14]||(t[14]=(...I)=>a.toggleSelectAll&&a.toggleSelectAll(...I)),class:"custom-checkbox"},null,40,Xx)])]),"item-select":Te(({item:I})=>[f("div",e2,[f("input",{type:"checkbox",checked:a.isSelected(I.id),onChange:Ce=>a.toggleSelectUser(I.id),class:"custom-checkbox"},null,40,t2)])]),"item-fullName":Te(({item:I})=>[f("a",{class:"user-name-container",href:`/user/view.php?id=${I.id}`,title:"Ver perfil de "+I.fullName},[A(w,{"full-name":I.fullName,size:36},null,8,["full-name"]),f("span",r2,q(I.fullName),1)],8,s2)]),"item-email":Te(({item:I})=>[We(q(I.email),1)]),"item-cpf":Te(({item:I})=>[We(q(I.cpf),1)]),"item-roles":Te(({item:I})=>[A(C,{userId:I.id,offeruserenrolid:I.offeruserenrolid,currentRole:I.roles,offerclassid:n.offerclassid,onSuccess:a.handleRoleUpdateSuccess,onError:a.handleRoleUpdateError,onReloadTable:a.reloadTable},null,8,["userId","offeruserenrolid","currentRole","offerclassid","onSuccess","onError","onReloadTable"])]),"item-groups":Te(({item:I})=>[We(q(I.groups),1)]),"item-startDate":Te(({item:I})=>[We(q(I.startDate),1)]),"item-endDate":Te(({item:I})=>[We(q(I.endDate),1)]),"item-deadline":Te(({item:I})=>[We(q(I.deadline),1)]),"item-progress":Te(({item:I})=>[f("div",n2,[f("div",{class:"progress-bar",style:is({width:I.progress})},null,4),f("span",o2,q(I.progress),1)])]),"item-situation":Te(({item:I})=>[We(q(I.situationName),1)]),"item-grade":Te(({item:I})=>[We(q(I.grade),1)]),"item-status":Te(({item:I})=>[f("div",i2,[f("span",{class:de(["status-tag badge",I.status===0?"badge-success":"badge-danger"])},q(I.statusName),3),f("div",a2,[f("button",{class:"btn-information",onClick:Ce=>a.showEnrollmentDetails(I),title:"Informações da matrícula"},t[25]||(t[25]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round",class:"custom-icon"},[f("circle",{cx:"12",cy:"12",r:"10"}),f("line",{x1:"12",y1:"16",x2:"12",y2:"12"}),f("line",{x1:"12",y1:"8",x2:"12.01",y2:"8"})],-1)]),8,l2),f("button",{class:"btn-settings",onClick:Ce=>a.editUser(I),title:"Editar matrícula"},t[26]||(t[26]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round",class:"custom-icon"},[f("circle",{cx:"12",cy:"12",r:"3"}),f("path",{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"})],-1)]),8,u2)])])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"])]),A(F,{"current-page":n.currentPage,"onUpdate:currentPage":t[15]||(t[15]=I=>n.currentPage=I),"per-page":n.perPage,"onUpdate:perPage":t[16]||(t[16]=I=>n.perPage=I),total:n.totalEnrolments,loading:n.loading},null,8,["current-page","per-page","total","loading"]),f("div",c2,[f("div",d2,[t[28]||(t[28]=f("label",{for:"bulk-actions"},"Com usuários selecionados...",-1)),lt(f("select",{id:"bulk-actions",class:"form-control bulk-select","onUpdate:modelValue":t[17]||(t[17]=I=>n.selectedBulkAction=I),onChange:t[18]||(t[18]=(...I)=>a.handleBulkAction&&a.handleBulkAction(...I))},t[27]||(t[27]=[ov('<option value="" data-v-d1a3ec34>Escolher...</option><optgroup label="Comunicação" data-v-d1a3ec34><option value="message" data-v-d1a3ec34>Enviar uma mensagem</option><option value="note" data-v-d1a3ec34>Escrever uma nova anotação</option></optgroup><optgroup label="Baixar dados da tabela como:" data-v-d1a3ec34><option value="download_csv" data-v-d1a3ec34> Valores separados por vírgula (.csv) </option><option value="download_xlsx" data-v-d1a3ec34>Microsoft excel (.xlsx)</option><option value="download_html" data-v-d1a3ec34>Tabela HTML</option><option value="download_json" data-v-d1a3ec34> JavaScript Object Notation (.json) </option><option value="download_ods" data-v-d1a3ec34>OpenDocument (.ods)</option><option value="download_pdf" data-v-d1a3ec34> Formato de documento portável (.pdf) </option></optgroup><optgroup label="Inscrições" data-v-d1a3ec34><option value="edit_enrolment" data-v-d1a3ec34> Editar matrículas de usuários selecionados </option><option value="delete_enrolment" data-v-d1a3ec34> Excluir matrículas de usuários selecionados </option></optgroup>',4)]),544),[[Jl,n.selectedBulkAction]])])]),!n.classDetails||((Ie=n.classDetails)==null?void 0:Ie.operational_cycle)!==2?(x(),D("div",f2,[A(_,{variant:"primary",label:"Matricular usuários",onClick:a.addNewUser},null,8,["onClick"])])):Z("",!0),A(se,{show:n.showEnrollmentModal,user:n.selectedUser,"course-name":((ie=n.classDetails)==null?void 0:ie.course_fullname)||"",onClose:a.closeEnrollmentModal},null,8,["show","user","course-name","onClose"]),A(Q,{show:n.showEnrolmentModal,offerclassid:n.offerclassid,roles:n.roleOptions,onClose:a.closeEnrolmentModal,onSuccess:a.handleEnrolmentSuccess},null,8,["show","offerclassid","roles","onClose","onSuccess"]),A(ne,{show:n.showEditEnrollmentModal,user:n.selectedUser,offerclassid:n.offerclassid,onClose:a.closeEditEnrollmentModal,onSuccess:a.handleEditEnrollmentSuccess,onError:a.handleEditEnrollmentError},null,8,["show","user","offerclassid","onClose","onSuccess","onError"]),A(Y,{show:n.showBulkEditEnrollmentModal,users:n.selectedUsers.map(I=>n.enrolments.find(Ce=>Ce.id===I)).filter(Boolean),offerclassid:n.offerclassid,onClose:t[19]||(t[19]=I=>this.showBulkEditEnrollmentModal=!1),onSuccess:a.handleBulkEditEnrollmentSuccess,onError:a.handleBulkEditEnrollmentError},null,8,["show","users","offerclassid","onSuccess","onError"]),A(be,{show:n.showBulkDeleteEnrollmentModal,users:n.selectedUsers.map(I=>n.enrolments.find(Ce=>Ce.id===I)).filter(Boolean),offerclassid:n.offerclassid,onClose:t[20]||(t[20]=I=>n.showBulkDeleteEnrollmentModal=!1),onConfirm:a.confirmeBulkDeleteEnrollment,onError:a.handleBulkDeleteEnrollmentError},null,8,["show","users","offerclassid","onConfirm","onError"]),A(J,{"is-loading":n.loading},null,8,["is-loading"]),A(he,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])])}const p2=Pe(Ax,[["render",h2],["__scopeId","data-v-d1a3ec34"]]),SM="",m2={name:"Alert",props:{type:{type:String,default:"info"},text:{type:String,required:!0},icon:{type:String,required:!1}}};function g2(e,t,s,i,n,a){return x(),D("div",{class:de(["alert",`alert-${s.type}`])},[s.icon?(x(),D("i",{key:0,class:de(s.icon)},null,2)):Z("",!0),We(" "+q(s.text.replace("-","‑")),1)],2)}const _2=Pe(m2,[["render",g2],["__scopeId","data-v-03af0515"]]),_u={data(){return{showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null}},methods:{showToastMessage(e,t="success"){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType=t,this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showSuccessMessage(e){this.showToastMessage(e,"success")},showErrorMessage(e){this.showToastMessage(e,"error")}}},DM="",v2={name:"TextEditor",props:{modelValue:{type:String,default:""},label:{type:String,default:""},placeholder:{type:String,default:"Digite o conteúdo aqui..."},rows:{type:Number,default:5},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"],data(){return{showHtmlSource:!1,htmlContent:this.modelValue}},mounted(){this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.modelValue)},watch:{modelValue:{handler(e){this.showHtmlSource?this.htmlContent=e:this.$refs.editableContent&&this.$refs.editableContent.textContent!==e&&(this.$refs.editableContent.textContent=e)},immediate:!0}},methods:{applyFormat(e,t=null){this.showHtmlSource||(document.execCommand(e,!1,t),this.updateContent())},insertLink(){if(this.showHtmlSource)return;const e=prompt("Digite a URL do link:","http://");e&&this.applyFormat("createLink",e)},insertImage(){if(this.showHtmlSource)return;const e=prompt("Digite a URL da imagem:","http://");e&&this.applyFormat("insertImage",e)},toggleHtmlView(){this.showHtmlSource?this.$nextTick(()=>{this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.htmlContent)}):this.htmlContent=this.$refs.editableContent.textContent,this.showHtmlSource=!this.showHtmlSource},updateContent(){if(!this.showHtmlSource&&this.$refs.editableContent){const e=this.$refs.editableContent.textContent;this.$emit("update:modelValue",e)}},updateHtmlContent(){this.showHtmlSource&&this.$emit("update:modelValue",this.htmlContent)}}},y2={class:"text-editor-container"},b2={class:"editor-toolbar"},C2={class:"toolbar-group"},w2=["disabled"],E2=["disabled"],O2=["disabled"],x2=["disabled"],S2={class:"toolbar-group"},D2=["disabled"],T2=["disabled"],N2=["contenteditable"],I2=["rows","placeholder","disabled"];function A2(e,t,s,i,n,a){return x(),D("div",y2,[s.label?(x(),D("label",{key:0,class:de(["filter-label",{disabled:s.disabled}])},q(s.label),3)):Z("",!0),f("div",{class:de(["editor-container",{disabled:s.disabled}])},[f("div",b2,[f("div",C2,[f("button",{class:"btn-editor",onClick:t[0]||(t[0]=u=>!s.disabled&&a.applyFormat("bold")),title:"Negrito",disabled:s.disabled},t[10]||(t[10]=[f("i",{class:"fas fa-bold"},null,-1)]),8,w2),f("button",{class:"btn-editor",onClick:t[1]||(t[1]=u=>!s.disabled&&a.applyFormat("italic")),title:"Itálico",disabled:s.disabled},t[11]||(t[11]=[f("i",{class:"fas fa-italic"},null,-1)]),8,E2),f("button",{class:"btn-editor",onClick:t[2]||(t[2]=u=>!s.disabled&&a.applyFormat("underline")),title:"Sublinhado",disabled:s.disabled},t[12]||(t[12]=[f("i",{class:"fas fa-underline"},null,-1)]),8,O2),f("button",{class:"btn-editor",onClick:t[3]||(t[3]=u=>!s.disabled&&a.applyFormat("strikethrough")),title:"Tachado",disabled:s.disabled},t[13]||(t[13]=[f("i",{class:"fas fa-strikethrough"},null,-1)]),8,x2)]),t[16]||(t[16]=f("div",{class:"toolbar-divider"},null,-1)),f("div",S2,[f("button",{class:"btn-editor",onClick:t[4]||(t[4]=u=>!s.disabled&&a.applyFormat("insertUnorderedList")),title:"Lista não ordenada",disabled:s.disabled},t[14]||(t[14]=[f("i",{class:"fas fa-list-ul"},null,-1)]),8,D2),f("button",{class:"btn-editor",onClick:t[5]||(t[5]=u=>!s.disabled&&a.applyFormat("insertOrderedList")),title:"Lista ordenada",disabled:s.disabled},t[15]||(t[15]=[f("i",{class:"fas fa-list-ol"},null,-1)]),8,T2)])]),n.showHtmlSource?lt((x(),D("textarea",{key:1,"onUpdate:modelValue":t[8]||(t[8]=u=>n.htmlContent=u),class:"editor-textarea",rows:s.rows,placeholder:s.placeholder,onInput:t[9]||(t[9]=(...u)=>a.updateHtmlContent&&a.updateHtmlContent(...u)),disabled:s.disabled},null,40,I2)),[[Zt,n.htmlContent]]):(x(),D("div",{key:0,class:"editor-content",contenteditable:!s.disabled,onInput:t[6]||(t[6]=(...u)=>a.updateContent&&a.updateContent(...u)),onKeyup:t[7]||(t[7]=(...u)=>a.updateContent&&a.updateContent(...u)),ref:"editableContent"},null,40,N2))],2)])}const vu=Pe(v2,[["render",A2],["__scopeId","data-v-6578517d"]]),TM="",M2={name:"OfferForm",mixins:[_u],components:{Toast:Br,HelpIcon:ea,TextEditor:vu,CustomInput:qn,CustomSelect:Qs,Autocomplete:_n,CustomCheckbox:Ho,ConfirmationModal:na},props:{offer:{type:Object,required:!0},isEditing:{type:Boolean,required:!0},isReadonly:{type:Boolean,default:!1}},emits:["update:offer","validate"],data(){return{localOffer:{...this.offer},showOfferStatusModal:!1,typeOptions:[],typeEnabled:!1,audienceOptions:[],formErrors:{name:{hasError:!1,message:"Nome da oferta é obrigatório"},audiences:{hasError:!1,message:"Selecione pelo menos um público-alvo"}}}},async created(){this.typeEnabled&&await this.getTypes(),await this.getAudiences()},watch:{offer:{handler(e){var t;if(JSON.stringify(e)!==JSON.stringify(this.localOffer)){const s=(t=e==null?void 0:e.audiences)==null?void 0:t.map(i=>({value:i,label:""}));this.localOffer={...e,audiences:s}}},deep:!0,immediate:!0},localOffer:{handler(e){var s;const t=(s=e==null?void 0:e.audiences)==null?void 0:s.map(i=>i.value);e={...e,audiences:t},JSON.stringify(e)!==JSON.stringify(this.offer)&&this.$emit("update:offer",{...e,audiences:t})},deep:!0}},methods:{async getTypes(){try{const e=await getTypeOptions(),{enabled:t,types:s,default:i}=e;this.typeEnabled=!!t,t&&Array.isArray(s)&&(this.typeOptions=s.map(n=>({value:n,label:n.charAt(0).toUpperCase()+n.slice(1)})))}catch(e){this.showErrorMessage(e.message||"Erro ao carregar opções de tipos.")}},async getAudiences(){this.loading=!0;try{const e=await sb("");this.audienceOptions=e.map(t=>({value:t.id,label:t.name.toUpperCase()}))}catch(e){console.log(e),this.showErrorMessage("Erro ao carregar públicos-alvo.")}finally{this.loading=!1}},handleStatusChange(e){if(console.log(e),!e){this.showOfferStatusModal=!0;return}this.localOffer.status=!0,this.validateForm()},confirmInactivateStatus(){this.showOfferStatusModal=!1,this.localOffer.status=!1,this.validateForm()},validateForm(){let e=!0;return Object.keys(this.formErrors).forEach(t=>{this.isValidField(t)||(e=!1)}),this.$emit("validate",e),e},isValidField(e){var t;switch(e){case"name":this.formErrors.name.hasError=!this.localOffer.name;break;case"audiences":this.formErrors.audiences.hasError=!((t=this.localOffer)!=null&&t.audiences)||this.localOffer.audiences.length===0;break}return!this.formErrors[e].hasError}}},P2={class:"form-row mb-3"},k2={class:"form-group"},V2={class:"input-container"},R2={key:0,class:"form-group"},F2={class:"input-container"},L2={key:1,class:"form-group"},U2={class:"input-container pt-4"},B2={class:"form-row mb-3"},$2={class:"form-group"},q2={class:"label-container"},H2={class:"label-with-help"},W2={class:"input-container"},j2={class:"form-group text-editor-container"},z2={class:"limited-width-editor"};function G2(e,t,s,i,n,a){const u=z("CustomInput"),c=z("CustomSelect"),h=z("CustomCheckbox"),_=z("HelpIcon"),p=z("Autocomplete"),g=z("TextEditor"),w=z("ConfirmationModal"),C=z("Toast");return x(),D("div",null,[f("div",P2,[f("div",k2,[t[9]||(t[9]=f("div",{class:"label-container"},[f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Nome da Oferta"),f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})])],-1)),f("div",V2,[A(u,{modelValue:n.localOffer.name,"onUpdate:modelValue":t[0]||(t[0]=M=>n.localOffer.name=M),placeholder:"Oferta 0001",width:280,required:"",disabled:s.isReadonly,"has-error":n.formErrors.name.hasError,"error-message":n.formErrors.name.message,onValidate:t[1]||(t[1]=M=>a.validateForm())},null,8,["modelValue","disabled","has-error","error-message"])])]),n.typeEnabled?(x(),D("div",R2,[t[10]||(t[10]=f("div",{class:"label-container"},[f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Tipo da oferta")])],-1)),f("div",F2,[A(c,{modelValue:n.localOffer.type,"onUpdate:modelValue":t[2]||(t[2]=M=>n.localOffer.type=M),options:n.typeOptions,disabled:s.isReadonly,width:280},null,8,["modelValue","options","disabled"])])])):Z("",!0),s.isEditing?(x(),D("div",L2,[f("div",U2,[A(h,{modelValue:n.localOffer.status,"onUpdate:modelValue":t[3]||(t[3]=M=>n.localOffer.status=M),id:"status",label:"Ativar oferta",confirmBeforeChange:!0,disabled:s.isReadonly,onRequestChange:a.handleStatusChange},null,8,["modelValue","disabled","onRequestChange"])])])):Z("",!0)]),f("div",B2,[f("div",$2,[f("div",q2,[f("div",H2,[t[11]||(t[11]=f("label",{class:"form-label"},"Público-alvo",-1)),t[12]||(t[12]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),A(_,{title:"Ajuda com público-alvo",text:`Para atribuir o público-alvo é necessário que o mesmo seja previamente criado no 'Gerenciador de Público-Alvo'.<br><br>
                      Após a exclusão do ‘atributo’ de um público-alvo, os usuários que já estiverem inscritos em um curso permanecerão matriculados, 
                      mantendo acesso ao conteúdo normalmente. <br><br>
                      A exclusão impactará apenas a exibição do curso para novos usuários dentro desse público-alvo.`})])]),f("div",W2,[A(p,{class:"autocomplete-audiences",modelValue:n.localOffer.audiences,"onUpdate:modelValue":[t[4]||(t[4]=M=>n.localOffer.audiences=M),t[5]||(t[5]=M=>a.validateForm())],items:n.audienceOptions,placeholder:"Pesquisar público-alvo...","input-max-width":218,required:!0,"show-all-option":!0,disabled:s.isReadonly,"has-error":n.formErrors.audiences.hasError,"error-message":n.formErrors.audiences.message},null,8,["modelValue","items","disabled","has-error","error-message"])])])]),f("div",j2,[t[13]||(t[13]=f("div",{class:"label-container"},[f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Descrição da oferta")])],-1)),f("div",z2,[A(g,{modelValue:n.localOffer.description,"onUpdate:modelValue":[t[6]||(t[6]=M=>n.localOffer.description=M),t[7]||(t[7]=M=>a.validateForm())],placeholder:"Digite a descrição da oferta aqui...",rows:5,disabled:s.isReadonly},null,8,["modelValue","disabled"])])]),A(w,{show:n.showOfferStatusModal,size:"md",title:"Ao inativar esta oferta, os cursos e as turmas associadas serão tratados da seguinte forma: ","list-title":"Comportamento para os cursos, turmas e matrículas:","list-items":["Todos os cursos contidos na oferta não serão mais disponibilizados para os usuários da oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."],"confirm-button-text":"Inativar oferta","cancel-button-text":"Cancelar",icon:"warning",onClose:t[8]||(t[8]=M=>n.showOfferStatusModal=!1),onConfirm:a.confirmInactivateStatus},null,8,["show","onConfirm"]),A(C,{show:e.showToast,message:e.toastMessage,type:e.toastType,duration:3e3},null,8,["show","message","type"])])}const rp=Pe(M2,[["render",G2],["__scopeId","data-v-991ff6e5"]]),K2="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCiAgICA8cGF0aCBkPSJNNyAxNHMtMSAwLTEtMSAxLTQgNS00IDUgMyA1IDQtMSAxLTEgMUg3em00LTZhMyAzIDAgMSAwIDAtNiAzIDMgMCAwIDAgMCA2eiIgZmlsbD0iI2ZmZiIvPg0KICAgIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNNS4yMTYgMTRBMi4yMzggMi4yMzggMCAwIDEgNSAxM2MwLTEuMzU1LjY4LTIuNzUgMS45MzYtMy43MkE2LjMyNSA2LjMyNSAwIDAgMCA1IDljLTQgMC01IDMtNSA0czEgMSAxIDFoNC4yMTZ6IiBmaWxsPSIjZmZmIi8+DQogICAgPHBhdGggZD0iTTQuNSA4YTIuNSAyLjUgMCAxIDAgMC01IDIuNSAyLjUgMCAwIDAgMCA1eiIgZmlsbD0iI2ZmZiIvPg0KPC9zdmc+DQo=",Z2="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCiAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTggMmEuNS41IDAgMCAxIC41LjV2NWg1YS41LjUgMCAwIDEgMCAxaC01djVhLjUuNSAwIDAgMS0xIDB2LTVoLTVhLjUuNSAwIDEgMSAwLTFoNXYtNUEuNS41IDAgMCAxIDggMnoiIGZpbGw9IiNmZmYiLz4NCjwvc3ZnPg0K",NM="",Y2={name:"CollapsibleTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1},expandable:{type:Boolean,default:!1}},data(){return{expandedRows:[]}},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})},toggleExpand(e){const t=this.expandedRows.indexOf(e);t===-1?this.expandedRows.push(e):this.expandedRows.splice(t,1)}}},J2={class:"table-responsive"},Q2={class:"table"},X2={key:0,class:"expand-column"},eS=["onClick","data-value"],tS={key:0,class:"sort-icon"},sS={key:0},rS={key:0,class:"expand-column"},nS=["onClick","title"],oS=["colspan"],iS={class:"expanded-content"},aS={key:1},lS=["colspan"];function uS(e,t,s,i,n,a){return x(),D("div",J2,[f("table",Q2,[f("thead",null,[f("tr",null,[s.expandable?(x(),D("th",X2)):Z("",!0),(x(!0),D(Ne,null,it(s.headers,u=>(x(),D("th",{key:u.value,onClick:c=>u.sortable?a.handleSort(u.value):null,class:de({sortable:u.sortable}),"data-value":u.value},[We(q(u.text)+" ",1),u.sortable?(x(),D("span",tS,[f("i",{class:de(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)])):Z("",!0)],10,eS))),128))])]),s.items.length>0?(x(),D("tbody",sS,[(x(!0),D(Ne,null,it(s.items,(u,c)=>(x(),D(Ne,{key:u.id},[f("tr",{class:de({expanded:n.expandedRows.includes(u.id)})},[s.expandable?(x(),D("td",rS,[f("button",{class:"btn-expand",onClick:h=>a.toggleExpand(u.id),title:n.expandedRows.includes(u.id)?"Recolher":"Expandir"},[f("div",{class:de(["icon-container",{"is-expanded":n.expandedRows.includes(u.id)}])},t[0]||(t[0]=[f("svg",{width:"16",height:"16",viewBox:"0 0 24 24",class:"expand-icon"},[f("rect",{x:"5",y:"11",width:"14",height:"2",fill:"var(--primary)"}),f("rect",{x:"11",y:"5",width:"2",height:"14",fill:"var(--primary)",class:"vertical-line"})],-1)]),2)],8,nS)])):Z("",!0),(x(!0),D(Ne,null,it(s.headers,h=>(x(),D("td",{key:`${u.id}-${h.value}`},[Mt(e.$slots,"item-"+h.value,{item:u},()=>[We(q(u[h.value]),1)],!0)]))),128))],2),s.expandable?(x(),D("tr",{key:0,class:de(["expanded-row",{"is-visible":n.expandedRows.includes(u.id)}])},[f("td",{colspan:s.headers.length+1},[f("div",iS,[Mt(e.$slots,"expanded-content",{item:u},void 0,!0)])],8,oS)],2)):Z("",!0)],64))),128))])):(x(),D("tbody",aS,[f("tr",null,[f("td",{colspan:s.headers.length+(s.expandable?1:0)},[Mt(e.$slots,"empty-state",{},()=>[t[1]||(t[1]=f("div",{class:"empty-state"},[f("span",null,"Não existem registros")],-1))],!0)],8,lS)])]))])])}const cS=Pe(Y2,[["render",uS],["__scopeId","data-v-05038124"]]),IM="",AM="",dS={name:"AddOfferCourseModal",components:{CustomInput:qn,CustomButton:mr,CustomTable:mn,Pagination:gn,Autocomplete:_n,FilterTag:Wo},props:{modelValue:{type:Boolean,required:!0},offerId:{type:Number,required:!0}},emits:["update:modelValue","confirm"],data(){return{selectedCategory:null,selectedCourse:null,categoryOptions:[],courseOptions:[],currentPage:1,perPage:5,sortBy:"name",sortDesc:!1,loading:!1,loadingCategories:!1,loadingCourses:!1,loadingCurrentOfferCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingMoreCourses:!1,tableHeaders:[{text:"CURSO",value:"name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],selectedCoursesPreview:[],currentOfferCourses:[]}},computed:{filteredCourses(){const e=[...this.selectedCoursesPreview].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)},totalPages(){return Math.ceil(this.selectedCoursesPreview.length/this.perPage)},courseNoResultsText(){return this.loadingCourses?"Buscando cursos...":this.loadingMoreCourses?"Carregando mais cursos...":this.selectedCategory?this.courseOptions.length===0&&this.selectedCategory?"Todos os cursos já foram adicionados":"Nenhum curso encontrado":"Selecione uma categoria primeiro"}},watch:{modelValue(e,t){e?(this.getCurrentOfferCourses(),this.getCategories()):(this.selectedCategory=null,this.selectedCourse=null,this.categoryOptions=[],this.courseOptions=[],this.selectedCoursesPreview=[])},selectedCategory(e){if(this.courseOptions=[],this.selectedCourse=null,this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,!e){this.getCurrentOfferCourses();return}this.getCoursesForCategory(e.value)},courseOptions(e){e.length<10&&this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&this.$nextTick(()=>{this.loadMoreCourses(),console.log("Carregando mais cursos... via watch")})}},methods:{async getCurrentOfferCourses(){try{this.loadingCurrentOfferCourses=!0;const e=await Xh(this.offerId);this.currentOfferCourses=e.courses.map(t=>({id:t.courseid,name:t.fullname,offerCourseId:t.id}))}catch{}finally{this.loadingCurrentOfferCourses=!1}},async getCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await hu("");this.categoryOptions=e.map(t=>({value:t.id,label:t.name}))}catch{}finally{this.loadingCategories=!1}},async addOfferCourses(){try{if(this.loading=!0,this.selectedCoursesPreview.length===0){this.closeModal();return}const e=this.selectedCoursesPreview.map(t=>t.id);await ib(this.offerId,e),this.$emit("confirm",this.selectedCoursesPreview),this.closeModal()}catch{}finally{this.loading=!1}},async getCoursesForCategory(e,t=1,s=!1,i=""){if(e)try{t===1?(this.loadingCourses=!0,s||(this.courseOptions=[])):this.loadingMoreCourses=!0;const n=await nb(this.offerId,e,i,t,this.coursesPerPage);let a=null,u=[];if(u=n.courses,a={page:n.page||1,total_pages:n.total_pages||1},a){if(this.coursesPage=a.page||1,this.coursesTotalPages=a.total_pages||1,this.hasMoreCourses=(a.page||1)<(a.total_pages||1),u&&u.length>0){const h=u.filter(_=>!this.currentOfferCourses.some(p=>p.id===_.id)&&!this.selectedCoursesPreview.some(p=>p.id===_.id)).map(_=>({value:_.id,label:_.fullname}));s?this.courseOptions=[...this.courseOptions,...h]:this.courseOptions=h}}else console.warn("Formato de resposta inesperado")}catch(n){console.error("Erro ao carregar cursos da categoria:",n),s||(this.courseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.getCoursesForCategory(this.selectedCategory.value,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,await this.getCoursesForCategory(this.selectedCategory.value,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.id===e.value)&&(this.selectedCoursesPreview.push({id:e.value,name:e.label}),this.courseOptions=this.courseOptions.filter(t=>t.value!==e.value),this.currentPage=1),this.selectedCourse=null},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.id===e.id);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.currentPage>1&&this.currentPage>Math.ceil(this.selectedCoursesPreview.length/this.perPage)&&(this.currentPage=Math.max(1,this.currentPage-1)),this.selectedCategory?this.getCoursesForCategory(this.selectedCategory.value,this.currentPage,!1):this.courseOptions.push({value:s.id,label:s.name})}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},handlePageChange(e){this.currentPage=e},handlePerPageChange(e){this.perPage=e,this.currentPage=1},closeModal(){this.$emit("update:modelValue",!1),this.selectedCategory=null,this.selectedCourse=null,this.selectedCoursesPreview=[]}}},fS={class:"modal-header"},hS={class:"modal-body"},pS={class:"search-section"},mS={class:"search-group"},gS={class:"search-group"},_S={class:"table-container"},vS={key:0,class:"empty-preview-message"},yS={class:"action-buttons"},bS=["onClick"],CS={class:"modal-footer"};function wS(e,t,s,i,n,a){const u=z("Autocomplete"),c=z("CustomTable"),h=z("Pagination"),_=z("CustomButton");return s.modelValue?(x(),D("div",{key:0,class:"modal-overlay",onClick:t[6]||(t[6]=(...p)=>a.closeModal&&a.closeModal(...p))},[f("div",{class:"modal-content",onClick:t[5]||(t[5]=Pt(()=>{},["stop"]))},[f("div",fS,[t[8]||(t[8]=f("h2",null,"Adicionar curso",-1)),f("button",{class:"close-button",onClick:t[0]||(t[0]=(...p)=>a.closeModal&&a.closeModal(...p))},t[7]||(t[7]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",hS,[t[11]||(t[11]=f("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),f("div",pS,[f("div",mS,[A(u,{modelValue:n.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=p=>n.selectedCategory=p),items:n.categoryOptions,label:"Categoria",placeholder:"Pesquisar...","input-max-width":250,loading:n.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada"},null,8,["modelValue","items","loading","no-results-text"])]),f("div",gS,[A(u,{modelValue:n.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=p=>n.selectedCourse=p),items:n.courseOptions,label:"Curso",placeholder:"Pesquisar...","input-max-width":250,disabled:!n.selectedCategory,loading:n.loadingCourses||n.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"keep-open-on-select":!0,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])]),f("div",_S,[n.selectedCoursesPreview.length===0?(x(),D("div",vS,t[9]||(t[9]=[f("p",null,"Selecione cursos acima para adicioná-los à oferta",-1)]))):(x(),dt(c,{key:1,headers:n.tableHeaders,items:a.filteredCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-actions":Te(({item:p})=>[f("div",yS,[f("button",{class:"btn-action btn-delete",onClick:g=>a.removeCourse(p),title:"Remover da lista"},t[10]||(t[10]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,bS)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]))]),n.selectedCoursesPreview.length>0?(x(),dt(h,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":[t[3]||(t[3]=p=>n.currentPage=p),a.handlePageChange],"per-page":n.perPage,"onUpdate:perPage":[t[4]||(t[4]=p=>n.perPage=p),a.handlePerPageChange],total:n.selectedCoursesPreview.length},null,8,["current-page","per-page","total","onUpdate:currentPage","onUpdate:perPage"])):Z("",!0)]),f("div",CS,[A(_,{variant:"primary",label:"Confirmar","is-loading":n.loading,disabled:n.selectedCoursesPreview.length===0,onClick:a.addOfferCourses},null,8,["is-loading","disabled","onClick"]),A(_,{variant:"secondary",label:"Cancelar",onClick:a.closeModal},null,8,["onClick"])])])])):Z("",!0)}const ES=Pe(dS,[["render",wS],["__scopeId","data-v-8cfa07af"]]),MM="",OS={name:"DuplicateOfferClassModal",components:{Autocomplete:_n,CustomTable:mn,Pagination:gn,CustomButton:mr},props:{offerClass:{type:Object,default:null},parentCourse:{type:Object,default:null},offerId:{type:[Number,String],default:null}},emits:["close","success","loading","error"],data(){return{selectedCategory:null,categoryOptions:[],loadingCategories:!1,selectedCourse:null,targetCourseOptions:[],selectedCoursesPreview:[],loading:!1,loadingCourses:!1,loadingMoreCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,currentPage:1,perPage:5,sortBy:"label",sortDesc:!1,tableHeaders:[{text:"CURSO",value:"label",sortable:!0},{text:"CATEGORIA",value:"category_name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],duplicatingCourses:!1,duplicatedCount:0,totalToDuplicate:0,existingCourses:[]}},computed:{courseNoResultsText(){return this.selectedCategory?this.loadingCourses||this.loadingMoreCourses?"Carregando cursos...":this.targetCourseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado":"Selecione uma categoria primeiro"},filteredCourses(){const e=(this.currentPage-1)*this.perPage,t=e+this.perPage;return this.getSortedCourses().slice(e,t)}},watch:{offerClass(){this.resetForm(),this.getCategories()},parentCourse(){this.resetForm(),this.getCategories()},selectedCategory(e){this.resetCourseSelection(),e!=null&&e.value&&this.getCoursesForCategory(e.value)}},async created(){this.initializeComponent()},methods:{async initializeComponent(){this.resetForm(),await this.getCategories()},resetForm(){this.resetCategorySelection(),this.resetCourseSelection(),this.resetPagination(),this.resetDuplicationState(),this.existingCourses=[]},resetCategorySelection(){this.selectedCategory=null,this.categoryOptions=[],this.loadingCategories=!1},resetCourseSelection(){this.selectedCourse=null,this.targetCourseOptions=[],this.selectedCoursesPreview=[],this.loadingCourses=!1,this.loadingMoreCourses=!1,this.resetCoursePagination()},resetCoursePagination(){this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1},resetPagination(){this.currentPage=1},resetDuplicationState(){this.duplicatingCourses=!1,this.duplicatedCount=0,this.totalToDuplicate=0},getSortedCourses(){return[...this.selectedCoursesPreview].sort((e,t)=>{const s=e[this.sortBy],i=t[this.sortBy];return s<i?this.sortDesc?1:-1:s>i?this.sortDesc?-1:1:0})},async getCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await hu("",this.offerId);this.categoryOptions=e.map(t=>({value:t.id,label:t.name}))}catch(e){this.handleError("Erro ao carregar categorias:",e)}finally{this.loadingCategories=!1}},async getCoursesForCategory(e,t=1,s=!1,i=""){if(!(!e||!this.offerClass))try{t===1?(this.loadingCourses=!0,s||(this.targetCourseOptions=[])):this.loadingMoreCourses=!0;const u=(await pb(this.offerClass.id)).filter(c=>{const h=!i||c.name&&c.name.toLowerCase().includes(i.toLowerCase())||c.fullname&&c.fullname.toLowerCase().includes(i.toLowerCase()),_=!this.selectedCoursesPreview.some(w=>parseInt(w.value)===parseInt(c.id)),p=parseInt(c.categoryid)===parseInt(e),g=parseInt(c.id)!==parseInt(this.parentCourse.courseId);return p&&g&&h&&_}).map(c=>({value:c.id,label:c.name||c.fullname||c.coursename||`Curso ${c.id}`,categoryid:c.categoryid,category_name:c.category_name})).filter(c=>c!==null);s?this.targetCourseOptions=[...this.targetCourseOptions,...u]:this.targetCourseOptions=u,this.hasMoreCourses=u.length>=this.coursesPerPage,t>this.coursesPage&&(this.coursesPage=t)}catch(n){this.handleError("Erro ao carregar cursos da categoria:",n),s||(this.targetCourseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.getCoursesForCategory(this.selectedCategory.value,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(this.resetCoursePagination(),await this.getCoursesForCategory(this.selectedCategory.value,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.value===e.value)&&(this.selectedCoursesPreview.push({value:e.value,label:e.label,categoryid:e.categoryid,category_name:e.category_name}),this.targetCourseOptions=this.targetCourseOptions.filter(t=>t.value!==e.value),this.selectedCourse=null)},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.value===e.value);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.targetCourseOptions.push(s)}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},handleError(e,t){this.$emit("error","Erro ao carregar dados. Por favor, tente novamente.")},async handleConfirm(){if(!(!this.offerClass||this.selectedCoursesPreview.length===0))try{this.loading=!0,this.duplicatingCourses=!0,this.totalToDuplicate=this.selectedCoursesPreview.length,this.duplicatedCount=0,this.$emit("loading",!0);const e=this.offerClass.name,t=parseInt(this.offerClass.id,10),s=[];for(const i of this.selectedCoursesPreview){const n=parseInt(i.value,10);try{const a=await hb(t,n);s.push({offerClassName:e,targetCourseName:i.label,offerClassId:t,targetCourseId:n,result:a}),this.duplicatedCount++}catch(a){this.$emit("error",`Erro ao duplicar para o curso ${i.label}: ${a.message}`)}}if(s.length===0)throw new Error("Nenhuma turma foi duplicada com sucesso.");this.$emit("success",{offerClassName:e,totalSelected:this.totalToDuplicate,totalDuplicates:s.length,duplicates:s}),this.resetForm(),this.$emit("close")}catch(e){this.$emit("error",e.message||"Erro ao duplicar turmas.")}finally{this.duplicatingCourses=!1,this.loading=!1,this.$emit("loading",!1)}}}},xS={class:"modal-header"},SS={class:"modal-title"},DS={class:"modal-body"},TS={class:"search-section"},NS={class:"search-group"},IS={class:"search-group"},AS={class:"table-container"},MS={key:0,class:"empty-preview-message"},PS={class:"action-buttons"},kS=["onClick"],VS={class:"modal-footer"};function RS(e,t,s,i,n,a){var p;const u=z("Autocomplete"),c=z("CustomTable"),h=z("Pagination"),_=z("CustomButton");return x(),D("div",{class:"modal-backdrop",onClick:t[7]||(t[7]=g=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[6]||(t[6]=Pt(()=>{},["stop"]))},[f("div",xS,[f("h3",SS,'Duplicar Turma "'+q((p=s.offerClass)==null?void 0:p.name)+'"',1),f("button",{class:"close-button",onClick:t[0]||(t[0]=g=>e.$emit("close"))},t[8]||(t[8]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",DS,[t[11]||(t[11]=f("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),f("div",TS,[f("div",NS,[A(u,{modelValue:n.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=g=>n.selectedCategory=g),items:n.categoryOptions,label:"Categoria",placeholder:"Pesquisar...","input-max-width":250,loading:n.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada"},null,8,["modelValue","items","loading","no-results-text"])]),f("div",IS,[A(u,{modelValue:n.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=g=>n.selectedCourse=g),items:n.targetCourseOptions,label:"Curso",placeholder:"Pesquisar...","input-max-width":250,disabled:!n.selectedCategory,loading:n.loadingCourses||n.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])]),f("div",AS,[n.selectedCoursesPreview.length===0?(x(),D("div",MS,t[9]||(t[9]=[f("p",null,"Selecione cursos acima para duplicar a turma",-1)]))):(x(),dt(c,{key:1,headers:n.tableHeaders,items:a.filteredCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-actions":Te(({item:g})=>[f("div",PS,[f("button",{class:"btn-action btn-delete",onClick:w=>a.removeCourse(g),title:"Remover da lista"},t[10]||(t[10]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,kS)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]))]),n.selectedCoursesPreview.length>0?(x(),dt(h,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[3]||(t[3]=g=>n.currentPage=g),"per-page":n.perPage,"onUpdate:perPage":t[4]||(t[4]=g=>n.perPage=g),total:n.selectedCoursesPreview.length},null,8,["current-page","per-page","total"])):Z("",!0)]),f("div",VS,[A(_,{variant:"primary",label:"Duplicar","is-loading":n.loading,disabled:n.selectedCoursesPreview.length===0,onClick:a.handleConfirm},null,8,["is-loading","disabled","onClick"]),A(_,{variant:"secondary",label:"Cancelar",onClick:t[5]||(t[5]=g=>e.$emit("close"))})])])])}const FS=Pe(OS,[["render",RS],["__scopeId","data-v-12115006"]]),PM="",LS={name:"CustomLabel",components:{HelpIcon:ea},props:{text:{type:String,default:""},help:{type:String,default:""},id:{type:String,required:!1},required:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}}},US={class:"label-with-help"},BS=["for"],$S={key:0,class:"icon fa fa-exclamation-circle text-danger fa-fw mr-0",title:"Obrigatório",role:"img","aria-label":"Obrigatório"};function qS(e,t,s,i,n,a){const u=z("HelpIcon");return x(),D("div",US,[f("label",{for:s.id,class:de(["form-label",{disabled:s.disabled}])},[Mt(e.$slots,"default",{},()=>[We(q(s.text),1)],!0)],10,BS),s.required?(x(),D("i",$S)):Z("",!0),s.help?(x(),dt(u,{key:1,title:`Ajuda com ${s.text.toLowerCase()}`,text:s.help},null,8,["title","text"])):Z("",!0)])}const np=Pe(LS,[["render",qS],["__scopeId","data-v-04d80c6b"]]),kM="",HS={name:"AddOfferClassModal",components:{CustomSelect:Qs,CustomLabel:np},props:{title:{type:String,default:"Tipo de Inscrição"},size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Continuar"},cancelButtonText:{type:String,default:"Cancelar"},offerCourseId:{type:[Number,String],required:!0},offerId:{type:[Number,String],required:!1,default:"0"}},emits:["close","confirm"],data(){return{selectedEnrolType:"",enrolmentMethods:[],loading:!1}},created(){this.getEnrolmentMethods()},methods:{async getEnrolmentMethods(){this.loading=!0;const e=await mb(!0);this.enrolmentMethods=e.map(t=>({value:t.enrol,label:t.name})),this.loading=!1},handleConfirm(){this.selectedEnrolType&&this.$emit("confirm",{enrolType:this.selectedEnrolType,offerCourseId:this.offerCourseId,offerId:this.offerId})}}},WS={class:"modal-header"},jS={class:"modal-title"},zS={class:"modal-body"},GS={class:"enrol-type-modal"},KS={class:"form-group mb-3"},ZS={class:"limited-width-input",style:{"max-width":"280px"}},YS={class:"modal-footer"},JS={class:"footer-buttons"},QS=["disabled"];function XS(e,t,s,i,n,a){const u=z("CustomLabel"),c=z("CustomSelect");return x(),D("div",{class:"modal-backdrop",onClick:t[5]||(t[5]=h=>s.closeOnBackdrop?e.$emit("close"):null)},[f("div",{class:de(["modal-container",[`modal-${s.size}`]]),onClick:t[4]||(t[4]=Pt(()=>{},["stop"]))},[f("div",WS,[f("h3",jS,q(s.title),1),f("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[6]||(t[6]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",zS,[f("div",GS,[t[7]||(t[7]=f("p",{class:"modal-description"}," Selecione o tipo de inscrição para esta turma. Esta configuração não poderá ser alterada posteriormente. ",-1)),f("div",KS,[A(u,{required:"",text:"Método de inscrição",help:`Inscrição automática por público-alvo em ofertas: Nesta opção o usuário será inscrito automaticamente nos cursos que forem atribuídos para o público-alvo dele.\r
Inscrição manual em ofertas: Nesta opção o usuário será matriculado manualmente (em lote ou individualmente) por um perfil autorizado, através da página 'Usuários matriculados' contida em cada turma.\r
Autoisncrição em ofertas: Nesta opção o usuário visualizará os cursos que forem atribuídos para o público-alvo dele, porém, ele precisa clicar no curso para fazer sua matrícula em uma turma. Ou seja, ele não será inscrito automaticamente como no Tipo de inscrição 'Inscrição por público-alvo em ofertas'.`}),f("div",ZS,[A(c,{modelValue:n.selectedEnrolType,"onUpdate:modelValue":t[1]||(t[1]=h=>n.selectedEnrolType=h),options:[{value:"",label:"Selecione um método..."},...n.enrolmentMethods],width:280,required:""},null,8,["modelValue","options"])])])])]),f("div",YS,[t[8]||(t[8]=f("div",{class:"form-info"},[f("span",{style:{color:"#f8f9fa","font-size":"15px"}},"Este formulário contém campos obrigatórios marcados com"),f("i",{class:"fas fa-exclamation-circle",style:{color:"#dc3545","font-size":"0.85rem","vertical-align":"middle"}})],-1)),f("div",JS,[f("button",{class:"btn btn-primary",onClick:t[2]||(t[2]=(...h)=>a.handleConfirm&&a.handleConfirm(...h)),disabled:!n.selectedEnrolType},q(s.confirmButtonText),9,QS),f("button",{class:"btn btn-secondary",onClick:t[3]||(t[3]=h=>e.$emit("close"))},q(s.cancelButtonText),1)])])],2)])}const eD=Pe(HS,[["render",XS],["__scopeId","data-v-4eca082d"]]),VM="",tD={name:"TableList",mixins:[_u],components:{OfferForm:rp,CustomTable:mn,CustomSelect:Qs,CustomInput:qn,CustomButton:mr,Pagination:gn,CollapsibleTable:cS,PageHeader:Hn,BackButton:jo,Autocomplete:_n,TextEditor:vu,CustomCheckbox:Ho,FilterRow:ta,FilterGroup:sa,FilterTag:Wo,FilterTags:oa,AddOfferCourseModal:ES,ConfirmationModal:na,Toast:Br,DuplicateOfferClassModal:FS,AddOfferClassModal:eD,LFLoading:ra},props:{offerId:{type:Number,required:!0},isReadonly:{type:Boolean,default:!1}},data(){return{icons:{users:K2,plus:Z2},showAddCourseModalVisible:!1,showCourseStatusModal:!1,showDeleteOfferCourseModal:!1,offerCourseToDelete:null,showDeleteOfferClassModal:!1,offerClassToDelete:null,classParentCourse:null,showOfferClassStatusModal:!1,showDuplicateOfferClassModal:!1,showEnrolTypeModal:!1,selectedOfferClass:null,offerClassToDuplicate:null,classToDuplicateParentOfferCourse:null,selectedOfferCourseForClass:null,categoryOptions:[],courseOptions:[],selectedOfferCourse:null,loading:!1,inputFilters:{course:null,category:null,onlyActive:!1},offerCourses:[],currentPage:1,perPage:5,totalItems:0,sortBy:"id",sortDesc:!1,filterCoursesPage:1,filterCoursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingCourses:!1,loadingMoreCourses:!1,filterCourseNoResultsText:"Nenhum curso encontrado",courseTableHeaders:[{text:"NOME DO CURSO",value:"name",sortable:!0},{text:"CATEGORIA",value:"category",sortable:!0},{text:"NÚMERO DE TURMAS",value:"courseClassCount",sortable:!0},{text:"STATUS DO CURSO",value:"status",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}]}},watch:{async"inputFilters.course"(e,t){this.currentPage=1,e!==null&&(this.inputFilters.category=null),await this.getCourses()},async"inputFilters.category"(e,t){e===""&&(this.inputFilters.category=null),e!==null&&(this.inputFilters.course=null),this.currentPage=1,await this.getCourses(),await this.getCourseOptions()},async"inputFilters.onlyActive"(e,t){this.currentPage=1,this.inputFilters.course=null,await this.getCourses(),await this.getCourseOptions()},currentPage(){this.getCourses()},perPage(){this.currentPage=1,this.getCourses()}},async created(){await this.getCourses(),await this.getCategoryOptions(),await this.getCourseOptions()},methods:{async getCourses(){if(this.offerId)try{this.loading=!0;const e={onlyActive:this.inputFilters.onlyActive,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc};this.inputFilters.course&&(e.courseIds=[this.inputFilters.course.value]),this.inputFilters.category&&(e.categorySearch=this.inputFilters.category.label);const t=await Xh(this.offerId,e),{page:s,total_pages:i,total_items:n,courses:a}=t;this.currentPage=s;const u=[];for(const c of a)try{const _=(await ub(c.id)).map(p=>({...p,enrol:p.enrol||"-",enrolName:p.enrol_name||"-",vacancies:p.max_users?p.max_users:"Ilimitado",totalEnrolled:p.enrolled_users||0,startDate:this.formatDate(p.startdate),endDate:this.formatDate(p.enddate),status:!!parseInt(p.status),statusName:parseInt(p.status)?"Ativa":"Inativo",canActivate:p.can_activate,canDelete:p.can_delete}));u.push({id:c.id,courseId:c.courseid,name:c.fullname,category:c.category_name||"-",courseClassCount:_.length,status:!!parseInt(c.status),statusName:parseInt(c.status)?"Ativo":"Inativo",canDelete:c.can_delete,canActivate:c.can_activate,offerClasses:_})}catch(h){console.log(h)}this.offerCourses=u,this.totalItems=n}catch{this.offerCourses=[],this.totalItems=0}finally{this.loading=!1}},async getCategoryOptions(){if(this.offerId)try{this.loading=!0;const e=await hu("",this.offerId);this.categoryOptions=e.map(t=>({value:t.id,label:t.name}))}catch(e){this.showErrorMessage(e)}finally{this.loading=!1}},async getCourseOptions(e="",t=!0){var s,i;if(this.offerId){this.loading=!0;try{t?(this.filterCoursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[]):this.loadingMoreCourses=!0;const a=(await ob(this.offerId,(s=this.inputFilters.category)==null?void 0:s.value,e,(i=this.inputFilters.course)!=null&&i.value?[this.inputFilters.course.value]:[],this.inputFilters.onlyActive)).map(u=>({value:u.id||u.courseid,label:u.fullname}));t?this.courseOptions=a:this.courseOptions=[...this.courseOptions,...a],this.hasMoreCourses=!1,this.courseOptions.length===0&&(this.filterCourseNoResultsText="Nenhum curso disponível nesta categoria")}catch{this.showErrorMessage("Erro ao carregar cursos da categoria."),t&&(this.courseOptions=[]),this.hasMoreCourses=!1}finally{t?this.loadingCourses=!1:this.loadingMoreCourses=!1,this.loading=!1}}},getOperationalCycleClassName(e){switch(e){case 0:return"badge-secondary";case 1:return"badge-primary";case 2:return"badge-success";default:return""}},formatDate(e){return e?new Date(e*1e3).toLocaleDateString("pt-BR"):"-"},clearFilters(){this.inputFilters={course:null,category:null,onlyActive:!1},this.getCourses(),this.getCourseOptions()},async removeFilter(e){this.inputFilters[e]=null},async loadMoreCourses(){var e;this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&(this.filterCoursesPage+=1,(e=this.inputFilters.category)!=null&&e.value&&await this.getCourseOptions("",!1))},handleOnlyActiveChange(){this.currentPage=1,this.getCourses()},async handleAddCourseConfirm(e){try{this.loading=!0,await this.getCourses(),this.showSuccessMessage("Curso(s) adicionado(s) com sucesso à oferta.")}catch(t){this.showErrorMessage(t.message||"Ocorreu um erro ao adicionar os cursos.")}finally{this.loading=!1}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.getCourses()},handlePageChange(e){this.currentPage=e,this.getCourses()},addOfferClass(e){this.selectedOfferCourseForClass=e,this.showEnrolTypeModal=!0},handleEnrolTypeConfirm(e){this.showEnrolTypeModal=!1,this.$router.push({name:"NewClass",params:{offerCourseId:e.offerCourseId,offerid:this.offerId},query:{enrol_type:e.enrolType}})},navigateToEditOfferClass(e){this.$router.push({name:"offer.class.edit",params:{offerClassId:e.id}})},requestToggleOfferClassStatus(e){this.selectedOfferClass={...e},this.showOfferClassStatusModal=!0},async toggleOfferClassStatus(){if(this.selectedOfferClass)try{this.loading=!0;const e=this.selectedOfferClass.name,t=!this.selectedOfferClass.status;await gb(this.selectedOfferClass.id,t);const s=this.offerCourses.findIndex(i=>i.offerClasses.some(n=>n.id===this.selectedOfferClass.id));if(s!==-1){const i=this.offerCourses[s],n=i.offerClasses.findIndex(a=>a.id===this.selectedOfferClass.id);if(n!==-1){const a=i.offerClasses[n];a.status=t,a.statusName=t?"Ativo":"Inativo"}}await this.getCourses(),this.showSuccessMessage(t?`Turma "${e}" ativada com sucesso.`:`Turma "${e}" inativada com sucesso.`),this.selectedOfferClass=null,this.showOfferClassStatusModal=!1}catch{}finally{this.loading=!1}},removeOfferClass(e,t){const s=e.offerClasses[t];s.canDelete&&(this.offerClassToDelete=s,this.classParentCourse=e,this.showDeleteOfferClassModal=!0)},navigateToEnrollments(e){this.$router.push({name:"Enrollments",params:{classId:parseInt(e.id)}})},async handleDuplicateSuccess(e){await this.getCourses(),e.totalDuplicates&&this.showSuccessMessage(`Turma "${e.offerClassName}" duplicada com sucesso para ${e.totalDuplicates} curso(s).`)},duplicateOfferClass(e,t){this.offerClassToDuplicate=e,this.classToDuplicateParentOfferCourse=t,this.showDuplicateOfferClassModal=!0},async deleteOfferClass(){if(!(!this.offerClassToDelete||!this.classParentCourse)){this.loading=!0;try{const e=this.offerClassToDelete.name;await db(this.offerClassToDelete.id);const t=this.classParentCourse.offerClasses.findIndex(s=>s.id===this.offerClassToDelete.id);t!==-1&&(this.classParentCourse.offerClasses.splice(t,1),this.classParentCourse.courseClassCount=this.classParentCourse.offerClasses.length),this.showSuccessMessage(`Turma ${e} excluída com sucesso.`),this.offerClassToDelete=null,this.classParentCourse=null,this.showDeleteOfferClassModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao excluir turma.")}finally{this.loading=!1}}},requestToggleOfferCourseStatus(e){e.canActivate&&(this.selectedOfferCourse=e,this.showCourseStatusModal=!0)},getStatusButtonTitle(e){return e.status?e.canActivate?"Inativar":"Não é possível inativar este curso":e.canActivate?"Ativar":"Não é possível ativar este curso"},async toggleOfferCourseStatus(){if(this.selectedOfferCourse)try{this.loading=!0;const e=!this.selectedOfferCourse.status,t=this.selectedOfferCourse.name,s=this.selectedOfferCourse.id;await tb(this.offerId,s,e);const i=this.offerCourses.findIndex(n=>n.id===this.selectedOfferCourse.id);if(i!==-1){const n=this.offerCourses[i];n.status=e,n.statusName=e?"Ativo":"Inativo"}this.showCourseStatusModal=!1,this.selectedOfferCourse=null,await this.getCourses(),this.showSuccessMessage(e?`Curso "${t}" ativado com sucesso.`:`Curso "${t}" inativado com sucesso.`)}catch{}finally{this.loading=!1}},requestDeleteOfferCourse(e){e.canDelete&&(this.offerCourseToDelete=e,this.showDeleteOfferCourseModal=!0)},async deleteOfferCourse(){if(this.offerCourseToDelete)try{this.loading=!0;const e=this.offerCourseToDelete.name,t=this.offerCourseToDelete.id;await eb(this.offerId,t),this.offerCourses=this.offerCourses.filter(i=>i.id!==this.offerCourseToDelete.id),this.offerCourseToDelete=null,this.showDeleteOfferCourseModal=!1,await this.getCourses();const s=`Curso "${e}" excluído com sucesso.`;this.showSuccessMessage(s)}catch(e){this.showErrorMessage(e.message||"Erro ao remover curso.")}finally{this.loading=!1}}}},sD={class:"filters-left-group"},rD={key:0,class:"filters-right-group"},nD={class:"empty-state"},oD={class:"no-results"},iD=["title"],aD={key:0},lD={key:1},uD={class:"action-buttons"},cD=["disabled","onClick"],dD=["src"],fD=["onClick","disabled","title"],hD=["onClick","disabled","title"],pD={class:"course-class-container"},mD={class:"course-class-content"},gD={key:0},_D={class:"course-class-col"},vD=["title"],yD={class:"course-class-col"},bD={class:"course-class-col"},CD={class:"course-class-col"},wD={class:"course-class-col"},ED={class:"course-class-col"},OD={class:"course-class-col operational-cycle"},xD={class:"course-class-col"},SD={class:"action-buttons"},DD=["onClick"],TD=["onClick"],ND=["src"],ID=["disabled","onClick"],AD=["disabled","onClick"],MD=["title","disabled","onClick"],PD=["onClick","disabled","title"],kD={key:1,class:"empty-course-class"};function VD(e,t,s,i,n,a){var be,J,he,ve,Ie,ie,I,Ce,le,ze,mt;const u=z("Autocomplete"),c=z("FilterGroup"),h=z("CustomCheckbox"),_=z("FilterTag"),p=z("FilterTags"),g=z("FilterRow"),w=z("CollapsibleTable"),C=z("Pagination"),M=z("AddOfferCourseModal"),F=z("ConfirmationModal"),se=z("DuplicateOfferClassModal"),Q=z("AddOfferClassModal"),ne=z("LFLoading"),Y=z("Toast");return x(),D("div",null,[A(g,{inline:"",class:"courses-filter-row"},{default:Te(()=>[f("div",sD,[A(c,null,{default:Te(()=>[A(u,{modelValue:n.inputFilters.category,"onUpdate:modelValue":t[0]||(t[0]=ue=>n.inputFilters.category=ue),items:n.categoryOptions,placeholder:"Pesquisar...",label:"Categoria","input-max-width":218,"has-search-icon":!0,"auto-open":!1,"show-filter-tags":!1,"show-selected-in-input":!0,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada"},null,8,["modelValue","items","no-results-text"])]),_:1}),A(c,null,{default:Te(()=>[A(u,{modelValue:n.inputFilters.course,"onUpdate:modelValue":t[1]||(t[1]=ue=>n.inputFilters.course=ue),items:n.courseOptions,placeholder:"Pesquisar...",label:"Curso","input-max-width":218,"has-search-icon":!0,"auto-open":!0,loading:n.loadingCourses||n.loadingMoreCourses,"no-results-text":n.filterCourseNoResultsText,onLoadMore:a.loadMoreCourses,onSearch:t[2]||(t[2]=ue=>a.getCourseOptions(ue)),ref:"courseAutocomplete"},null,8,["modelValue","items","loading","no-results-text","onLoadMore"])]),_:1}),A(c,{isCheckbox:!0,class:"checkbox-filter-group"},{default:Te(()=>[A(h,{modelValue:n.inputFilters.onlyActive,"onUpdate:modelValue":t[3]||(t[3]=ue=>n.inputFilters.onlyActive=ue),id:"onlyActive",label:"Não exibir inativos",onChange:a.handleOnlyActiveChange},null,8,["modelValue","onChange"])]),_:1}),n.inputFilters.course?(x(),dt(p,{key:0,class:"mt-3"},{default:Te(()=>[A(_,{onRemove:t[4]||(t[4]=ue=>a.removeFilter("course"))},{default:Te(()=>[We(" Curso: "+q(n.inputFilters.course.label),1)]),_:1})]),_:1})):Z("",!0)]),s.isReadonly?Z("",!0):(x(),D("div",rD,[f("button",{class:"btn btn-primary",onClick:t[5]||(t[5]=ue=>n.showAddCourseModalVisible=!0)}," Adicionar Curso ")]))]),_:1}),A(w,{headers:n.courseTableHeaders,items:n.offerCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort,expandable:!0},{"empty-state":Te(()=>[f("div",nD,[f("span",oD,q(n.loading?"Carregando registros...":"Não existem registros"),1)])]),"item-name":Te(({item:ue})=>[f("span",{title:ue.name},q(ue.name.length>50?ue.name.slice(0,50)+"...":ue.name),9,iD)]),"item-status":Te(({item:ue})=>[ue.status?(x(),D("span",aD,t[15]||(t[15]=[f("svg",{class:"mr-1",width:"22",height:"23",viewBox:"0 0 22 23",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",fill:"white"}),f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",stroke:"var(--success)","stroke-width":"2"}),f("path",{d:"M11.0024 1.40109C13.6546 1.40109 16.1984 2.45444 18.0737 4.3298C19.9491 6.20517 21.0024 8.74893 21.0024 11.4011C21.0024 14.0533 19.9491 16.597 18.0737 18.4724C16.1984 20.3477 13.6546 21.4011 11.0024 21.4011C8.35028 21.4011 5.80652 20.3477 3.93115 18.4724C2.05579 16.597 1.00244 14.0533 1.00244 11.4011C1.00244 8.74893 2.05579 6.20517 3.93115 4.3298C5.80652 2.45444 8.35028 1.40109 11.0024 1.40109ZM15.3589 7.34055C15.2329 7.34314 15.1086 7.37093 14.9937 7.42258C14.8788 7.47425 14.7755 7.54884 14.6899 7.64133L10.3491 13.1726L7.73291 10.5544C7.55519 10.3888 7.31954 10.2992 7.07666 10.3034C6.83383 10.3078 6.60191 10.4061 6.43018 10.5779C6.25849 10.7496 6.16005 10.9815 6.15576 11.2243C6.15152 11.4672 6.24215 11.7019 6.40771 11.8796L9.71533 15.1882C9.80438 15.2771 9.91016 15.3472 10.0269 15.3943C10.1436 15.4413 10.2691 15.4649 10.395 15.4626C10.5206 15.4602 10.6446 15.4327 10.7593 15.3816C10.8742 15.3302 10.9782 15.256 11.064 15.1638L16.0532 8.92648C16.2233 8.74961 16.3183 8.51269 16.3159 8.2673C16.3136 8.02207 16.2147 7.78755 16.0415 7.61398H16.0396C15.9503 7.52501 15.844 7.45488 15.7271 7.40793C15.6101 7.36102 15.4849 7.33798 15.3589 7.34055Z",fill:"var(--success)"})],-1),We(" Ativo ")]))):(x(),D("span",lD,t[16]||(t[16]=[f("svg",{class:"mr-1",width:"22",height:"23",viewBox:"0 0 22 23",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[f("g",{"clip-path":"url(#clip0_572_6021)"},[f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",fill:"white"}),f("path",{d:"M11.0024 1.40109C13.6546 1.40109 16.1984 2.45444 18.0737 4.3298C19.9491 6.20517 21.0024 8.74893 21.0024 11.4011C21.0024 14.0533 19.9491 16.597 18.0737 18.4724C16.1984 20.3477 13.6546 21.4011 11.0024 21.4011C8.35028 21.4011 5.80652 20.3477 3.93115 18.4724C2.05579 16.597 1.00244 14.0533 1.00244 11.4011C1.00244 8.74893 2.05579 6.20517 3.93115 4.3298C5.80652 2.45444 8.35028 1.40109 11.0024 1.40109ZM14.7524 7.02512C14.6703 7.02512 14.5891 7.04157 14.5132 7.07297C14.4373 7.10442 14.3682 7.1506 14.3101 7.20871L11.0024 10.5173L7.69482 7.20871C7.57747 7.09135 7.41841 7.02512 7.25244 7.02512C7.08647 7.02512 6.92742 7.09135 6.81006 7.20871C6.6927 7.32607 6.62646 7.48512 6.62646 7.65109C6.62646 7.81706 6.6927 7.97612 6.81006 8.09348L10.1187 11.4011L6.81006 14.7087C6.75195 14.7668 6.70577 14.8359 6.67432 14.9118C6.64292 14.9877 6.62646 15.069 6.62646 15.1511C6.62646 15.2332 6.64292 15.3145 6.67432 15.3904C6.70577 15.4663 6.75195 15.5354 6.81006 15.5935C6.92742 15.7108 7.08647 15.7771 7.25244 15.7771C7.33456 15.7771 7.41583 15.7606 7.4917 15.7292C7.56762 15.6978 7.63671 15.6516 7.69482 15.5935L11.0024 12.2849L14.3101 15.5935C14.3682 15.6516 14.4373 15.6978 14.5132 15.7292C14.5891 15.7606 14.6703 15.7771 14.7524 15.7771C14.8346 15.7771 14.9158 15.7606 14.9917 15.7292C15.0676 15.6978 15.1367 15.6516 15.1948 15.5935C15.2529 15.5354 15.2991 15.4663 15.3306 15.3904C15.362 15.3145 15.3784 15.2332 15.3784 15.1511C15.3784 15.069 15.362 14.9877 15.3306 14.9118C15.2991 14.8359 15.2529 14.7668 15.1948 14.7087L11.8862 11.4011L15.1948 8.09348C15.2529 8.03537 15.2991 7.96627 15.3306 7.89035C15.362 7.81448 15.3784 7.73321 15.3784 7.65109C15.3784 7.56898 15.362 7.48771 15.3306 7.41183C15.2991 7.33591 15.2529 7.26682 15.1948 7.20871C15.1367 7.1506 15.0676 7.10442 14.9917 7.07297C14.9158 7.04157 14.8346 7.02512 14.7524 7.02512Z",fill:"var(--danger)"})]),f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",stroke:"var(--danger)","stroke-width":"2"}),f("defs",null,[f("clipPath",{id:"clip0_572_6021"},[f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",fill:"white"})])])],-1),We(" Inativo ")])))]),"item-actions":Te(({item:ue})=>[f("div",uD,[f("button",{class:"btn-action btn-add",disabled:s.isReadonly,onClick:ot=>a.addOfferClass(ue),title:"Adicionar turma"},[f("img",{src:n.icons.plus,alt:"Adicionar turma"},null,8,dD)],8,cD),f("button",{class:de(["btn-action",ue.status?"btn-deactivate":"btn-activate"]),onClick:ot=>a.requestToggleOfferCourseStatus(ue),disabled:!ue.status&&!ue.canActivate||!ue.canActivate||s.isReadonly,title:a.getStatusButtonTitle(ue)},[f("i",{class:de(ue.status?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,fD),f("button",{class:"btn-action btn-delete",onClick:ot=>a.requestDeleteOfferCourse(ue),disabled:!ue.canDelete||s.isReadonly,title:ue.canDelete?"Excluir":"Não é possível excluir este curso"},t[17]||(t[17]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,hD)])]),"expanded-content":Te(({item:ue})=>{var ot;return[f("div",pD,[t[23]||(t[23]=f("div",{class:"course-class-header"},[f("div",{class:"course-class-col"},"NOME DA TURMA"),f("div",{class:"course-class-col"},"TIPO DE INSCRIÇÃO"),f("div",{class:"course-class-col"},"Nº DE VAGAS"),f("div",{class:"course-class-col"},"Nº DE INSCRITOS"),f("div",{class:"course-class-col"},"DATA INÍCIO"),f("div",{class:"course-class-col"},"DATA FIM"),f("div",{class:"course-class-col"},"STATUS"),f("div",{class:"course-class-col"},"AÇÕES")],-1)),f("div",mD,[((ot=ue==null?void 0:ue.offerClasses)==null?void 0:ot.length)>0?(x(),D("div",gD,[(x(!0),D(Ne,null,it(ue.offerClasses,(ce,we)=>(x(),D("div",{class:"course-class-row",key:we},[f("div",_D,[f("span",{title:ce.name},q(ce.name.length>20?ce.name.slice(0,20)+"...":ce.name),9,vD)]),f("div",yD,q(ce.enrolName),1),f("div",bD,q(ce.vacancies),1),f("div",CD,q(ce.totalEnrolled),1),f("div",wD,q(ce.startDate),1),f("div",ED,q(ce.endDate),1),f("div",OD,[ce.operational_cycle===0?(x(),D("span",{key:0,class:de(["badge",a.getOperationalCycleClassName(ce.operational_cycle)])},q(ce.operational_cycle_name),3)):Z("",!0)]),f("div",xD,[f("div",SD,[f("button",{class:"btn-action btn-edit",onClick:Et=>e.editOffer(ue),title:"Visualizar"},t[18]||(t[18]=[f("svg",{width:"38",height:"39",viewBox:"0 0 38 39",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[f("path",{d:"M18.1875 25.5897C20.04 25.5897 21.5417 24.0629 21.5417 22.1795C21.5417 20.296 20.04 18.7692 18.1875 18.7692C16.3351 18.7692 14.8334 20.296 14.8334 22.1795C14.8334 24.0629 16.3351 25.5897 18.1875 25.5897Z",stroke:"white","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),f("path",{d:"M11 29.4872L15.7917 24.6154M11 22.6667V11.9487C11 11.4319 11.2019 10.9362 11.5614 10.5708C11.9208 10.2053 12.4083 10 12.9167 10H20.5833L26.3333 15.8462V27.5385C26.3333 28.0553 26.1314 28.551 25.772 28.9164C25.4125 29.2819 24.925 29.4872 24.4167 29.4872H17.7083",stroke:"white","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)]),8,DD),f("button",{class:"btn-action btn-users",onClick:Et=>a.navigateToEnrollments(ce),title:"Usuários Matriculados"},[f("img",{src:n.icons.users,alt:"Usuários Matriculados"},null,8,ND)],8,TD),f("button",{class:"btn-action btn-edit",disabled:s.isReadonly,onClick:Et=>a.navigateToEditOfferClass(ce),title:"Editar"},t[19]||(t[19]=[f("i",{class:"fas fa-pencil-alt"},null,-1)]),8,ID),f("button",{class:"btn-action btn-duplicate",disabled:s.isReadonly,onClick:Et=>a.duplicateOfferClass(ce,ue),title:"Duplicar Turma"},t[20]||(t[20]=[f("i",{class:"fas fa-copy"},null,-1)]),8,AD),f("button",{class:de(["btn-action",ce.status?"btn-deactivate":"btn-activate"]),title:ce.status?"Inativar":"Ativar",disabled:s.isReadonly,onClick:Et=>a.requestToggleOfferClassStatus(ce)},[f("i",{class:de(ce.status?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,MD),f("button",{class:"btn-action btn-delete",onClick:Et=>a.removeOfferClass(ue,we),disabled:!ce.canDelete||s.isReadonly,title:ce.canDelete?"Excluir":"Não é possível excluir esta turma"},t[21]||(t[21]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,PD)])])]))),128))])):(x(),D("div",kD,t[22]||(t[22]=[f("span",null,"Nenhuma turma encontrada para este curso",-1)])))])])]}),_:1},8,["headers","items","sort-by","sort-desc","onSort"]),A(C,{ref:"pagination","current-page":n.currentPage,"onUpdate:currentPage":[t[6]||(t[6]=ue=>n.currentPage=ue),a.handlePageChange],"per-page":n.perPage,"onUpdate:perPage":t[7]||(t[7]=ue=>n.perPage=ue),total:n.totalItems},null,8,["current-page","per-page","total","onUpdate:currentPage"]),s.offerId?(x(),dt(M,{key:0,modelValue:n.showAddCourseModalVisible,"onUpdate:modelValue":t[8]||(t[8]=ue=>n.showAddCourseModalVisible=ue),offerId:s.offerId,onConfirm:a.handleAddCourseConfirm},null,8,["modelValue","offerId","onConfirm"])):Z("",!0),A(F,{size:"md",show:n.showCourseStatusModal,title:(be=n.selectedOfferCourse)!=null&&be.showOfferClassStatusModal?"Ao inativar este curso da oferta, o curso e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:(J=n.selectedOfferCourse)!=null&&J.status?"":"Tem certeza que deseja ativar este curso?","list-items":(he=n.selectedOfferCourse)!=null&&he.status?["O curso não será mais disponibilizados na oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":(ve=n.selectedOfferCourse)!=null&&ve.status?"Inativar curso":"Ativar","cancel-button-text":"Cancelar",icon:(Ie=n.selectedOfferCourse)!=null&&Ie.status?"warning":"question",onClose:t[9]||(t[9]=ue=>n.showCourseStatusModal=!1),onConfirm:a.toggleOfferCourseStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),A(F,{size:"md",show:n.showDeleteOfferCourseModal,title:"A exclusão deste curso da instância de oferta é uma ação irreversível",message:"Ele será desassociado e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir curso","cancel-button-text":"Cancelar",icon:"warning",onClose:t[10]||(t[10]=ue=>n.showDeleteOfferCourseModal=!1),onConfirm:a.deleteOfferCourse},null,8,["show","onConfirm"]),A(F,{size:"md",show:n.showDeleteOfferClassModal,title:"A exclusão desta turma é uma ação irreversível",message:"Todas as configurações realizadas serão excluídas e a turma será removida do curso. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Turma","cancel-button-text":"Cancelar",icon:"warning",onClose:t[11]||(t[11]=ue=>n.showDeleteOfferClassModal=!1),onConfirm:a.deleteOfferClass},null,8,["show","onConfirm"]),A(F,{show:n.showOfferClassStatusModal,size:"md",title:(ie=n.selectedOfferClass)!=null&&ie.status?"Ao inativar esta turma, as matrículas e o curso associados serão tratados da seguinte forma:":"Confirmar Ativação",message:(I=n.selectedOfferClass)!=null&&I.status?"":"Tem certeza que deseja ativar esta turma?","list-items":(Ce=n.selectedOfferClass)!=null&&Ce.status?["Se o curso não possuir outra turma disponível, ele não será mais disponibilizado para novos usuários da oferta. No entanto, matrículas já realizadas permanecerão ativas.","Usuários já matriculados manterão o acesso ao curso normalmente até o encerramento da turma ou da sua matrícula.","Novos alunos não poderão ser matriculados através da oferta."]:[],"confirm-button-text":(le=n.selectedOfferClass)!=null&&le.status?"Inativar Turma":"Ativar","cancel-button-text":"Cancelar",icon:(ze=n.selectedOfferClass)!=null&&ze.status?"warning":"question",onClose:t[12]||(t[12]=ue=>n.showOfferClassStatusModal=!1),onConfirm:a.toggleOfferClassStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),n.showDuplicateOfferClassModal?(x(),dt(se,{key:1,offerClass:n.offerClassToDuplicate,parentCourse:n.classToDuplicateParentOfferCourse,offerId:s.offerId,onClose:t[13]||(t[13]=ue=>n.showDuplicateOfferClassModal=!1),onSuccess:a.handleDuplicateSuccess,onError:e.showErrorMessage},null,8,["offerClass","parentCourse","offerId","onSuccess","onError"])):Z("",!0),n.showEnrolTypeModal?(x(),dt(Q,{key:2,offerCourseId:(mt=n.selectedOfferCourseForClass)==null?void 0:mt.id,offerId:s.offerId,onClose:t[14]||(t[14]=ue=>n.showEnrolTypeModal=!1),onConfirm:a.handleEnrolTypeConfirm},null,8,["offerCourseId","offerId","onConfirm"])):Z("",!0),A(ne,{"is-loading":n.loading},null,8,["is-loading"]),A(Y,{show:e.showToast,message:e.toastMessage,type:e.toastType,duration:3e3},null,8,["show","message","type"])])}const RD=Pe(tD,[["render",VD],["__scopeId","data-v-b7e2abd9"]]),RM="",FD={name:"CreateEdit",mixins:[_u],components:{Toast:Br,Alert:_2,LFLoading:ra,OfferForm:rp,PageHeader:Hn,BackButton:jo,CustomButton:mr,OfferCourseTableList:RD},props:{isReadonly:{type:Boolean,default:!1}},data(){return{loading:!1,offer:{id:null,name:"",type:"",description:"",status:!1,audiences:[]},showWarning:!0,isEditing:!1,isValidForm:!1}},async created(){const e=parseInt(this.$route.params.id);e&&(await this.getOffer(e),this.isEditing=!0)},methods:{async getOffer(e){try{this.loading=!0;const t=await Jy(e);this.offer={...t,status:!!parseInt(t.status)}}catch(t){this.showErrorMessage(t)}finally{this.loading=!1}},async createOffer(){if(this.loading=!0,!this.isValidForm){this.loading=!1;return}try{const e=await Qh(this.offer);if(e.id){const t=e.id;this.showSuccessMessage("Oferta criada com sucesso!"),this.isEditing=!0,this.$router.push({name:"offer.edit",params:{id:t}})}}catch(e){this.showErrorMessage(e)}finally{this.loading=!1}},async updateOffer(){if(this.loading=!0,!this.isValidForm){this.loading=!1;return}try{await Qh(this.offer),this.showSuccessMessage("Oferta atualizada com sucesso!")}catch(e){this.showErrorMessage(e)}finally{this.loading=!1}},goBack(){this.$router.push({name:"offer.index"})}}},LD={class:"section-container mt-3"},UD={key:0,class:"section-title"},BD={key:1,class:"message-container"},$D={class:"d-flex justify-content-between align-items-center"},qD={class:"actions-container"};function HD(e,t,s,i,n,a){const u=z("BackButton"),c=z("PageHeader"),h=z("Alert"),_=z("OfferForm"),p=z("OfferCourseTableList"),g=z("CustomButton"),w=z("LFLoading"),C=z("Toast");return x(),D("div",{id:"create-edit-component",class:de({"edit-offer":n.isEditing&&!s.isReadonly,"view-offer":s.isReadonly,"create-offer":!n.isEditing})},[A(c,{title:n.isEditing?s.isReadonly?"Visualizar oferta":`Editar oferta: ${n.offer.name}`:"Adicionar Oferta"},{actions:Te(()=>[A(u,{onClick:a.goBack},null,8,["onClick"])]),_:1},8,["title"]),n.showWarning?(x(),dt(h,{key:0,type:"primary",icon:"fas fa-exclamation-triangle",text:`Para que uma instância de oferta seja ativada e disponibilize os cursos\r
      para os públicos-alvo configurados, é necessário garantir que pelo menos\r
      um curso, um grupo de público-alvo, e uma turma estejam configurados à\r
      instância de oferta.`})):Z("",!0),f("div",LD,[t[3]||(t[3]=f("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS",-1)),A(_,{offer:n.offer,"onUpdate:offer":t[0]||(t[0]=M=>n.offer=M),isEditing:n.isEditing,isReadonly:s.isReadonly,onValidate:t[1]||(t[1]=M=>n.isValidForm=M)},null,8,["offer","isEditing","isReadonly"])]),f("div",{class:de(["section-container",{"no-title-section":!n.isEditing}])},[n.isEditing?(x(),D("h2",UD,"CURSOS")):Z("",!0),n.isEditing?Z("",!0):(x(),D("div",BD,t[4]||(t[4]=[f("div",{class:"lock-message"},[f("i",{class:"fas fa-lock lock-icon"}),f("span",null,"Salve a oferta primeiro para gerenciar os cursos")],-1)]))),n.isEditing?(x(),dt(p,{key:2,offerId:n.offer.id,isReadonly:s.isReadonly},null,8,["offerId","isReadonly"])):Z("",!0)],2),t[6]||(t[6]=f("hr",null,null,-1)),f("div",$D,[t[5]||(t[5]=f("div",{class:"required-fields-message"},[f("div",{class:"form-info"},[We(" Este formulário contém campos obrigatórios marcados com "),f("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),f("div",qD,[A(g,{variant:"primary",label:"Salvar",onClick:t[2]||(t[2]=M=>n.isEditing?a.updateOffer():a.createOffer()),disabled:!n.isValidForm||s.isReadonly},null,8,["disabled"]),A(g,{variant:"secondary",label:"Cancelar",onClick:a.goBack},null,8,["onClick"])])]),A(w,{"is-loading":n.loading},null,8,["is-loading"]),A(C,{show:e.showToast,message:e.toastMessage,type:e.toastType,duration:3e3},null,8,["show","message","type"])],2)}const yu=Pe(FD,[["render",HD],["__scopeId","data-v-66c77ab6"]]),FM="",WD={name:"Form",components:{CustomLabel:np,CustomInput:qn,CustomSelect:Qs,CustomButton:mr,PageHeader:Hn,BackButton:jo,Autocomplete:_n,TextEditor:vu,CustomCheckbox:Ho,FilterRow:ta,FilterGroup:sa,Toast:Br,HelpIcon:ea,FilterTag:Wo,FilterTags:oa},props:{offerCourse:{type:Object,required:!0},offerClass:{type:Object,required:!0},isEditing:{type:Boolean,required:!0}},emits:["update:offerClass"],data(){return{loading:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,localOfferClass:{},divisionsOptions:[],sectorsOptions:[],groupsOptions:[],dealershipsOptions:[],selectedTeachers:[],teacherSearchTerm:"",debounceTimer:null,teacherList:[],showTeacherDropdown:!1,highlightedIndex:-1,extensionSituations:[],reenrolSituations:[],roleOptions:[],modalityOptions:[],situationOptions:[],formErrors:{enrol:{hasError:!1,message:"Método de inscrição é obrigatório"},classname:{hasError:!1,message:"Nome da turma é obrigatório"},startdate:{hasError:!1,message:"Data de início é obrigatória"},roleid:{hasError:!1,message:"Perfil padrão é obrigatório"},modality:{hasError:!1,message:"Modalidade é obrigatório"},enddate:{hasError:!1,message:"Data fim da turma é obrigatória quando habilitada"},preenrolmentstartdate:{hasError:!1,message:"Data início de pré-inscrição é obrigatória quando habilitada"},preenrolmentenddate:{hasError:!1,message:"Data fim de pré-inscrição é obrigatória quando habilitada"},enrolperiod:{hasError:!1,message:"Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma"},extensionperiod:{hasError:!1,message:"Dias para prorrogação é obrigatório quando habilitada"},extensiondaysavailable:{hasError:!1,message:"Dias antes do término é obrigatório quando habilitada"},extensionmaxrequests:{hasError:!1,message:"Máximo de solicitações é obrigatório quando habilitada"},extensionsituations:{hasError:!1,message:"É necessário selecionar pelo menos uma situação de matrícula para prorrogação"},minusers:{hasError:!1,message:"Mínimo de usuários deve ser maior ou igual a zero"},maxusers:{hasError:!1,message:"Máximo de usuários deve ser maior ou igual a zero"}},validationAlert:{show:!1,message:"Há campos obrigatórios a serem preenchidos. Por favor, verifique os campos destacados."}}},async created(){await this.getInitialData()},mounted(){window.scrollTo(0,0),document.addEventListener("click",this.handleClickOutside)},beforeUnmount(){document.removeEventListener("click",this.handleClickOutside),this.debounceTimer&&clearTimeout(this.debounceTimer)},directives:{tooltip:{mounted(e,t){e.setAttribute("title",t.value)},updated(e,t){e.setAttribute("title",t.value)}}},computed:{extensionSituationList(){let e=[0,1];return this.situationOptions.filter(t=>e.includes(t.value))},reenrolSituationList(){let e=[6,7,8,4,5];return this.situationOptions.filter(t=>e.includes(t.value))},maxEnrolPeriod(){if(this.localOfferClass.startdate&&this.localOfferClass.optional_fields.enddate&&this.localOfferClass.optional_fields.enableenddate){const e=this.calculateDaysDifference(this.localOfferClass.startdate,this.localOfferClass.optional_fields.enddate);return e>=1?e:1}return null},isOneDayClass(){return this.localOfferClass.startdate&&this.localOfferClass.optional_fields.enableenddate&&this.localOfferClass.optional_fields.enddate&&this.localOfferClass.startdate===this.localOfferClass.optional_fields.enddate},shouldDisableEnrolPeriod(){return this.isOneDayClass}},watch:{offerClass:{handler(e){Ur.isEqual(e,this.localOfferClass)||(this.localOfferClass={...e})},deep:!0,immediate:!0},localOfferClass:{handler(e){Ur.isEqual(e,this.offerClass)||this.$emit("update:offerClass",e)},deep:!0},"localOfferClass.optional_fields.enablehirearchyrestriction":function(e,t){Ur.isEqual(e,t)||e||(this.localOfferClass.optional_fields={...this.localOfferClass.optional_fields,hirearchyrestrictiondivisions:[],hirearchyrestrictionsectors:[],hirearchyrestrictiongroups:[],hirearchyrestrictiondealerships:[]})},"localOfferClass.optional_fields.hirearchyrestrictiondivisions":function(e,t){if(Ur.isEqual(e,t))return;if(!e.length){this.localOfferClass.optional_fields.hirearchyrestrictionsectors=[],this.localOfferClass.optional_fields.hirearchyrestrictiongroups=[],this.localOfferClass.optional_fields.hirearchyrestrictiondealerships=[];return}const s=e.map(i=>i.value);this.getSectors(s)},"localOfferClass.optional_fields.hirearchyrestrictionsectors":function(e,t){if(Ur.isEqual(e,t))return;if(!e.length){this.localOfferClass.optional_fields.hirearchyrestrictiongroups=[],this.localOfferClass.optional_fields.hirearchyrestrictiondealerships=[];return}const s=e.map(i=>i.value);this.getGroups(s)},"localOfferClass.optional_fields.hirearchyrestrictiongroups":function(e,t){if(Ur.isEqual(e,t))return;if(!e.length){this.localOfferClass.optional_fields.hirearchyrestrictiondealerships=[];return}const s=e.map(i=>i.value);this.getDealerships(s)},reenrolSituations:{handler(e){this.localOfferClass.optional_fields.reenrolmentsituations=e.map(t=>t.value)},deep:!0},"localOfferClass.optional_fields.enableenrolperiod":function(e){!e&&this.localOfferClass.optional_fields.enableextension&&(this.localOfferClass.optional_fields.enableextension=!1,this.showWarningMessage("Prorrogação de matrícula foi desabilitada automaticamente porque o Prazo de conclusão da turma foi desabilitado.")),this.isValidField("extensionsituations")},"localOfferClass.startdate":function(){this.localOfferClass.optional_fields.enableenddate&&this.localOfferClass.optional_fields.enddate&&this.isValidField("enddate"),this.localOfferClass.optional_fields.enableenrolperiod&&this.localOfferClass.optional_fields.enrolperiod&&this.isValidField("enrolperiod"),this.localOfferClass.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"localOfferClass.optional_fields.enddate":function(){this.localOfferClass.optional_fields.enableenddate&&this.isValidField("enddate"),this.localOfferClass.optional_fields.enableenrolperiod&&this.localOfferClass.optional_fields.enrolperiod&&this.isValidField("enrolperiod"),this.localOfferClass.optional_fields.enablepreenrolment&&this.localOfferClass.optional_fields.enableenddate&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"localOfferClass.optional_fields.enableenddate":function(e){e&&this.localOfferClass.optional_fields.enddate&&this.isValidField("enddate"),!e&&this.localOfferClass.optional_fields.enableenrolperiod&&this.localOfferClass.optional_fields.enrolperiod&&this.isValidField("enrolperiod"),this.localOfferClass.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"localOfferClass.optional_fields.enrolperiod":function(e){this.localOfferClass.optional_fields.enableenrolperiod&&e&&this.isValidField("enrolperiod")},"localOfferClass.optional_fields.preenrolmentstartdate":function(){this.localOfferClass.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()},"localOfferClass.optional_fields.preenrolmentenddate":function(){this.localOfferClass.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()},"localOfferClass.optional_fields.minusers":function(){this.isValidField("minusers"),this.localOfferClass.optional_fields.maxusers!==null&&this.localOfferClass.optional_fields.maxusers!==void 0&&this.isValidField("maxusers")},"localOfferClass.optional_fields.maxusers":function(){this.isValidField("maxusers"),this.localOfferClass.optional_fields.minusers!==null&&this.localOfferClass.optional_fields.minusers!==void 0&&this.isValidField("minusers")}},methods:{async getInitialData(){try{this.loading=!0,await this.setEnrolmentMethod(),await this.getRoles(),await this.getModalities(),await this.getSituations(),await this.getHierarchyRestrictionData()}catch{this.showErrorMessage("Alguns dados não puderam ser carregados.")}finally{this.loading=!1}},async getRoles(){const e=await mu(this.offerCourse.id);if(this.roleOptions=e.map(t=>({value:t.id,label:t.name})).sort((t,s)=>t.label.localeCompare(s.label)),!this.localOfferClass.optional_fields.roleid){const t=this.roleOptions.find(s=>s.value===5);this.localOfferClass.optional_fields.roleid=(t==null?void 0:t.value)??this.roleOptions[0].value}},async getModalities(){this.modalityOptions=[{value:"presencial",label:"Presencial"},{value:"web",label:"WEB"},{value:"virtual",label:"Virtual"},{value:"blended",label:"Blended"}],this.localOfferClass.optional_fields.modality||(this.localOfferClass.optional_fields.modality=this.modalityOptions[0].value)},async getSituations(){const e=await fb();this.situationOptions=e.map(t=>({value:t.id,label:t.name}))},async setEnrolmentMethod(){try{if(this.isEditing)return;if(!this.route.query.enrol_type)throw new Error("Método de inscrição não informado");this.localOfferClass.enrol=enrolTypeFromUrl}catch(e){console.log(e)}},async getHierarchyRestrictionData(){await this.getDivisions(),this.localOfferClass.optional_fields.enablehirearchyrestriction&&(console.log(this.localOfferClass.optional_fields.hirearchyrestrictiondivisions),this.localOfferClass.optional_fields.hirearchyrestrictiondivisions.length&&await this.getSectors(),this.localOfferClass.optional_fields.hirearchyrestrictionsectors.length&&await this.getGroups(),this.localOfferClass.optional_fields.hirearchyrestrictiongroups.length&&await this.getDealerships())},async getDivisions(){try{const e=await _b();this.divisionsOptions=e.map(t=>({value:t.id,label:t.name}))}catch(e){console.log(e)}},async getSectors(e){try{console.log(e),e||(e=this.localOfferClass.optional_fields.hirearchyrestrictiondivisions.map(s=>s.value),console.log(e));const t=await vb(e);this.sectorsOptions=t.map(s=>({value:s.id,label:s.name}))}catch(t){console.log(t)}},async getGroups(e){try{e||(e=this.localOfferClass.optional_fields.hirearchyrestrictionsectors.map(s=>s.value));const t=await yb(e);this.groupsOptions=t.map(s=>({value:s.id,label:s.name}))}catch(t){console.log(t)}},async getDealerships(e){try{e||(e=this.localOfferClass.optional_fields.hirearchyrestrictiongroups.map(s=>s.value));const t=await bb(e);this.dealershipsOptions=t.map(s=>({value:s.id,label:s.name}))}catch(t){console.log(t)}},isValidForm(){let e=!0;return Object.keys(this.formErrors).forEach(t=>{this.isValidField(t)||(e=!1)}),this.$emit("validate",e),e},isValidField(e){switch(e){case"enrol":this.formErrors.enrol.hasError=!1;break;case"classname":this.formErrors.classname.hasError=!this.localOfferClass.classname;break;case"startdate":const t=this.localOfferClass.startdate,s=t&&this.localOfferClass.optional_fields.enableenddate&&this.localOfferClass.optional_fields.enddate&&new Date(this.localOfferClass.startdate)>new Date(this.localOfferClass.optional_fields.enddate);t?s?(this.formErrors.startdate.message="Data de início deve ser igual ou anterior à data fim da turma",this.formErrors.startdate.hasError=!0):(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!1):(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!0);break;case"roleid":this.formErrors.roleid.hasError=!this.localOfferClass.optional_fields.roleid;break;case"enddate":const i=this.localOfferClass.optional_fields.enableenddate,n=this.localOfferClass.optional_fields.enddate,a=i&&n&&this.localOfferClass.startdate&&new Date(this.localOfferClass.optional_fields.enddate)<new Date(this.localOfferClass.startdate);i&&!n?(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!0):a?(this.formErrors.enddate.message="Data fim da turma deve ser igual ou posterior à data de início",this.formErrors.enddate.hasError=!0):(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!1);break;case"preenrolmentstartdate":this.formErrors.preenrolmentstartdate.hasError=this.localOfferClass.optional_fields.enablepreenrolment&&!this.localOfferClass.optional_fields.preenrolmentstartdate,this.validatePreenrolmentDates();break;case"preenrolmentenddate":this.formErrors.preenrolmentenddate.hasError=this.localOfferClass.optional_fields.enablepreenrolment&&!this.localOfferClass.optional_fields.preenrolmentenddate,this.validatePreenrolmentDates();break;case"enrolperiod":const u=this.localOfferClass.optional_fields.enableenrolperiod,c=this.localOfferClass.optional_fields.enrolperiod!==null&&this.localOfferClass.optional_fields.enrolperiod!==void 0&&this.localOfferClass.optional_fields.enrolperiod!=="",h=this.maxEnrolPeriod!==null&&c&&parseInt(this.localOfferClass.optional_fields.enrolperiod)>this.maxEnrolPeriod;u&&!c?(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado",this.formErrors.enrolperiod.hasError=!0):u&&h?(this.formErrors.enrolperiod.message=`Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`,this.formErrors.enrolperiod.hasError=!0):(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma",this.formErrors.enrolperiod.hasError=!1);break;case"extensionperiod":this.formErrors.extensionperiod.hasError=this.localOfferClass.optional_fields.enableextension&&this.localOfferClass.optional_fields.enableenrolperiod&&!this.localOfferClass.optional_fields.extensionperiod;break;case"extensiondaysavailable":this.formErrors.extensiondaysavailable.hasError=this.localOfferClass.optional_fields.enableextension&&this.localOfferClass.optional_fields.enableenrolperiod&&!this.localOfferClass.optional_fields.extensiondaysavailable;break;case"extensionmaxrequests":this.formErrors.extensionmaxrequests.hasError=this.localOfferClass.optional_fields.enableextension&&this.localOfferClass.optional_fields.enableenrolperiod&&!this.localOfferClass.optional_fields.extensionmaxrequests;break;case"extensionsituations":this.formErrors.extensionsituations.hasError=this.localOfferClass.optional_fields.enableextension&&this.localOfferClass.optional_fields.enableenrolperiod&&(!this.extensionSituations||this.extensionSituations.length===0);break;case"minusers":this.validateMinUsers();break;case"maxusers":this.validateMaxUsers();break}return!this.formErrors[e].hasError},mapToOptions(e){return e.map(t=>({value:t,label:""}))},mapToValues(e){return e.map(t=>t.value)},calculateDaysDifference(e,t){if(!e||!t)return 0;const s=new Date(e),i=new Date(t);if(isNaN(s.getTime())||isNaN(i.getTime()))return 0;if(s.getTime()===i.getTime())return 1;const n=Math.abs(i-s);return Math.ceil(n/(1e3*60*60*24))},checkAndDisableEnrolPeriodForOneDayClass(){this.isOneDayClass&&this.localOfferClass.optional_fields.enableenrolperiod&&(this.localOfferClass.optional_fields.enableenrolperiod=!1,this.localOfferClass.optional_fields.enrolperiod=null,this.localOfferClass.optional_fields.enableextension&&(this.localOfferClass.optional_fields.enableextension=!1,this.localOfferClass.optional_fields.extensionperiod=null,this.localOfferClass.optional_fields.extensiondaysavailable=null,this.localOfferClass.optional_fields.extensionmaxrequests=null,this.extensionSituations=[]),this.showWarningMessage("Prazo de conclusão foi desabilitado automaticamente porque a turma tem duração de apenas um dia (data início = data fim)."))},validatePreenrolmentDates(){let e=!0;if(this.formErrors.preenrolmentstartdate.hasError=!1,this.formErrors.preenrolmentenddate.hasError=!1,this.localOfferClass.optional_fields.enablepreenrolment){const t=this.localOfferClass.startdate,s=this.localOfferClass.optional_fields.enableenddate?this.localOfferClass.optional_fields.enddate:null,i=this.localOfferClass.optional_fields.preenrolmentstartdate,n=this.localOfferClass.optional_fields.preenrolmentenddate,a=this.offerCourse.startdate,u=this.offerCourse.enddate;i||(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início de pré-inscrição é obrigatória",e=!1),n||(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim de pré-inscrição é obrigatória",e=!1),new Date(n)<new Date(i)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser posterior à data início",e=!1),new Date(i)>new Date(t)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou anterior à data início da turma",e=!1),new Date(i)<new Date(a)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou posterior à data início do curso",e=!1),u&&new Date(i)>new Date(u)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data de início deve ser igual ou anterior à data fim do curso",e=!1),s&&new Date(n)>=new Date(s)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser anterior à data fim da turma",e=!1),u&&new Date(n)>new Date(u)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser igual ou anterior à data fim do curso",e=!1)}return e},validateMinUsers(){const e=this.localOfferClass.optional_fields.minusers,t=this.localOfferClass.optional_fields.maxusers,s=parseInt(e),i=parseInt(t);return this.formErrors.minusers.hasError=!1,s===0?!0:i>0&&s>i?(this.formErrors.minusers.message="Mínimo de usuários inscritos deve ser menor que o máximo de usuários inscritos",this.formErrors.minusers.hasError=!0,!1):!0},validateMaxUsers(){const e=this.localOfferClass.optional_fields.minusers,t=this.localOfferClass.optional_fields.maxusers,s=parseInt(t),i=parseInt(e);return this.formErrors.maxusers.hasError=!1,s===0?!0:i>0&&s<i?(this.formErrors.maxusers.message="Máximo de usuários inscritos deve ser maior que o mínimo de usuários inscritos",this.formErrors.maxusers.hasError=!0,!1):!0},handleTeacherInput(){const e=this.teacherSearchTerm.trim();this.debounceTimer&&clearTimeout(this.debounceTimer),e.length>=3?(this.showTeacherDropdown=!0,this.highlightedIndex=-1,this.debounceTimer=setTimeout(async()=>{await this.fetchPotentialTeachers(e)},500)):(this.showTeacherDropdown=!1,this.teacherList=[])},async fetchPotentialTeachers(e){let t=this.selectedTeachers.map(s=>s.id||s.value)??[];this.teacherList=await ep(this.offerCourse.id,this.classId,e,t)},removeTeacher(e){this.selectedTeachers=this.selectedTeachers.filter(t=>t.id!==e)},handleTeacherInputFocus(){this.teacherSearchTerm.length>=3&&this.teacherList.length>0&&(this.showTeacherDropdown=!0)},selectTeacher(e){this.selectedTeachers.push({id:e.id,value:e.id,fullname:e.fullname,email:e.email}),this.teacherSearchTerm="",this.showTeacherDropdown=!1,this.highlightedIndex=-1,this.teacherList=[],this.$nextTick(()=>{this.$refs.teacherSearchInput&&this.$refs.teacherSearchInput.focus()})},handleKeydown(e){if(!(!this.showTeacherDropdown||this.teacherList.length===0))switch(e.key){case"ArrowDown":e.preventDefault(),this.highlightedIndex=Math.min(this.highlightedIndex+1,this.teacherList.length-1);break;case"ArrowUp":e.preventDefault(),this.highlightedIndex=Math.max(this.highlightedIndex-1,0);break;case"Enter":e.preventDefault(),this.highlightedIndex>=0&&this.highlightedIndex<this.teacherList.length&&this.selectTeacher(this.teacherList[this.highlightedIndex]);break;case"Escape":e.preventDefault(),this.showTeacherDropdown=!1,this.highlightedIndex=-1;break}},handleClickOutside(e){this.$refs.teacherSearchContainer&&!this.$refs.teacherSearchContainer.contains(e.target)&&(this.showTeacherDropdown=!1,this.highlightedIndex=-1)},updateUIAfterLoading(){this.$nextTick(()=>{this.updateFormFields(),this.$forceUpdate(),(!this.localOfferClass.classname||!this.localOfferClass.enrol)&&this.showErrorMessage("Dados incompletos após carregamento.")})},updateFormFields(){this.updateSelectField("enrolSelect",this.localOfferClass.enrol),this.updateInputField("classnameInput",this.localOfferClass.classname),this.updateInputField("startdateInput",this.localOfferClass.startdate)},updateSelectField(e,t){if(this.$refs[e]){this.$refs[e].value=t;const s=new Event("change");this.$refs[e].$el.dispatchEvent(s)}},updateInputField(e,t){if(this.$refs[e]&&t){this.$refs[e].value=t;const s=new Event("input");this.$refs[e].$el.dispatchEvent(s)}},goBack(){this.offerCourse.offerid?this.router.push({name:"offer.edit",params:{id:this.offerCourse.offerid}}):this.router.push({name:"offer.index"})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showWarningMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="warning",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},updateFormFields(){if(this.$refs.enrolSelect&&this.localOfferClass.enrol)try{this.$refs.enrolSelect.value=this.localOfferClass.enrol;const e=new Event("change");this.$refs.enrolSelect.$el.dispatchEvent(e),this.$refs.enrolSelect.$emit("input",this.localOfferClass.enrol),this.$refs.enrolSelect.$forceUpdate()}catch{}if(this.$refs.classnameInput&&this.localOfferClass.classname)try{this.$refs.classnameInput.value=this.localOfferClass.classname;const e=new Event("input");this.$refs.classnameInput.$el.dispatchEvent(e),this.$refs.classnameInput.$emit("input",this.localOfferClass.classname),this.$refs.classnameInput.$forceUpdate()}catch{}if(this.$refs.startdateInput&&this.localOfferClass.startdate)try{this.$refs.startdateInput.value=this.localOfferClass.startdate;const e=new Event("input");this.$refs.startdateInput.$el.dispatchEvent(e),this.$refs.startdateInput.$emit("input",this.localOfferClass.startdate),this.$refs.startdateInput.$forceUpdate()}catch{}this.$forceUpdate()},handleSelectAllReenrolSituations(){const e=this.reenrolSituationList.every(t=>this.reenrolSituations.some(s=>s.value===t.value));this.reenrolSituations=e?[]:[...this.reenrolSituationList],this.localOfferClass.optional_fields.reenrolmentsituations=this.reenrolSituations.map(t=>t.value)},restartComponent(){window.scrollTo(0,0),this.updateFormFields(),this.$forceUpdate(),setTimeout(()=>{this.updateFormFields(),this.$forceUpdate(),window.scrollTo(0,0)},500)}}},jD={class:"row mb-3"},zD={class:"col-4"},GD={class:"form-group"},KD={class:"row mb-3"},ZD={class:"col-3"},YD={class:"form-group"},JD={class:"col-3"},QD={class:"col-3 ml-n3"},XD={class:"form-group"},eT={key:0,class:"form-row mb-3"},tT={key:1,class:"form-group"},sT={class:"form-row mb-3"},rT={class:"col-12"},nT={class:"form-group"},oT={class:"limited-width-editor"},iT={class:"row mb-3"},aT={key:0,class:"col-3"},lT={class:"form-group"},uT={class:"limited-width-input"},cT={key:1,class:"col-3"},dT={class:"form-group"},fT={class:"limited-width-input"},hT={class:"col-3"},pT={class:"form-group"},mT={class:"col-3"},gT={class:"form-group"},_T={class:"form-row mb-n3"},vT={class:"row mb-3"},yT={class:"col-md-4 col-lg-3"},bT={class:"form-group"},CT={class:"col-md-4 col-lg-3"},wT={class:"form-group"},ET={class:"col-md-4 col-lg-3"},OT={class:"form-group"},xT={class:"col-md-4 col-lg-3"},ST={class:"form-group"},DT={class:"form-row mb-3"},TT={class:"col-4"},NT={class:"form-group"},IT={class:"form-row mb-3"},AT={class:"col-3"},MT={class:"col-3 ml-n3"},PT={class:"form-group"},kT={class:"form-row mb-n3"},VT={class:"form-row mb-3"},RT={class:"form-row mb-3"},FT={class:"limited-width-input"},LT={class:"form-row mb-3"},UT={class:"limited-width-input"},BT={key:1},$T={class:"form-row mb-3"},qT={class:"form-group"},HT={class:"limited-width-select"},WT={class:"form-group mb-3"},jT={class:"limited-width-select"},zT={class:"position-relative",ref:"teacherSearchContainer"},GT={class:"input-wrapper with-icon"},KT={key:0,class:"dropdown-menu show position-absolute w-100 border rounded-bottom shadow-sm",style:{top:"100%",left:"0",right:"0","max-height":"200px","overflow-y":"auto","z-index":"1000","border-top":"none",cursor:"pointer","border-radius":"0 0 0.375rem 0.375rem"},ref:"teacherDropdown"},ZT=["onClick","onMouseenter"],YT={key:0,class:"text-muted small"},JT={key:1,class:"dropdown-menu show position-absolute w-100 border rounded-bottom shadow-sm",style:{top:"100%",left:"0",right:"0","z-index":"1000","border-top":"none","border-radius":"0 0 0.375rem 0.375rem"}},QT={class:"my-4"},XT=["onClick"];function e3(e,t,s,i,n,a){const u=z("CustomLabel"),c=z("CustomInput"),h=z("CustomCheckbox"),_=z("TextEditor"),p=z("CustomSelect"),g=z("Autocomplete"),w=m1("tooltip");return x(),D("div",null,[f("div",jD,[f("div",zD,[f("div",GD,[A(u,{required:"",text:"Nome da turma",help:"Insira um nome para a turma. Exemplo: Turma ADM 2025."}),A(c,{modelValue:n.localOfferClass.classname,"onUpdate:modelValue":t[0]||(t[0]=C=>n.localOfferClass.classname=C),placeholder:"Digite o nome da turma",required:"",ref:"classnameInput","has-error":n.formErrors.classname.hasError,"error-message":n.formErrors.classname.message,onValidate:t[1]||(t[1]=C=>a.isValidField("classname"))},null,8,["modelValue","has-error","error-message"])])])]),f("div",KD,[f("div",ZD,[f("div",YD,[A(u,{required:"",text:"Data de início",help:"Insira uma data de início para a turma. Exemplo: 07/04/2025."}),A(c,{modelValue:n.localOfferClass.startdate,"onUpdate:modelValue":t[2]||(t[2]=C=>n.localOfferClass.startdate=C),type:"date",required:"",class:"date-input",ref:"startdateInput","has-error":n.formErrors.startdate.hasError,"error-message":n.formErrors.startdate.message,onValidate:t[3]||(t[3]=C=>a.isValidField("startdate"))},null,8,["modelValue","has-error","error-message"])])]),f("div",JD,[f("div",{class:de(["form-group",{disabled:!n.localOfferClass.optional_fields.enableenddate}])},[A(u,{text:"Data de término",help:"Insira uma data de término para a turma, caso haja. Exemplo: 07/12/2025."}),A(c,{modelValue:n.localOfferClass.optional_fields.enddate,"onUpdate:modelValue":t[4]||(t[4]=C=>n.localOfferClass.optional_fields.enddate=C),type:"date",disabled:!n.localOfferClass.optional_fields.enableenddate,required:"",class:"date-input","has-error":n.formErrors.enddate.hasError,"error-message":n.formErrors.enddate.message,onValidate:t[5]||(t[5]=C=>a.isValidField("enddate"))},null,8,["modelValue","disabled","has-error","error-message"])],2)]),f("div",QD,[f("div",XD,[t[44]||(t[44]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"}," ")],-1)),A(h,{modelValue:n.localOfferClass.optional_fields.enableenddate,"onUpdate:modelValue":t[6]||(t[6]=C=>n.localOfferClass.optional_fields.enableenddate=C),id:"enableEndDate",label:"Habilitar data de termínio",class:"inline-checkbox",disabled:!1},null,8,["modelValue"])])])]),n.localOfferClass.enrol=="offer_self"?(x(),D("div",eT,[f("div",{class:de(["form-group",{disabled:!n.localOfferClass.optional_fields.enablepreenrolment}])},[A(u,{text:"Data de início da inscrição",help:"Data de início do período de inscrição."}),A(c,{modelValue:n.localOfferClass.optional_fields.preenrolmentstartdate,"onUpdate:modelValue":t[7]||(t[7]=C=>n.localOfferClass.optional_fields.preenrolmentstartdate=C),type:"date",width:180,disabled:!n.localOfferClass.optional_fields.enablepreenrolment,class:"date-input","has-error":n.formErrors.preenrolmentstartdate.hasError,"error-message":n.formErrors.preenrolmentstartdate.message,onValidate:t[8]||(t[8]=C=>a.isValidField("preenrolmentstartdate"))},null,8,["modelValue","disabled","has-error","error-message"])],2),n.localOfferClass.enrol=="offer_self"?(x(),D("div",{key:0,class:de(["form-group",{disabled:!n.localOfferClass.optional_fields.enablepreenrolment}])},[A(u,{required:"",text:"Data de término da inscrição"}),A(c,{modelValue:n.localOfferClass.optional_fields.preenrolmentenddate,"onUpdate:modelValue":t[9]||(t[9]=C=>n.localOfferClass.optional_fields.preenrolmentenddate=C),type:"date",width:180,disabled:!n.localOfferClass.optional_fields.enablepreenrolment,class:"date-input","has-error":n.formErrors.preenrolmentenddate.hasError,"error-message":n.formErrors.preenrolmentenddate.message,onValidate:t[10]||(t[10]=C=>a.isValidField("preenrolmentenddate"))},null,8,["modelValue","disabled","has-error","error-message"])],2)):Z("",!0),n.localOfferClass.enrol=="offer_self"?(x(),D("div",tT,[t[45]||(t[45]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"}," ")],-1)),A(h,{modelValue:n.localOfferClass.optional_fields.enablepreenrolment,"onUpdate:modelValue":t[11]||(t[11]=C=>n.localOfferClass.optional_fields.enablepreenrolment=C),id:"enablePreEnrolment",label:"Habilitar a inscrição",disabled:!1},null,8,["modelValue"])])):Z("",!0)])):Z("",!0),f("div",sT,[f("div",rT,[f("div",nT,[A(u,{text:"Descrição da turma",help:"Esta descrição estará disponível para os usuários na página intermediária do curso. Exemplo: Esta turma se destina a usuários com os cargos administrativos que foram selecionados para a realização dos cursos obrigatórios do ano de 2025"}),f("div",oT,[A(_,{modelValue:n.localOfferClass.optional_fields.description,"onUpdate:modelValue":t[12]||(t[12]=C=>n.localOfferClass.optional_fields.description=C),placeholder:"Digite a descrição da turma aqui...",rows:5,disabled:!1},null,8,["modelValue"])])])])]),f("div",iT,[n.localOfferClass.enrol=="offer_self"?(x(),D("div",aT,[f("div",lT,[A(u,{text:"Número mínimo de inscrições",help:"Insira um número mínimo de inscrições (vagas) para a turma, se houver. Exemplo: 20."}),f("div",uT,[A(c,{modelValue:n.localOfferClass.optional_fields.minusers,"onUpdate:modelValue":t[13]||(t[13]=C=>n.localOfferClass.optional_fields.minusers=C),type:"number",width:180,"has-error":n.formErrors.minusers.hasError,"error-message":n.formErrors.minusers.message,onValidate:t[14]||(t[14]=C=>a.isValidField("minusers")),min:0},null,8,["modelValue","has-error","error-message"])])])])):Z("",!0),n.localOfferClass.enrol=="offer_self"?(x(),D("div",cT,[f("div",dT,[A(u,{text:"Número máximo de inscrições",help:"Insira um número máximo de inscrições (vagas) para a turma. Exemplo: 100."}),f("div",fT,[A(c,{modelValue:n.localOfferClass.optional_fields.maxusers,"onUpdate:modelValue":t[15]||(t[15]=C=>n.localOfferClass.optional_fields.maxusers=C),type:"number",width:180,"has-error":n.formErrors.maxusers.hasError,"error-message":n.formErrors.maxusers.message,onValidate:t[16]||(t[16]=C=>a.isValidField("maxusers")),min:0},null,8,["modelValue","has-error","error-message"])])])])):Z("",!0),f("div",hT,[f("div",pT,[A(u,{required:"",text:"Perfil atribuído por padrão",help:`Caso a turma seja criada para perfis de 'Aluno Nissan' e ‘Aluno Concessionária’, o perfil padrão selecionado será ‘Estudante’.<br><br>
                    Caso a turma seja criada para perfis de ‘Gestores’, o perfil padrão selecionado será ‘Gestor’ ou outro perfil pertinente.`}),A(p,{modelValue:n.localOfferClass.optional_fields.roleid,"onUpdate:modelValue":t[17]||(t[17]=C=>n.localOfferClass.optional_fields.roleid=C),options:n.roleOptions,required:"","has-error":n.formErrors.roleid.hasError,"error-message":n.formErrors.roleid.message,onValidate:t[18]||(t[18]=C=>a.isValidField("roleid"))},null,8,["modelValue","options","has-error","error-message"])])]),f("div",mT,[f("div",gT,[A(u,{required:"",text:"Modalidade da turma",help:"Selecione a modalidade da turma."}),A(p,{modelValue:n.localOfferClass.optional_fields.modality,"onUpdate:modelValue":t[19]||(t[19]=C=>n.localOfferClass.optional_fields.modality=C),options:n.modalityOptions,required:"","has-error":n.formErrors.modality.hasError,"error-message":n.formErrors.modality.message,onValidate:t[20]||(t[20]=C=>a.isValidField("roleid"))},null,8,["modelValue","options","has-error","error-message"])])])]),f("div",_T,[f("div",{class:de(["form-group",{disabled:!n.localOfferClass.optional_fields.enablehirearchyrestriction,"dependent-field":!0}])},[A(h,{modelValue:n.localOfferClass.optional_fields.enablehirearchyrestriction,"onUpdate:modelValue":t[21]||(t[21]=C=>n.localOfferClass.optional_fields.enablehirearchyrestriction=C),id:"enablehirearchyrestriction",label:"Habilitar restrição por estruturas",help:"Ao habilitar esta opção, os campos 'Divisão', 'Setor', 'Grupo' e 'Concessionária' serão automaticamente ajustados conforme as seleções realizadas nos respectivos filtros."},null,8,["modelValue"])],2)]),f("div",vT,[f("div",yT,[f("div",bT,[A(u,{text:"Divisão"}),A(g,{class:"autocomplete-audiences",modelValue:n.localOfferClass.optional_fields.hirearchyrestrictiondivisions,"onUpdate:modelValue":t[22]||(t[22]=C=>n.localOfferClass.optional_fields.hirearchyrestrictiondivisions=C),items:n.divisionsOptions,placeholder:"Selecionar",required:!0,autoOpen:!1,"show-all-option":!0,disabled:!n.localOfferClass.optional_fields.enablehirearchyrestriction},null,8,["modelValue","items","disabled"])])]),f("div",CT,[f("div",wT,[A(u,{text:"Setor"}),A(g,{class:"autocomplete-audiences",modelValue:n.localOfferClass.optional_fields.hirearchyrestrictionsectors,"onUpdate:modelValue":t[23]||(t[23]=C=>n.localOfferClass.optional_fields.hirearchyrestrictionsectors=C),items:n.sectorsOptions,placeholder:"Selecionar",required:!0,autoOpen:!1,"show-all-option":!0,disabled:!n.localOfferClass.optional_fields.enablehirearchyrestriction||!n.localOfferClass.optional_fields.hirearchyrestrictiondivisions.length},null,8,["modelValue","items","disabled"])])]),f("div",ET,[f("div",OT,[A(u,{text:"Grupo"}),A(g,{class:"autocomplete-audiences",modelValue:n.localOfferClass.optional_fields.hirearchyrestrictiongroups,"onUpdate:modelValue":t[24]||(t[24]=C=>n.localOfferClass.optional_fields.hirearchyrestrictiongroups=C),items:n.groupsOptions,placeholder:"Selecionar",required:!0,autoOpen:!1,"show-all-option":!0,disabled:!n.localOfferClass.optional_fields.enablehirearchyrestriction||!n.localOfferClass.optional_fields.hirearchyrestrictionsectors.length},null,8,["modelValue","items","disabled"])])]),f("div",xT,[f("div",ST,[A(u,{text:"Concessionária"}),A(g,{class:"autocomplete-audiences",modelValue:n.localOfferClass.optional_fields.hirearchyrestrictiondealerships,"onUpdate:modelValue":t[25]||(t[25]=C=>n.localOfferClass.optional_fields.hirearchyrestrictiondealerships=C),items:n.dealershipsOptions,placeholder:"Selecionar",required:!0,autoOpen:!1,"show-all-option":!0,disabled:!n.localOfferClass.optional_fields.enablehirearchyrestriction||!n.localOfferClass.optional_fields.hirearchyrestrictiongroups.length},null,8,["modelValue","items","disabled"])])])]),f("div",DT,[f("div",TT,[f("div",NT,[A(u,{required:"",text:"Número máximo de inscrições por concessionária",help:"Insira um número máximo de inscrições por concessionária para a turma, se houver. Exemplo: 5."}),A(c,{modelValue:n.localOfferClass.optional_fields.minusers,"onUpdate:modelValue":t[26]||(t[26]=C=>n.localOfferClass.optional_fields.minusers=C),type:"number","has-error":n.formErrors.minusers.hasError,"error-message":n.formErrors.minusers.message,onValidate:t[27]||(t[27]=C=>a.isValidField("minusers")),min:0,width:180},null,8,["modelValue","has-error","error-message"])])])]),f("div",IT,[f("div",AT,[f("div",{class:de(["form-group",{disabled:!n.localOfferClass.optional_fields.enableenrolperiod}])},[A(u,{required:"",id:"enrolperiod",text:"Duração da matrícula",help:"Insira um período em dias para a duração da matricula dos alunos, se houver, na turma. Exemplo: 15"+(a.maxEnrolPeriod?` O valor máximo permitido é de ${a.maxEnrolPeriod} dias, que corresponde ao período entre as datas de início e fim da turma.`:"")},null,8,["help"]),A(c,{modelValue:n.localOfferClass.optional_fields.enrolperiod,"onUpdate:modelValue":t[28]||(t[28]=C=>n.localOfferClass.optional_fields.enrolperiod=C),type:"number",id:"enrolperiod",disabled:!n.localOfferClass.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.enrolperiod.hasError,"error-message":n.formErrors.enrolperiod.message,max:a.maxEnrolPeriod,onValidate:t[29]||(t[29]=C=>a.isValidField("enrolperiod"))},null,8,["modelValue","disabled","has-error","error-message","max"])],2)]),f("div",MT,[f("div",PT,[A(u,{text:" "}),lt(A(h,{modelValue:n.localOfferClass.optional_fields.enableenrolperiod,"onUpdate:modelValue":t[30]||(t[30]=C=>n.localOfferClass.optional_fields.enableenrolperiod=C),id:"enableEnrolPeriod",label:"Habilitar duração da matrícula",class:"inline-checkbox",disabled:a.shouldDisableEnrolPeriod},null,8,["modelValue","disabled"]),[[w,a.shouldDisableEnrolPeriod?"Não é possível habilitar duração da matrícula para turmas de um dia (data início = data fim)":""]])])])]),f("div",kT,[f("div",{class:de(["form-group",{disabled:!n.localOfferClass.optional_fields.enableenrolperiod,"dependent-field":!0}])},[lt(A(h,{modelValue:n.localOfferClass.optional_fields.enableextension,"onUpdate:modelValue":t[31]||(t[31]=C=>n.localOfferClass.optional_fields.enableextension=C),id:"enableextension",label:"Habilitar prorrogação de matrícula",disabled:!n.localOfferClass.optional_fields.enableenrolperiod,help:"A prorrogação estende a duração da matrícula do usuário, permitindo sua permanência na turma enquanto ela estiver ativa. No entanto, não redefine seu progresso, garantindo que ele retome o curso de onde parou.<br><br> Nota: A prorrogação só pode ser habilitada quando o Prazo de conclusão da turma estiver habilitado."},null,8,["modelValue","disabled"]),[[w,n.localOfferClass.optional_fields.enableenrolperiod?"":"É necessário habilitar o duração da matrícula primeiro"]])],2)]),f("div",VT,[f("div",{class:de(["form-group",{disabled:!n.localOfferClass.optional_fields.enableextension||!n.localOfferClass.optional_fields.enableenrolperiod,"dependent-field":!0}])},[A(u,{required:"",text:"Quantos dias serão acrescentados para prorrogação?",help:"Insira um período em dias para prorrogar a matricula dos alunos da turma. Exemplo: 3."}),A(c,{modelValue:n.localOfferClass.optional_fields.extensionperiod,"onUpdate:modelValue":t[32]||(t[32]=C=>n.localOfferClass.optional_fields.extensionperiod=C),type:"number",width:180,disabled:!n.localOfferClass.optional_fields.enableextension||!n.localOfferClass.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.extensionperiod.hasError,"error-message":n.formErrors.extensionperiod.message,onValidate:t[33]||(t[33]=C=>a.isValidField("extensionperiod"))},null,8,["modelValue","disabled","has-error","error-message"])],2)]),f("div",RT,[f("div",{class:de(["form-group",{disabled:!n.localOfferClass.optional_fields.enableextension||!n.localOfferClass.optional_fields.enableenrolperiod,"dependent-field":!0}])},[A(u,{required:"",text:`Quantos dias antes do término do prazo de matrícula o botão de
            prorrogação deve ser exibido?`}),f("div",FT,[A(c,{modelValue:n.localOfferClass.optional_fields.extensiondaysavailable,"onUpdate:modelValue":t[34]||(t[34]=C=>n.localOfferClass.optional_fields.extensiondaysavailable=C),type:"number",width:180,disabled:!n.localOfferClass.optional_fields.enableextension||!n.localOfferClass.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.extensiondaysavailable.hasError,"error-message":n.formErrors.extensiondaysavailable.message,onValidate:t[35]||(t[35]=C=>a.isValidField("extensiondaysavailable"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),f("div",LT,[f("div",{class:de(["form-group",{disabled:!n.localOfferClass.optional_fields.enableextension||!n.localOfferClass.optional_fields.enableenrolperiod,"dependent-field":!0}])},[A(u,{required:"",text:"Quantas vezes o usuário pode pedir prorrogação?"}),f("div",UT,[A(c,{modelValue:n.localOfferClass.optional_fields.extensionmaxrequests,"onUpdate:modelValue":t[36]||(t[36]=C=>n.localOfferClass.optional_fields.extensionmaxrequests=C),type:"number",width:180,disabled:!n.localOfferClass.optional_fields.enableextension||!n.localOfferClass.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.extensionmaxrequests.hasError,"error-message":n.formErrors.extensionmaxrequests.message,onValidate:t[37]||(t[37]=C=>a.isValidField("extensionmaxrequests"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),n.localOfferClass.enrol=="offer_self"?(x(),D("div",BT,[f("div",$T,[f("div",qT,[A(u,{text:"Habilitar rematrícula",help:"Permite que usuários se matriculem novamente na turma após concluírem ou saírem dela."}),A(h,{modelValue:n.localOfferClass.optional_fields.enablereenrol,"onUpdate:modelValue":t[38]||(t[38]=C=>n.localOfferClass.optional_fields.enablereenrol=C),id:"enableReenrol",label:"Habilitar rematrícula",disabled:!1},null,8,["modelValue"])])]),f("div",{class:de(["form-group mb-3",{disabled:!n.localOfferClass.optional_fields.enablereenrol}])},[A(u,{text:"Quais situações de matrícula permitem rematrícula?"}),f("div",HT,[A(g,{modelValue:n.reenrolSituations,"onUpdate:modelValue":t[39]||(t[39]=C=>n.reenrolSituations=C),items:a.reenrolSituationList,placeholder:"Selecione as situações...",disabled:!n.localOfferClass.optional_fields.enablereenrol,width:280,"show-all-option":!0,"auto-open":!1,onSelectAll:a.handleSelectAllReenrolSituations},null,8,["modelValue","items","disabled","onSelectAll"])])],2)])):Z("",!0),f("div",WT,[A(u,{text:"Atribuir Instrutor",help:"Ao selecionar usuários para a composição do corpo docente, ele será matriculado na turma com o papel “Professor”."}),f("div",jT,[f("div",zT,[f("div",GT,[lt(f("input",{type:"text","onUpdate:modelValue":t[40]||(t[40]=C=>n.teacherSearchTerm=C),placeholder:"Pesquisar ...",class:"form-control custom-input",onInput:t[41]||(t[41]=(...C)=>a.handleTeacherInput&&a.handleTeacherInput(...C)),onFocus:t[42]||(t[42]=(...C)=>a.handleTeacherInputFocus&&a.handleTeacherInputFocus(...C)),onKeydown:t[43]||(t[43]=(...C)=>a.handleKeydown&&a.handleKeydown(...C)),ref:"teacherSearchInput"},null,544),[[Zt,n.teacherSearchTerm]])]),n.showTeacherDropdown&&n.teacherList.length>0?(x(),D("div",KT,[(x(!0),D(Ne,null,it(n.teacherList,(C,M)=>(x(),D("div",{key:C.id,class:de(["dropdown-item",{active:n.highlightedIndex===M}]),onClick:F=>a.selectTeacher(C),onMouseenter:F=>n.highlightedIndex=M},[f("div",null,[f("div",null,q(C.fullname),1),C.email?(x(),D("div",YT,q(C.email),1)):Z("",!0)])],42,ZT))),128))],512)):Z("",!0),n.showTeacherDropdown&&n.teacherSearchTerm.length>=3&&n.teacherList.length===0?(x(),D("div",JT,t[46]||(t[46]=[f("div",{class:"dropdown-item-text text-center fst-italic"}," Nenhum professor encontrado ",-1)]))):Z("",!0)],512),f("div",QT,[(x(!0),D(Ne,null,it(n.selectedTeachers,C=>(x(),D("a",{key:C.id,class:"tag badge bg-primary text-white p-2 cursor-pointer mr-2",onClick:M=>a.removeTeacher(C.id),style:{color:"white !important",cursor:"pointer"}},[t[47]||(t[47]=f("i",{class:"fas fa-times mr-1"},null,-1)),We(" "+q(C.fullname),1)],8,XT))),128))])])])])}const t3=Pe(WD,[["render",e3],["__scopeId","data-v-0313c350"]]),LM="",s3={name:"CreateEdit",components:{Form:t3,Toast:Br,CustomButton:mr,PageHeader:Hn,BackButton:jo,Autocomplete:_n},props:{offerCourseId:{type:[Number,String],required:!1},offerClassId:{type:[Number,String],required:!1,default:null}},data(){return{loading:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,enrolmentMethods:[],offerClass:{enrol:"",offercourseid:null,classname:"",startdate:"",teachers:[],optional_fields:{enableenddate:!1,enddate:"",enablepreenrolment:!1,preenrolmentstartdate:"",preenrolmentenddate:"",description:"",enableenrolperiod:!1,enrolperiod:null,minusers:null,maxusers:null,roleid:null,modality:null,enablereenrol:!1,reenrolmentsituations:[],enableextension:!1,extensionperiod:null,extensiondaysavailable:null,extensionmaxrequests:null,enablehirearchyrestriction:!1,hirearchyrestrictiondivisions:[],hirearchyrestrictionsectors:[],hirearchyrestrictiongroups:[],hirearchyrestrictiondealerships:[]}},offerCourse:null,selectedTeachers:[],teacherSearchTerm:"",debounceTimer:null,teacherList:[],showTeacherDropdown:!1,highlightedIndex:-1,reenrolSituations:[],validationAlert:{show:!1,message:"Há campos obrigatórios a serem preenchidos. Por favor, verifique os campos destacados."},isValidForm:!1}},async created(){this.isEditing&&await this.getOfferClass(),await this.getOfferCourse()},mounted(){window.scrollTo(0,0),document.addEventListener("click",this.handleClickOutside)},computed:{isEditing(){return this.offerClassId!==null},reenrolSituationList(){let e=[6,7,8,4,5];return this.situationList.filter(t=>e.includes(t.value))},maxEnrolPeriod(){if(this.offerClass.startdate&&this.offerClass.optional_fields.enddate&&this.offerClass.optional_fields.enableenddate){const e=this.calculateDaysDifference(this.offerClass.startdate,this.offerClass.optional_fields.enddate);return e>=1?e:1}return null},isOneDayClass(){return this.offerClass.startdate&&this.offerClass.optional_fields.enableenddate&&this.offerClass.optional_fields.enddate&&this.offerClass.startdate===this.offerClass.optional_fields.enddate},shouldDisableEnrolPeriod(){return this.isOneDayClass}},methods:{async getOfferClass(){this.loading=!0;const e=await pu(parseInt(this.offerClassId));this.offerClass=e,e.optional_fields&&this.processOptionalFields(e.optional_fields),e.teachers&&(this.selectedTeachers=e.teachers),this.updateUIAfterLoading(),document.title=`Editar Turma: ${this.offerClass.classname}`,this.loading=!1},async getOfferCourse(){var e;try{const t=await lb(this.offerCourseId||((e=this.offerClass)==null?void 0:e.offercourseid));this.offerCourse=t}catch{}},async createClass(){if(!this.$refs.form.isValidForm())return;this.loading=!0;const t=JSON.parse(JSON.stringify(this.offerClass));t.teachers=this.selectedTeachers.map(a=>a.id),(!t.optional_fields.enableextension||!t.optional_fields.enableenrolperiod)&&(t.optional_fields.extensionperiod=void 0,t.optional_fields.extensiondaysavailable=void 0,t.optional_fields.extensionmaxrequests=void 0,t.optional_fields.enableenrolperiod||(t.optional_fields.enableextension=!1)),t.optional_fields.enableenrolperiod||(t.optional_fields.enrolperiod=void 0),t.optional_fields.enablereenrol?t.optional_fields.reenrolmentsituations=this.reenrolSituations.map(a=>a.value):t.optional_fields.reenrolmentsituations=[],t.optional_fields.enablehirearchyrestriction?(t.optional_fields.hirearchyrestrictiondivisions=this.mapToValues(t.optional_fields.hirearchyrestrictiondivisions),t.optional_fields.hirearchyrestrictionsectors=this.mapToValues(t.optional_fields.hirearchyrestrictionsectors),t.optional_fields.hirearchyrestrictiongroups=this.mapToValues(t.optional_fields.hirearchyrestrictiongroups),t.optional_fields.hirearchyrestrictiondealerships=this.mapToValues(t.optional_fields.hirearchyrestrictiondealerships)):(t.optional_fields.hirearchyrestrictiondivisions=[],t.optional_fields.hirearchyrestrictionsectors=[],t.optional_fields.hirearchyrestrictiongroups=[],t.optional_fields.hirearchyrestrictiondealerships=[]);const i=["offercourseid","classname","startdate","enrol"].filter(a=>!t[a]);if(i.length>0){this.showErrorMessage(`Campos obrigatórios ausentes: ${i.join(", ")}`),this.loading=!1;return}t.offercourseid=parseInt(t.offercourseid);let n=await ab(t);this.showSuccessMessage(n.message),this.isEditing=!0,this.$router.push({name:"offer.class.edit",params:{offerCourseId:this.offerCourse.id,offerClassId:n.offerclassid}}),this.loading=!1},async updateClass(){if(!this.$refs.form.isValidForm())return;this.loading=!0;const t=JSON.parse(JSON.stringify(this.offerClass));t.teachers=this.selectedTeachers.map(a=>a.id),(!t.optional_fields.enableextension||!t.optional_fields.enableenrolperiod)&&(t.optional_fields.extensionperiod=void 0,t.optional_fields.extensiondaysavailable=void 0,t.optional_fields.extensionmaxrequests=void 0,t.optional_fields.enableenrolperiod||(t.optional_fields.enableextension=!1)),t.optional_fields.enableenrolperiod||(t.optional_fields.enrolperiod=void 0),t.optional_fields.enablereenrol?t.optional_fields.reenrolmentsituations=this.reenrolSituations.map(a=>a.value):t.optional_fields.reenrolmentsituations=[],t.optional_fields.enablehirearchyrestriction?(t.optional_fields.hirearchyrestrictiondivisions=this.mapToValues(t.optional_fields.hirearchyrestrictiondivisions),t.optional_fields.hirearchyrestrictionsectors=this.mapToValues(t.optional_fields.hirearchyrestrictionsectors),t.optional_fields.hirearchyrestrictiongroups=this.mapToValues(t.optional_fields.hirearchyrestrictiongroups),t.optional_fields.hirearchyrestrictiondealerships=this.mapToValues(t.optional_fields.hirearchyrestrictiondealerships)):(t.optional_fields.hirearchyrestrictiondivisions=[],t.optional_fields.hirearchyrestrictionsectors=[],t.optional_fields.hirearchyrestrictiongroups=[],t.optional_fields.hirearchyrestrictiondealerships=[]),"enrol"in t&&delete t.enrol;const i=["offercourseid","classname","startdate"].filter(a=>!t[a]);if(i.length>0){this.showErrorMessage(`Campos obrigatórios ausentes: ${i.join(", ")}`),this.loading=!1;return}t.offercourseid=parseInt(t.offercourseid),t.offerclassid=this.offerClassId;let n=await cb(t);this.showSuccessMessage(n.message),this.getOfferClass(),this.loading=!1},processOptionalFields(e){this.processDateFields(e),this.processEnrolmentFields(e),this.processHirarchyRestrictionFields(e),this.processUserLimits(e),this.processDescriptionAndRole(e),this.processReenrolment(e),this.processExtensionFields(e)},processHirarchyRestrictionFields(e){this.offerClass.optional_fields.hirearchyrestrictiondivisions=this.mapToOptions(e.hirearchyrestrictiondivisions),this.offerClass.optional_fields.hirearchyrestrictionsectors=this.mapToOptions(e.hirearchyrestrictionsectors),this.offerClass.optional_fields.hirearchyrestrictiongroups=this.mapToOptions(e.hirearchyrestrictiongroups),this.offerClass.optional_fields.hirearchyrestrictiondealerships=this.mapToOptions(e.hirearchyrestrictiondealerships)},processDateFields(e){e.enableenddate&&(this.offerClass.optional_fields.enableenddate=!0,this.offerClass.optional_fields.enddate=e.enddate||null),e.enablepreenrolment&&(this.offerClass.optional_fields.enablepreenrolment=!0,this.offerClass.optional_fields.preenrolmentstartdate=e.preenrolmentstartdate||null,this.offerClass.optional_fields.preenrolmentenddate=e.preenrolmentenddate||null)},processEnrolmentFields(e){e.enableenrolperiod?(this.offerClass.optional_fields.enableenrolperiod=!0,this.offerClass.optional_fields.enrolperiod=e.enrolperiod>0?e.enrolperiod:null):this.offerClass.optional_fields.enrolperiod=null},processUserLimits(e){this.offerClass.optional_fields.minusers=e.minusers>0?e.minusers:null,this.offerClass.optional_fields.maxusers=e.maxusers>0?e.maxusers:null},processDescriptionAndRole(e){this.offerClass.optional_fields.roleid=e.roleid||null,this.offerClass.optional_fields.description=e.description||"",this.offerClass.optional_fields.modality=e.modality||null},processReenrolment(e){e.enablereenrol?(this.offerClass.optional_fields.enablereenrol=!0,this.offerClass.optional_fields.reenrolmentsituations=e.reenrolmentsituations||[],Array.isArray(e.reenrolmentsituations)&&(this.reenrolSituations=e.reenrolmentsituations.map(t=>this.situationList.find(s=>s.value===parseInt(t))))):this.offerClass.optional_fields.reenrolmentsituations=[]},processExtensionFields(e){e.enableextension&&e.enableenrolperiod?(this.offerClass.optional_fields.enableextension=!0,this.processExtensionPeriods(e),this.processExtensionSituations(e)):this.resetExtensionFields()},processExtensionPeriods(e){this.offerClass.optional_fields.extensionperiod=e.extensionperiod>0?e.extensionperiod:null,this.offerClass.optional_fields.extensiondaysavailable=e.extensiondaysavailable>0?e.extensiondaysavailable:null,this.offerClass.optional_fields.extensionmaxrequests=e.extensionmaxrequests>0?e.extensionmaxrequests:null},mapToOptions(e){return e.map(t=>({value:t,label:""}))},mapToValues(e){return e.map(t=>t.value)},resetExtensionFields(){this.offerClass.optional_fields.extensionperiod=null,this.offerClass.optional_fields.extensiondaysavailable=null,this.offerClass.optional_fields.extensionmaxrequests=null,this.extensionSituations=[]},calculateDaysDifference(e,t){if(!e||!t)return 0;const s=new Date(e),i=new Date(t);if(isNaN(s.getTime())||isNaN(i.getTime()))return 0;if(s.getTime()===i.getTime())return 1;const n=Math.abs(i-s);return Math.ceil(n/(1e3*60*60*24))},checkAndDisableEnrolPeriodForOneDayClass(){this.isOneDayClass&&this.offerClass.optional_fields.enableenrolperiod&&(this.offerClass.optional_fields.enableenrolperiod=!1,this.offerClass.optional_fields.enrolperiod=null,this.offerClass.optional_fields.enableextension&&(this.offerClass.optional_fields.enableextension=!1,this.offerClass.optional_fields.extensionperiod=null,this.offerClass.optional_fields.extensiondaysavailable=null,this.offerClass.optional_fields.extensionmaxrequests=null,this.extensionSituations=[]),this.showWarningMessage("Prazo de conclusão foi desabilitado automaticamente porque a turma tem duração de apenas um dia (data início = data fim)."))},handleTeacherInput(){const e=this.teacherSearchTerm.trim();this.debounceTimer&&clearTimeout(this.debounceTimer),e.length>=3?(this.showTeacherDropdown=!0,this.highlightedIndex=-1,this.debounceTimer=setTimeout(async()=>{await this.fetchPotentialTeachers(e)},500)):(this.showTeacherDropdown=!1,this.teacherList=[])},async fetchPotentialTeachers(e){let t=this.selectedTeachers.map(s=>s.id||s.value)??[];this.teacherList=await ep(this.offerCourseId,this.offerClassId,e,t)},removeTeacher(e){this.selectedTeachers=this.selectedTeachers.filter(t=>t.id!==e)},handleTeacherInputFocus(){this.teacherSearchTerm.length>=3&&this.teacherList.length>0&&(this.showTeacherDropdown=!0)},selectTeacher(e){this.selectedTeachers.push({id:e.id,value:e.id,fullname:e.fullname,email:e.email}),this.teacherSearchTerm="",this.showTeacherDropdown=!1,this.highlightedIndex=-1,this.teacherList=[],this.$nextTick(()=>{this.$refs.teacherSearchInput&&this.$refs.teacherSearchInput.focus()})},handleKeydown(e){if(!(!this.showTeacherDropdown||this.teacherList.length===0))switch(e.key){case"ArrowDown":e.preventDefault(),this.highlightedIndex=Math.min(this.highlightedIndex+1,this.teacherList.length-1);break;case"ArrowUp":e.preventDefault(),this.highlightedIndex=Math.max(this.highlightedIndex-1,0);break;case"Enter":e.preventDefault(),this.highlightedIndex>=0&&this.highlightedIndex<this.teacherList.length&&this.selectTeacher(this.teacherList[this.highlightedIndex]);break;case"Escape":e.preventDefault(),this.showTeacherDropdown=!1,this.highlightedIndex=-1;break}},handleClickOutside(e){this.$refs.teacherSearchContainer&&!this.$refs.teacherSearchContainer.contains(e.target)&&(this.showTeacherDropdown=!1,this.highlightedIndex=-1)},updateUIAfterLoading(){this.$nextTick(()=>{this.updateFormFields(),this.$forceUpdate(),(!this.offerClass.classname||!this.offerClass.enrol)&&this.showErrorMessage("Dados incompletos após carregamento.")})},updateFormFields(){this.updateSelectField("enrolSelect",this.offerClass.enrol),this.updateInputField("classnameInput",this.offerClass.classname),this.updateInputField("startdateInput",this.offerClass.startdate)},updateSelectField(e,t){if(this.$refs[e]){this.$refs[e].value=t;const s=new Event("change");this.$refs[e].$el.dispatchEvent(s)}},updateInputField(e,t){if(this.$refs[e]&&t){this.$refs[e].value=t;const s=new Event("input");this.$refs[e].$el.dispatchEvent(s)}},goBack(){this.offerCourse.offerid?this.$router.push({name:"offer.edit",params:{id:this.offerCourse.offerid}}):this.router.push({name:"offer.index"})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showWarningMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="warning",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},updateFormFields(){if(this.$refs.enrolSelect&&this.offerClass.enrol)try{this.$refs.enrolSelect.value=this.offerClass.enrol;const e=new Event("change");this.$refs.enrolSelect.$el.dispatchEvent(e),this.$refs.enrolSelect.$emit("input",this.offerClass.enrol),this.$refs.enrolSelect.$forceUpdate()}catch{}if(this.$refs.classnameInput&&this.offerClass.classname)try{this.$refs.classnameInput.value=this.offerClass.classname;const e=new Event("input");this.$refs.classnameInput.$el.dispatchEvent(e),this.$refs.classnameInput.$emit("input",this.offerClass.classname),this.$refs.classnameInput.$forceUpdate()}catch{}if(this.$refs.startdateInput&&this.offerClass.startdate)try{this.$refs.startdateInput.value=this.offerClass.startdate;const e=new Event("input");this.$refs.startdateInput.$el.dispatchEvent(e),this.$refs.startdateInput.$emit("input",this.offerClass.startdate),this.$refs.startdateInput.$forceUpdate()}catch{}this.$forceUpdate()},handleSelectAllReenrolSituations(){const e=this.reenrolSituationList.every(t=>this.reenrolSituations.some(s=>s.value===t.value));this.reenrolSituations=e?[]:[...this.reenrolSituationList],this.offerClass.optional_fields.reenrolmentsituations=this.reenrolSituations.map(t=>t.value)},restartComponent(){window.scrollTo(0,0),this.updateFormFields(),this.$forceUpdate(),setTimeout(()=>{this.updateFormFields(),this.$forceUpdate(),window.scrollTo(0,0)},500)}}},r3={class:"new-class",ref:"classView"},n3={class:"page-header-container"},o3={key:0,class:"validation-alert"},i3={class:"actions-container"},a3={key:2,class:"loading"};function l3(e,t,s,i,n,a){const u=z("BackButton"),c=z("PageHeader"),h=z("Form"),_=z("CustomButton"),p=z("Toast");return x(),D("div",r3,[f("div",n3,[A(c,{title:a.isEditing?"Editar turma":"Adicionar Turma"},{actions:Te(()=>[A(u,{onClick:a.goBack},null,8,["onClick"])]),_:1},8,["title"])]),n.validationAlert.show?(x(),D("div",o3,[t[2]||(t[2]=f("i",{class:"fas fa-exclamation-triangle"},null,-1)),f("span",null,q(n.validationAlert.message),1)])):Z("",!0),t[4]||(t[4]=f("div",{class:"section-container"},[f("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS")],-1)),n.offerCourse?(x(),dt(h,{key:1,ref:"form",offerClass:n.offerClass,"onUpdate:offerClass":t[0]||(t[0]=g=>n.offerClass=g),offerCourse:n.offerCourse,isEditing:a.isEditing},null,8,["offerClass","offerCourse","isEditing"])):Z("",!0),t[5]||(t[5]=f("div",{class:"required-fields-message"},[f("div",{class:"form-info"},[We(" Este formulário contém campos obrigatórios marcados com "),f("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),f("div",i3,[A(_,{variant:"primary",label:"Salvar",loading:n.loading,onClick:t[1]||(t[1]=g=>a.isEditing?a.updateClass():a.createClass())},null,8,["loading"]),A(_,{variant:"secondary",label:"Cancelar",onClick:a.goBack},null,8,["onClick"])]),n.loading?(x(),D("div",a3,t[3]||(t[3]=[f("div",{class:"spinner-border",role:"status"},[f("span",{class:"sr-only"},"Carregando...")],-1)]))):Z("",!0),A(p,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])],512)}const op=Pe(s3,[["render",l3],["__scopeId","data-v-815bb8d7"]]),u3="/local/offermanager/",c3=(()=>{const e=window.location.host,t=window.location.pathname,s=Ug.wwwroot.replace(/^https?\:\/\//i,"").replace(e,"").concat(u3);return t.includes("index.php")?s+"index.php":s})(),d3=[{path:"/",name:"offer.index",component:dw,meta:{title:"Gerenciar Ofertas"}},{path:"/offers/create",name:"offer.create",component:yu,meta:{title:"Nova Oferta"}},{path:"/offers/:id/edit",name:"offer.edit",component:yu,props:!0,meta:{title:"Editar Oferta"}},{path:"/offers/:id",name:"offer.show",component:yu,props:{isReadonly:!0},meta:{title:"Visualizar Oferta"}},{path:"/offers/classes/create",name:"offer.class.create",component:op,props:!0,meta:{title:"Nova Turma"}},{path:"/offers/classes/:offerClassId/edit",name:"offer.class.edit",component:op,props:!0,meta:{title:"Editar Turma"}},{path:"/enrollments/:classId",name:"Enrollments",component:p2,props:!0,meta:{title:"Usuários matriculados"}},{path:"/:pathMatch(.*)*",redirect:"/"}],ia=Ky({history:oy(c3),routes:d3,scrollBehavior(){return{top:0}}});ia.beforeEach((e,t,s)=>{document.title=e.meta.title||"Gerenciar Ofertas",s()}),ia.onError(e=>{console.error("Erro de navegação:",e),(e.name==="NavigationDuplicated"||e.message.includes("No match")||e.message.includes("missing required param"))&&ia.push("/")});const UM="",f3=()=>{const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)};return{init:(e,t={})=>{f3();const s=Gv(N0);return s.use(x0()),s.use(ia),s.mount(e),s}}});
//# sourceMappingURL=app-lazy.min.js.map
