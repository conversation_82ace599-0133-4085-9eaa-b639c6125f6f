<template>
  <div class="new-class" ref="classView">
    <div class="page-header-container">
      <PageHeader :title="isEditing ? 'Editar turma' : 'Adicionar Turma'">
        <template #actions>
          <BackButton @click="goBack" />
        </template>
      </PageHeader>
    </div>

    <div v-if="validationAlert.show" class="validation-alert">
      <i class="fas fa-exclamation-triangle"></i>
      <span>{{ validationAlert.message }}</span>
    </div>

    <div class="section-container">
      <h2 class="section-title">CONFIGURAÇÕES GERAIS</h2>
    </div>

    <Form
      v-if="offerCourse"
      ref="form"
      v-model:offerClass="offerClass"
      :offerCourse="offerCourse"
      :isEditing="isEditing"
    />

    <div class="required-fields-message">
      <div class="form-info">
        Este formulário contém campos obrigatórios marcados com
        <i class="fa fa-exclamation-circle text-danger"></i>
      </div>
    </div>

    <div class="actions-container">
      <CustomButton
        variant="primary"
        label="Salvar"
        :loading="loading"
        @click="isEditing ? updateClass() : createClass()"
      />
      <CustomButton variant="secondary" label="Cancelar" @click="goBack" />
    </div>

    <div class="loading" v-if="loading">
      <div class="spinner-border" role="status">
        <span class="sr-only">Carregando...</span>
      </div>
    </div>

    <Toast
      :show="showToast"
      :message="toastMessage"
      :type="toastType"
      :duration="3000"
    />
  </div>
</template>

<script>
import { addClass, updateClass, getClass, getCourse } from "@/services/offer";
import { getPotentialTeachers } from "@/services/offer";

import Toast from "@/components/Toast.vue";
import Form from "@/components/offer-class/Form.vue";
import CustomButton from "@/components/CustomButton.vue";
import PageHeader from "@/components/PageHeader.vue";
import BackButton from "@/components/BackButton.vue";
import Autocomplete from "@/components/Autocomplete.vue";

export default {
  name: "CreateEdit",

  components: {
    Form,
    Toast,
    CustomButton,
    PageHeader,
    BackButton,
    Autocomplete,
  },

  props: {
    offerCourseId: {
      type: [Number, String],
      required: false,
    },

    offerClassId: {
      type: [Number, String],
      required: false,
      default: null,
    },
  },

  data() {
    return {
      loading: false,
      showToast: false,
      toastMessage: "",
      toastType: "success",
      toastTimeout: null,

      // Métodos de inscrição disponíveis
      enrolmentMethods: [],

      // Dados da turma
      offerClass: {
        enrol: "", // Será definido pelo usuário
        offercourseid: null,
        classname: "",
        startdate: "",
        teachers: [],
        optional_fields: {
          enableenddate: false,
          enddate: "",
          enablepreenrolment: false,
          preenrolmentstartdate: "",
          preenrolmentenddate: "",
          description: "",
          enableenrolperiod: false,
          enrolperiod: null,
          minusers: null,
          maxusers: null,
          roleid: null,
          modality: null,
          enablereenrol: false,
          reenrolmentsituations: [],
          enableextension: false,
          extensionperiod: null,
          extensiondaysavailable: null,
          extensionmaxrequests: null,
          enablehirearchyrestriction: false,
          hirearchyrestrictiondivisions: [],
          hirearchyrestrictionsectors: [],
          hirearchyrestrictiongroups: [],
          hirearchyrestrictiondealerships: [],
        },
      },
      offerCourse: null,

      // Professores selecionados (para o Autocomplete)
      selectedTeachers: [],
      teacherSearchTerm: "",
      debounceTimer: null,
      teacherList: [],

      // Controle do dropdown de professores
      showTeacherDropdown: false,
      highlightedIndex: -1,

      reenrolSituations: [],

      // Alerta de validação
      validationAlert: {
        show: false,
        message:
          "Há campos obrigatórios a serem preenchidos. Por favor, verifique os campos destacados.",
      },

      isValidForm: false,
    };
  },

  async created() {
    if (this.isEditing) {
      await this.getOfferClass();
    }

    await this.getOfferCourse();
  },

  mounted() {
    // Garantir que a página role para o topo quando for carregada
    window.scrollTo(0, 0);

    // Adicionar listener para cliques fora do dropdown de professores
    document.addEventListener("click", this.handleClickOutside);
  },

  computed: {
    isEditing() {
      return this.offerClassId !== null;
    },

    reenrolSituationList() {
      let allowedSituations = [6, 7, 8, 4, 5];
      return this.situationList.filter((situation) =>
        allowedSituations.includes(situation.value)
      );
    },

    // Calcula o valor máximo permitido para o prazo de conclusão da turma
    maxEnrolPeriod() {
      // Só calcula se ambas as datas estiverem preenchidas e a data fim estiver habilitada
      if (
        this.offerClass.startdate &&
        this.offerClass.optional_fields.enddate &&
        this.offerClass.optional_fields.enableenddate
      ) {
        // Calcula a diferença em dias entre as datas
        const daysDifference = this.calculateDaysDifference(
          this.offerClass.startdate,
          this.offerClass.optional_fields.enddate
        );

        // Retorna a diferença em dias como valor máximo (mínimo 1 dia)
        return daysDifference >= 1 ? daysDifference : 1;
      }

      // Se alguma das condições não for atendida, retorna null (sem limite)
      return null;
    },

    // Verifica se as datas de início e fim são iguais (turma de um dia)
    isOneDayClass() {
      // Só considera turma de um dia se:
      // 1. Data início estiver preenchida
      // 2. Data fim estiver habilitada E preenchida
      // 3. As duas datas forem iguais
      return (
        this.offerClass.startdate &&
        this.offerClass.optional_fields.enableenddate &&
        this.offerClass.optional_fields.enddate &&
        this.offerClass.startdate === this.offerClass.optional_fields.enddate
      );
    },

    // Verifica se o prazo de conclusão deve estar desabilitado
    shouldDisableEnrolPeriod() {
      // Só desabilita se for realmente uma turma de um dia
      // (ambas as datas preenchidas e iguais)
      return this.isOneDayClass;
    },
  },

  methods: {
    /**
     * Carrega os dados de uma turma existente para edição
     */
    async getOfferClass() {
      this.loading = true;

      const response = await getClass(parseInt(this.offerClassId));

      this.offerClass = response;

      if (response.optional_fields) {
        this.processOptionalFields(response.optional_fields);
      }

      if (response.teachers) {
        this.selectedTeachers = response.teachers;
      }

      this.updateUIAfterLoading();

      document.title = `Editar Turma: ${this.offerClass.classname}`;

      this.loading = false;
    },

    /**
     * Carrega informações do curso da oferta
     */
    async getOfferCourse() {
      try {
        const response = await getCourse(
          this.offerCourseId || this.offerClass?.offercourseid
        );

        this.offerCourse = response;
      } catch (error) {
        //
      }
    },

    /**
     * Salva a turma
     */
    async saveClass() {
      const isValid = this.$refs.form.isValidForm();

      if (!isValid) return;

      this.loading = true;

      const dataCopy = JSON.parse(JSON.stringify(this.offerClass));

      dataCopy.teachers = this.selectedTeachers.map((t) => t.id);

      if (
        !dataCopy.optional_fields.enableextension ||
        !dataCopy.optional_fields.enableenrolperiod
      ) {
        dataCopy.optional_fields.extensionperiod = undefined;
        dataCopy.optional_fields.extensiondaysavailable = undefined;
        dataCopy.optional_fields.extensionmaxrequests = undefined;

        if (!dataCopy.optional_fields.enableenrolperiod) {
          dataCopy.optional_fields.enableextension = false;
        }
      }

      if (!dataCopy.optional_fields.enableenrolperiod) {
        dataCopy.optional_fields.enrolperiod = undefined;
      }

      if (!dataCopy.optional_fields.enablereenrol) {
        dataCopy.optional_fields.reenrolmentsituations = [];
      } else {
        dataCopy.optional_fields.reenrolmentsituations =
          this.reenrolSituations.map((item) => item.value);
      }

      if (!dataCopy.optional_fields.enablehirearchyrestriction) {
        dataCopy.optional_fields.hirearchyrestrictiondivisions = [];
        dataCopy.optional_fields.hirearchyrestrictionsectors = [];
        dataCopy.optional_fields.hirearchyrestrictiongroups = [];
        dataCopy.optional_fields.hirearchyrestrictiondealerships = [];
      } else {
        dataCopy.optional_fields.hirearchyrestrictiondivisions =
          this.mapToValues(
            dataCopy.optional_fields.hirearchyrestrictiondivisions
          );
        dataCopy.optional_fields.hirearchyrestrictionsectors = this.mapToValues(
          dataCopy.optional_fields.hirearchyrestrictionsectors
        );
        dataCopy.optional_fields.hirearchyrestrictiongroups = this.mapToValues(
          dataCopy.optional_fields.hirearchyrestrictiongroups
        );
        dataCopy.optional_fields.hirearchyrestrictiondealerships =
          this.mapToValues(
            dataCopy.optional_fields.hirearchyrestrictiondealerships
          );
      }

      const numericFields = [
        "enrolperiod",
        "extensionperiod",
        "extensiondaysavailable",
        "extensionmaxrequests",
        "minusers",
        "maxusers",
      ];

      if (this.isEditing && "enrol" in dataCopy) {
        delete dataCopy.enrol;
      }

      const requiredFields = this.isEditing
        ? ["offercourseid", "classname", "startdate"]
        : ["offercourseid", "classname", "startdate", "enrol"];
      const missingFields = requiredFields.filter((field) => !dataCopy[field]);

      if (missingFields.length > 0) {
        this.showErrorMessage(
          `Campos obrigatórios ausentes: ${missingFields.join(", ")}`
        );
        this.loading = false;
        return;
      }

      dataCopy.offercourseid = parseInt(dataCopy.offercourseid);

      if (this.isEditing && this.offerClassId) {
        dataCopy.offerclassid = this.offerClassId;

        let response = await updateClass(dataCopy);

        this.showSuccessMessage(response.message);

        this.getOfferClass();
      } else {
        let response = await addClass(dataCopy);

        this.showSuccessMessage(response.message);

        this.isEditing = true;

        this.$router.push({
          name: "offer.class.edit",
          params: {
            offerCourseId: this.offerCourse.id,
            offerClassId: response.offerclassid,
          },
        });
      }

      this.loading = false;
    },

    async createClass() {
      const isValid = this.$refs.form.isValidForm();

      if (!isValid) return;

      this.loading = true;

      const dataCopy = JSON.parse(JSON.stringify(this.offerClass));

      dataCopy.teachers = this.selectedTeachers.map((t) => t.id);

      if (
        !dataCopy.optional_fields.enableextension ||
        !dataCopy.optional_fields.enableenrolperiod
      ) {
        dataCopy.optional_fields.extensionperiod = undefined;
        dataCopy.optional_fields.extensiondaysavailable = undefined;
        dataCopy.optional_fields.extensionmaxrequests = undefined;

        if (!dataCopy.optional_fields.enableenrolperiod) {
          dataCopy.optional_fields.enableextension = false;
        }
      }

      if (!dataCopy.optional_fields.enableenrolperiod) {
        dataCopy.optional_fields.enrolperiod = undefined;
      }

      if (!dataCopy.optional_fields.enablereenrol) {
        dataCopy.optional_fields.reenrolmentsituations = [];
      } else {
        dataCopy.optional_fields.reenrolmentsituations =
          this.reenrolSituations.map((item) => item.value);
      }

      if (!dataCopy.optional_fields.enablehirearchyrestriction) {
        dataCopy.optional_fields.hirearchyrestrictiondivisions = [];
        dataCopy.optional_fields.hirearchyrestrictionsectors = [];
        dataCopy.optional_fields.hirearchyrestrictiongroups = [];
        dataCopy.optional_fields.hirearchyrestrictiondealerships = [];
      } else {
        dataCopy.optional_fields.hirearchyrestrictiondivisions =
          this.mapToValues(
            dataCopy.optional_fields.hirearchyrestrictiondivisions
          );
        dataCopy.optional_fields.hirearchyrestrictionsectors = this.mapToValues(
          dataCopy.optional_fields.hirearchyrestrictionsectors
        );
        dataCopy.optional_fields.hirearchyrestrictiongroups = this.mapToValues(
          dataCopy.optional_fields.hirearchyrestrictiongroups
        );
        dataCopy.optional_fields.hirearchyrestrictiondealerships =
          this.mapToValues(
            dataCopy.optional_fields.hirearchyrestrictiondealerships
          );
      }

      const requiredFields = ["offercourseid", "classname", "startdate", "enrol"];
      const missingFields = requiredFields.filter((field) => !dataCopy[field]);

      if (missingFields.length > 0) {
        this.showErrorMessage(
          `Campos obrigatórios ausentes: ${missingFields.join(", ")}`
        );
        this.loading = false;
        return;
      }

      dataCopy.offercourseid = parseInt(dataCopy.offercourseid);

      let response = await addClass(dataCopy);

      this.showSuccessMessage(response.message);

      this.isEditing = true;

      this.$router.push({
        name: "offer.class.edit",
        params: {
          offerCourseId: this.offerCourse.id,
          offerClassId: response.offerclassid,
        },
      });

      this.loading = false;
    },

    async updateClass() {
      const isValid = this.$refs.form.isValidForm();

      if (!isValid) return;

      this.loading = true;

      const dataCopy = JSON.parse(JSON.stringify(this.offerClass));

      dataCopy.teachers = this.selectedTeachers.map((t) => t.id);

      if (
        !dataCopy.optional_fields.enableextension ||
        !dataCopy.optional_fields.enableenrolperiod
      ) {
        dataCopy.optional_fields.extensionperiod = undefined;
        dataCopy.optional_fields.extensiondaysavailable = undefined;
        dataCopy.optional_fields.extensionmaxrequests = undefined;

        if (!dataCopy.optional_fields.enableenrolperiod) {
          dataCopy.optional_fields.enableextension = false;
        }
      }

      if (!dataCopy.optional_fields.enableenrolperiod) {
        dataCopy.optional_fields.enrolperiod = undefined;
      }

      if (!dataCopy.optional_fields.enablereenrol) {
        dataCopy.optional_fields.reenrolmentsituations = [];
      } else {
        dataCopy.optional_fields.reenrolmentsituations =
          this.reenrolSituations.map((item) => item.value);
      }

      if (!dataCopy.optional_fields.enablehirearchyrestriction) {
        dataCopy.optional_fields.hirearchyrestrictiondivisions = [];
        dataCopy.optional_fields.hirearchyrestrictionsectors = [];
        dataCopy.optional_fields.hirearchyrestrictiongroups = [];
        dataCopy.optional_fields.hirearchyrestrictiondealerships = [];
      } else {
        dataCopy.optional_fields.hirearchyrestrictiondivisions =
          this.mapToValues(
            dataCopy.optional_fields.hirearchyrestrictiondivisions
          );
        dataCopy.optional_fields.hirearchyrestrictionsectors = this.mapToValues(
          dataCopy.optional_fields.hirearchyrestrictionsectors
        );
        dataCopy.optional_fields.hirearchyrestrictiongroups = this.mapToValues(
          dataCopy.optional_fields.hirearchyrestrictiongroups
        );
        dataCopy.optional_fields.hirearchyrestrictiondealerships =
          this.mapToValues(
            dataCopy.optional_fields.hirearchyrestrictiondealerships
          );
      }

      // Remove enrol field for updates
      if ("enrol" in dataCopy) {
        delete dataCopy.enrol;
      }

      const requiredFields = ["offercourseid", "classname", "startdate"];
      const missingFields = requiredFields.filter((field) => !dataCopy[field]);

      if (missingFields.length > 0) {
        this.showErrorMessage(
          `Campos obrigatórios ausentes: ${missingFields.join(", ")}`
        );
        this.loading = false;
        return;
      }

      dataCopy.offercourseid = parseInt(dataCopy.offercourseid);
      dataCopy.offerclassid = this.offerClassId;

      let response = await updateClass(dataCopy);

      this.showSuccessMessage(response.message);

      this.getOfferClass();

      this.loading = false;
    },

    /**
     * Processa os campos opcionais da turma
     */
    processOptionalFields(optionalFields) {
      this.processDateFields(optionalFields);
      this.processEnrolmentFields(optionalFields);
      this.processHirarchyRestrictionFields(optionalFields);
      this.processUserLimits(optionalFields);
      this.processDescriptionAndRole(optionalFields);
      this.processReenrolment(optionalFields);
      this.processExtensionFields(optionalFields);
    },

    processHirarchyRestrictionFields(fields) {
      this.offerClass.optional_fields.hirearchyrestrictiondivisions =
        this.mapToOptions(fields.hirearchyrestrictiondivisions);
      this.offerClass.optional_fields.hirearchyrestrictionsectors =
        this.mapToOptions(fields.hirearchyrestrictionsectors);
      this.offerClass.optional_fields.hirearchyrestrictiongroups =
        this.mapToOptions(fields.hirearchyrestrictiongroups);
      this.offerClass.optional_fields.hirearchyrestrictiondealerships =
        this.mapToOptions(fields.hirearchyrestrictiondealerships);
    },
    /**
     * Processa campos relacionados a datas
     */
    processDateFields(fields) {
      // Data de fim
      if (fields.enableenddate) {
        this.offerClass.optional_fields.enableenddate = true;
        this.offerClass.optional_fields.enddate = fields.enddate || null;
      }

      // Pré-inscrição
      if (fields.enablepreenrolment) {
        this.offerClass.optional_fields.enablepreenrolment = true;
        this.offerClass.optional_fields.preenrolmentstartdate =
          fields.preenrolmentstartdate || null;
        this.offerClass.optional_fields.preenrolmentenddate =
          fields.preenrolmentenddate || null;
      }
    },

    /**
     * Processa campos de matrícula
     */
    processEnrolmentFields(fields) {
      // Prazo de conclusão
      if (fields.enableenrolperiod) {
        this.offerClass.optional_fields.enableenrolperiod = true;
        this.offerClass.optional_fields.enrolperiod =
          fields.enrolperiod > 0 ? fields.enrolperiod : null;
      } else {
        this.offerClass.optional_fields.enrolperiod = null;
      }
    },

    /**
     * Processa limites de usuários
     */
    processUserLimits(fields) {
      // Vagas mínimas e máximas
      this.offerClass.optional_fields.minusers =
        fields.minusers > 0 ? fields.minusers : null;
      this.offerClass.optional_fields.maxusers =
        fields.maxusers > 0 ? fields.maxusers : null;
    },

    /**
     * Processa descrição e papel padrão
     */
    processDescriptionAndRole(fields) {
      this.offerClass.optional_fields.roleid = fields.roleid || null;
      this.offerClass.optional_fields.description = fields.description || "";
      this.offerClass.optional_fields.modality = fields.modality || null;
    },

    /**
     * Processa rematrícula
     */
    processReenrolment(fields) {
      if (fields.enablereenrol) {
        this.offerClass.optional_fields.enablereenrol = true;
        this.offerClass.optional_fields.reenrolmentsituations =
          fields.reenrolmentsituations || [];

        // Atualiza o array de situações para o Autocomplete
        if (Array.isArray(fields.reenrolmentsituations)) {
          this.reenrolSituations = fields.reenrolmentsituations.map((id) => {
            return this.situationList.find((s) => s.value === parseInt(id));
          });
        }
      } else {
        this.offerClass.optional_fields.reenrolmentsituations = [];
      }
    },

    /**
     * Processa campos de prorrogação
     */
    processExtensionFields(fields) {
      if (fields.enableextension && fields.enableenrolperiod) {
        this.offerClass.optional_fields.enableextension = true;
        this.processExtensionPeriods(fields);
        this.processExtensionSituations(fields);
      } else {
        this.resetExtensionFields();
      }
    },

    /**
     * Processa períodos de prorrogação
     */
    processExtensionPeriods(fields) {
      this.offerClass.optional_fields.extensionperiod =
        fields.extensionperiod > 0 ? fields.extensionperiod : null;
      this.offerClass.optional_fields.extensiondaysavailable =
        fields.extensiondaysavailable > 0
          ? fields.extensiondaysavailable
          : null;
      this.offerClass.optional_fields.extensionmaxrequests =
        fields.extensionmaxrequests > 0 ? fields.extensionmaxrequests : null;
    },

    mapToOptions(array) {
      return array.map((item) => ({ value: item, label: "" }));
    },

    mapToValues(array) {
      return array.map((item) => item.value);
    },
    /**
     * Reseta campos de prorrogação
     */
    resetExtensionFields() {
      this.offerClass.optional_fields.extensionperiod = null;
      this.offerClass.optional_fields.extensiondaysavailable = null;
      this.offerClass.optional_fields.extensionmaxrequests = null;
      this.extensionSituations = [];
    },

    /**
     * Calcula a diferença em dias entre duas datas
     * @param {string} startDate - Data de início no formato YYYY-MM-DD
     * @param {string} endDate - Data de fim no formato YYYY-MM-DD
     * @returns {number} - Diferença em dias entre as datas
     */
    calculateDaysDifference(startDate, endDate) {
      // Verifica se as datas são válidas
      if (!startDate || !endDate) {
        return 0;
      }

      // Converte as strings de data para objetos Date
      const start = new Date(startDate);
      const end = new Date(endDate);

      // Verifica se as datas são válidas
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return 0;
      }

      // Se as datas são iguais, retorna 1 (um dia de prazo)
      if (start.getTime() === end.getTime()) {
        return 1;
      }

      // Calcula a diferença em milissegundos
      const diffTime = Math.abs(end - start);

      // Converte para dias e arredonda para cima
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      return diffDays;
    },

    /**
     * Verifica se deve desabilitar o prazo de conclusão para turmas de um dia
     */
    checkAndDisableEnrolPeriodForOneDayClass() {
      // Se as datas são iguais (turma de um dia), desabilita o prazo de conclusão
      if (
        this.isOneDayClass &&
        this.offerClass.optional_fields.enableenrolperiod
      ) {
        this.offerClass.optional_fields.enableenrolperiod = false;
        this.offerClass.optional_fields.enrolperiod = null;

        // Também desabilita a prorrogação se estiver habilitada
        if (this.offerClass.optional_fields.enableextension) {
          this.offerClass.optional_fields.enableextension = false;
          this.offerClass.optional_fields.extensionperiod = null;
          this.offerClass.optional_fields.extensiondaysavailable = null;
          this.offerClass.optional_fields.extensionmaxrequests = null;
          this.extensionSituations = [];
        }

        // Exibe mensagem informativa
        this.showWarningMessage(
          "Prazo de conclusão foi desabilitado automaticamente porque a turma tem duração de apenas um dia (data início = data fim)."
        );
      }
    },

    handleTeacherInput() {
      const term = this.teacherSearchTerm.trim();

      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      if (term.length >= 3) {
        this.showTeacherDropdown = true;
        this.highlightedIndex = -1;
        this.debounceTimer = setTimeout(async () => {
          await this.fetchPotentialTeachers(term);
        }, 500);
      } else {
        this.showTeacherDropdown = false;
        this.teacherList = [];
      }
    },

    /**
     * Recupera lista de usuários potenciais para adicionar como professor
     */
    async fetchPotentialTeachers(searchString) {
      let selectedTeacherIds =
        this.selectedTeachers.map((teacher) => teacher.id || teacher.value) ??
        [];

      this.teacherList = await getPotentialTeachers(
        this.offerCourseId,
        this.offerClassId,
        searchString,
        selectedTeacherIds
      );
    },

    /**
     * Remove usuário da lista de professor
     */
    removeTeacher(teacherId) {
      this.selectedTeachers = this.selectedTeachers.filter(
        (teacher) => teacher.id !== teacherId
      );
    },

    /**
     * Manipula o foco no campo de busca de professores
     */
    handleTeacherInputFocus() {
      if (this.teacherSearchTerm.length >= 3 && this.teacherList.length > 0) {
        this.showTeacherDropdown = true;
      }
    },

    /**
     * Seleciona um professor do dropdown
     */
    selectTeacher(teacher) {
      // Adiciona o professor à lista de selecionados
      this.selectedTeachers.push({
        id: teacher.id,
        value: teacher.id,
        fullname: teacher.fullname,
        email: teacher.email,
      });

      // Limpa o campo de busca e esconde o dropdown
      this.teacherSearchTerm = "";
      this.showTeacherDropdown = false;
      this.highlightedIndex = -1;
      this.teacherList = [];

      // Foca novamente no campo de busca para facilitar a adição de mais professores
      this.$nextTick(() => {
        if (this.$refs.teacherSearchInput) {
          this.$refs.teacherSearchInput.focus();
        }
      });
    },

    /**
     * Manipula navegação por teclado no dropdown
     */
    handleKeydown(event) {
      if (!this.showTeacherDropdown || this.teacherList.length === 0) {
        return;
      }

      switch (event.key) {
        case "ArrowDown":
          event.preventDefault();
          this.highlightedIndex = Math.min(
            this.highlightedIndex + 1,
            this.teacherList.length - 1
          );
          break;
        case "ArrowUp":
          event.preventDefault();
          this.highlightedIndex = Math.max(this.highlightedIndex - 1, 0);
          break;
        case "Enter":
          event.preventDefault();
          if (
            this.highlightedIndex >= 0 &&
            this.highlightedIndex < this.teacherList.length
          ) {
            this.selectTeacher(this.teacherList[this.highlightedIndex]);
          }
          break;
        case "Escape":
          event.preventDefault();
          this.showTeacherDropdown = false;
          this.highlightedIndex = -1;
          break;
      }
    },

    /**
     * Manipula cliques fora do dropdown para escondê-lo
     */
    handleClickOutside(event) {
      if (
        this.$refs.teacherSearchContainer &&
        !this.$refs.teacherSearchContainer.contains(event.target)
      ) {
        this.showTeacherDropdown = false;
        this.highlightedIndex = -1;
      }
    },

    /**
     * Atualiza a UI após o carregamento dos dados
     */
    updateUIAfterLoading() {
      this.$nextTick(() => {
        this.updateFormFields();
        this.$forceUpdate();

        if (!this.offerClass.classname || !this.offerClass.enrol) {
          this.showErrorMessage("Dados incompletos após carregamento.");
        }
      });
    },

    /**
     * Atualiza os campos do formulário manualmente
     */
    updateFormFields() {
      this.updateSelectField("enrolSelect", this.offerClass.enrol);
      this.updateInputField("classnameInput", this.offerClass.classname);
      this.updateInputField("startdateInput", this.offerClass.startdate);
    },

    /**
     * Atualiza um campo de seleção
     */
    updateSelectField(ref, value) {
      if (this.$refs[ref]) {
        this.$refs[ref].value = value;
        const event = new Event("change");
        this.$refs[ref].$el.dispatchEvent(event);
      }
    },

    /**
     * Atualiza um campo de input
     */
    updateInputField(ref, value) {
      if (this.$refs[ref] && value) {
        this.$refs[ref].value = value;
        const event = new Event("input");
        this.$refs[ref].$el.dispatchEvent(event);
      }
    },

    /**
     * Volta para tela da oferta
     */
    goBack() {
      if (this.offerCourse.offerid) {
        this.$router.push({
          name: "offer.edit",
          params: { id: this.offerCourse.offerid },
        });
      } else {
        this.router.push({ name: "offer.index" });
      }
    },

    /**
     * Exibe mensagem de erro
     * @param {string} message Mensagem a ser exibida
     */
    showErrorMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "error";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000);
      });
    },

    /**
     * Exibe mensagem de sucesso
     * @param {string} message Mensagem a ser exibida
     */
    showSuccessMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "success";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000);
      });
    },

    /**
     * Exibe mensagem de aviso
     * @param {string} message Mensagem a ser exibida
     */
    showWarningMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "warning";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000);
      });
    },

    /**
     * Método para atualizar manualmente os campos do formulário
     * Este método é chamado após o carregamento dos dados da turma
     */
    updateFormFields() {
      // Atualizar campo de método de inscrição
      if (this.$refs.enrolSelect && this.offerClass.enrol) {
        // Tentar diferentes abordagens para atualizar o campo
        try {
          // Abordagem 1: Atualizar diretamente o valor do componente
          this.$refs.enrolSelect.value = this.offerClass.enrol;

          // Abordagem 2: Disparar evento de mudança
          const changeEvent = new Event("change");
          this.$refs.enrolSelect.$el.dispatchEvent(changeEvent);

          // Abordagem 3: Atualizar o modelo e forçar atualização
          this.$refs.enrolSelect.$emit("input", this.offerClass.enrol);
          this.$refs.enrolSelect.$forceUpdate();
        } catch (error) {
          // Erro ao atualizar campo de método de inscrição
        }
      }

      // Atualizar campo de nome da turma
      if (this.$refs.classnameInput && this.offerClass.classname) {
        try {
          // Atualizar diretamente o valor do componente
          this.$refs.classnameInput.value = this.offerClass.classname;

          // Disparar evento de input
          const inputEvent = new Event("input");
          this.$refs.classnameInput.$el.dispatchEvent(inputEvent);

          // Atualizar o modelo e forçar atualização
          this.$refs.classnameInput.$emit("input", this.offerClass.classname);
          this.$refs.classnameInput.$forceUpdate();
        } catch (error) {
          // Erro ao atualizar campo de nome da turma
        }
      }

      // Atualizar campo de data de início
      if (this.$refs.startdateInput && this.offerClass.startdate) {
        try {
          // Atualizar diretamente o valor do componente
          this.$refs.startdateInput.value = this.offerClass.startdate;

          // Disparar evento de input
          const inputEvent = new Event("input");
          this.$refs.startdateInput.$el.dispatchEvent(inputEvent);

          // Atualizar o modelo e forçar atualização
          this.$refs.startdateInput.$emit("input", this.offerClass.startdate);
          this.$refs.startdateInput.$forceUpdate();
        } catch (error) {
          // Erro ao atualizar campo de data de início
        }
      }

      // Forçar atualização do componente principal
      this.$forceUpdate();
    },

    /**
     * Seleciona todas as situações para rematrícula
     */
    handleSelectAllReenrolSituations() {
      const allSelected = this.reenrolSituationList.every((option) =>
        this.reenrolSituations.some(
          (selected) => selected.value === option.value
        )
      );

      this.reenrolSituations = allSelected
        ? []
        : [...this.reenrolSituationList];

      this.offerClass.optional_fields.reenrolmentsituations =
        this.reenrolSituations.map((item) => item.value);
    },

    /**
     * Método para reiniciar o componente
     * Este método é chamado após o carregamento dos dados da turma
     * para garantir que todos os campos sejam atualizados corretamente
     */
    restartComponent() {
      // Garantir que a página role para o topo
      window.scrollTo(0, 0);

      // Atualizar manualmente os campos do formulário
      this.updateFormFields();

      // Forçar atualização do componente
      this.$forceUpdate();

      // Adicionar um pequeno atraso e forçar nova atualização
      setTimeout(() => {
        this.updateFormFields();
        this.$forceUpdate();

        // Garantir novamente que a página role para o topo após a atualização
        window.scrollTo(0, 0);
      }, 500);
    },
  },
};
</script>

<style lang="scss" scoped>
.new-class {
  margin-bottom: 2rem;
}

.validation-alert {
  background-color: #332701;
  border: 1px solid #997404;
  color: #ffda6a;
  padding: 1rem;
  margin: 0 1rem 1rem 1rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 0.75rem;

  i {
    font-size: 1.25rem;
  }

  span {
    flex: 1;
  }
}

.section-container {
  margin: 0;
  background-color: #212529;
  border-radius: 4px;
}

.section-title {
  color: var(--primary);
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-transform: uppercase;
}

.actions-container {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}
</style>
