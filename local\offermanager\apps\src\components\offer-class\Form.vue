<template>
  <div>
    <!-- Primeira linha: Nome da turma -->
    <div class="row mb-3">
      <div class="col-4">
        <div class="form-group">
          <CustomLabel
            required
            text="Nome da turma"
            help="Insira um nome para a turma. Exemplo: Turma ADM 2025."
          />

          <CustomInput
            v-model="localOfferClass.classname"
            placeholder="Digite o nome da turma"
            required
            ref="classnameInput"
            :has-error="formErrors.classname.hasError"
            :error-message="formErrors.classname.message"
            @validate="isValidField('classname')"
          />
        </div>
      </div>
    </div>

    <!-- Segunda linha: Data início, Data fim, Habilitar data fim -->
    <div class="row mb-3">
      <div class="col-3">
        <div class="form-group">
          <CustomLabel
            required
            text="Data de início"
            help="Insira uma data de início para a turma. Exemplo: 07/04/2025."
          />

          <CustomInput
            v-model="localOfferClass.startdate"
            type="date"
            required
            class="date-input"
            ref="startdateInput"
            :has-error="formErrors.startdate.hasError"
            :error-message="formErrors.startdate.message"
            @validate="isValidField('startdate')"
          />
        </div>
      </div>

      <div class="col-3">
        <div
          class="form-group"
          :class="{ disabled: !localOfferClass.optional_fields.enableenddate }"
        >
          <CustomLabel
            text="Data de término"
            help="Insira uma data de término para a turma, caso haja. Exemplo: 07/12/2025."
          />

          <CustomInput
            v-model="localOfferClass.optional_fields.enddate"
            type="date"
            :disabled="!localOfferClass.optional_fields.enableenddate"
            required
            class="date-input"
            :has-error="formErrors.enddate.hasError"
            :error-message="formErrors.enddate.message"
            @validate="isValidField('enddate')"
          />
        </div>
      </div>

      <div class="col-3 ml-n3">
        <div class="form-group">
          <div class="label-with-help">
            <label class="form-label">&nbsp;</label>
          </div>
          <CustomCheckbox
            v-model="localOfferClass.optional_fields.enableenddate"
            id="enableEndDate"
            label="Habilitar data de termínio"
            class="inline-checkbox"
            :disabled="false"
          />
        </div>
      </div>
    </div>

    <!-- Terceira linha: Pré-inscrição -->
    <div class="form-row mb-3" v-if="localOfferClass.enrol == 'offer_self'">
      <div
        class="form-group"
        :class="{
          disabled: !localOfferClass.optional_fields.enablepreenrolment,
        }"
      >
        <CustomLabel
          text="Data de início da inscrição"
          help="Data de início do período de inscrição."
        />

        <CustomInput
          v-model="localOfferClass.optional_fields.preenrolmentstartdate"
          type="date"
          :width="180"
          :disabled="!localOfferClass.optional_fields.enablepreenrolment"
          class="date-input"
          :has-error="formErrors.preenrolmentstartdate.hasError"
          :error-message="formErrors.preenrolmentstartdate.message"
          @validate="isValidField('preenrolmentstartdate')"
        />
      </div>

      <div
        class="form-group"
        :class="{
          disabled: !localOfferClass.optional_fields.enablepreenrolment,
        }"
        v-if="localOfferClass.enrol == 'offer_self'"
      >
        <CustomLabel required text="Data de término da inscrição" />

        <CustomInput
          v-model="localOfferClass.optional_fields.preenrolmentenddate"
          type="date"
          :width="180"
          :disabled="!localOfferClass.optional_fields.enablepreenrolment"
          class="date-input"
          :has-error="formErrors.preenrolmentenddate.hasError"
          :error-message="formErrors.preenrolmentenddate.message"
          @validate="isValidField('preenrolmentenddate')"
        />
      </div>

      <div class="form-group" v-if="localOfferClass.enrol == 'offer_self'">
        <div class="label-with-help">
          <label class="form-label">&nbsp;</label>
        </div>
        <CustomCheckbox
          v-model="localOfferClass.optional_fields.enablepreenrolment"
          id="enablePreEnrolment"
          label="Habilitar a inscrição"
          :disabled="false"
        />
      </div>
    </div>

    <!-- Quarta linha: Descrição -->
    <div class="form-row mb-3">
      <div class="col-12">
        <div class="form-group">
          <CustomLabel
            text="Descrição da turma"
            help="Esta descrição estará disponível para os usuários na página intermediária do curso. Exemplo: Esta turma se destina a usuários com os cargos administrativos que foram selecionados para a realização dos cursos obrigatórios do ano de 2025"
          />
          <div class="limited-width-editor">
            <TextEditor
              v-model="localOfferClass.optional_fields.description"
              placeholder="Digite a descrição da turma aqui..."
              :rows="5"
              :disabled="false"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Quinta linha: Mínimo/Máximo de usuários e Papel padrão -->
    <div class="row mb-3">
      <div class="col-3" v-if="localOfferClass.enrol == 'offer_self'">
        <div class="form-group">
          <CustomLabel
            text="Número mínimo de inscrições"
            help="Insira um número mínimo de inscrições (vagas) para a turma, se houver. Exemplo: 20."
          />
          <div class="limited-width-input">
            <CustomInput
              v-model="localOfferClass.optional_fields.minusers"
              type="number"
              :width="180"
              :has-error="formErrors.minusers.hasError"
              :error-message="formErrors.minusers.message"
              @validate="isValidField('minusers')"
              :min="0"
            />
          </div>
        </div>
      </div>

      <div class="col-3" v-if="localOfferClass.enrol == 'offer_self'">
        <div class="form-group">
          <CustomLabel
            text="Número máximo de inscrições"
            help="Insira um número máximo de inscrições (vagas) para a turma. Exemplo: 100."
          />
          <div class="limited-width-input">
            <CustomInput
              v-model="localOfferClass.optional_fields.maxusers"
              type="number"
              :width="180"
              :has-error="formErrors.maxusers.hasError"
              :error-message="formErrors.maxusers.message"
              @validate="isValidField('maxusers')"
              :min="0"
            />
          </div>
        </div>
      </div>

      <div class="col-3">
        <div class="form-group">
          <CustomLabel
            required
            text="Perfil atribuído por padrão"
            help="Caso a turma seja criada para perfis de 'Aluno Nissan' e ‘Aluno Concessionária’, o perfil padrão selecionado será ‘Estudante’.<br><br>
                    Caso a turma seja criada para perfis de ‘Gestores’, o perfil padrão selecionado será ‘Gestor’ ou outro perfil pertinente."
          />

          <CustomSelect
            v-model="localOfferClass.optional_fields.roleid"
            :options="roleOptions"
            required
            :has-error="formErrors.roleid.hasError"
            :error-message="formErrors.roleid.message"
            @validate="isValidField('roleid')"
          />
        </div>
      </div>

      <div class="col-3">
        <div class="form-group">
          <CustomLabel
            required
            text="Modalidade da turma"
            help="Selecione a modalidade da turma."
          />

          <CustomSelect
            v-model="localOfferClass.optional_fields.modality"
            :options="modalityOptions"
            required
            :has-error="formErrors.modality.hasError"
            :error-message="formErrors.modality.message"
            @validate="isValidField('roleid')"
          />
        </div>
      </div>
    </div>

    <!-- Sétima linha: Habilitar restrição por estruturas -->
    <div class="form-row mb-n3">
      <div
        class="form-group"
        :class="{
          disabled: !localOfferClass.optional_fields.enablehirearchyrestriction,
          'dependent-field': true,
        }"
      >
        <CustomCheckbox
          v-model="localOfferClass.optional_fields.enablehirearchyrestriction"
          id="enablehirearchyrestriction"
          label="Habilitar restrição por estruturas"
          help="Ao habilitar esta opção, os campos 'Divisão', 'Setor', 'Grupo' e 'Concessionária' serão automaticamente ajustados conforme as seleções realizadas nos respectivos filtros."
        />
      </div>
    </div>

    <!-- Restrição por estruturas -->
    <div class="row mb-3">
      <div class="col-md-4 col-lg-3">
        <div class="form-group">
          <CustomLabel text="Divisão" />
          <Autocomplete
            class="autocomplete-audiences"
            v-model="
              localOfferClass.optional_fields.hirearchyrestrictiondivisions
            "
            :items="divisionsOptions"
            placeholder="Selecionar"
            :required="true"
            :autoOpen="false"
            :show-all-option="true"
            :disabled="
              !localOfferClass.optional_fields.enablehirearchyrestriction
            "
          />
        </div>
      </div>
      <div class="col-md-4 col-lg-3">
        <div class="form-group">
          <CustomLabel text="Setor" />
          <Autocomplete
            class="autocomplete-audiences"
            v-model="
              localOfferClass.optional_fields.hirearchyrestrictionsectors
            "
            :items="sectorsOptions"
            placeholder="Selecionar"
            :required="true"
            :autoOpen="false"
            :show-all-option="true"
            :disabled="
              !localOfferClass.optional_fields.enablehirearchyrestriction ||
              !localOfferClass.optional_fields.hirearchyrestrictiondivisions
                .length
            "
          />
        </div>
      </div>
      <div class="col-md-4 col-lg-3">
        <div class="form-group">
          <CustomLabel text="Grupo" />
          <Autocomplete
            class="autocomplete-audiences"
            v-model="localOfferClass.optional_fields.hirearchyrestrictiongroups"
            :items="groupsOptions"
            placeholder="Selecionar"
            :required="true"
            :autoOpen="false"
            :show-all-option="true"
            :disabled="
              !localOfferClass.optional_fields.enablehirearchyrestriction ||
              !localOfferClass.optional_fields.hirearchyrestrictionsectors
                .length
            "
          />
        </div>
      </div>
      <div class="col-md-4 col-lg-3">
        <div class="form-group">
          <CustomLabel text="Concessionária" />
          <Autocomplete
            class="autocomplete-audiences"
            v-model="
              localOfferClass.optional_fields.hirearchyrestrictiondealerships
            "
            :items="dealershipsOptions"
            placeholder="Selecionar"
            :required="true"
            :autoOpen="false"
            :show-all-option="true"
            :disabled="
              !localOfferClass.optional_fields.enablehirearchyrestriction ||
              !localOfferClass.optional_fields.hirearchyrestrictiongroups.length
            "
          />
        </div>
      </div>
    </div>

    <!-- Número máximo de inscrições por concessionária -->
    <div class="form-row mb-3">
      <div class="col-4">
        <div class="form-group">
          <CustomLabel
            required
            text="Número máximo de inscrições por concessionária"
            help="Insira um número máximo de inscrições por concessionária para a turma, se houver. Exemplo: 5."
          />
          <CustomInput
            v-model="localOfferClass.optional_fields.minusers"
            type="number"
            :has-error="formErrors.minusers.hasError"
            :error-message="formErrors.minusers.message"
            @validate="isValidField('minusers')"
            :min="0"
            :width="180"
          />
        </div>
      </div>
    </div>

    <!-- Sexta linha: Duração da matrícula -->
    <div class="form-row mb-3">
      <div class="col-3">
        <div
          class="form-group"
          :class="{
            disabled: !localOfferClass.optional_fields.enableenrolperiod,
          }"
        >
          <CustomLabel
            required
            id="enrolperiod"
            text="Duração da matrícula"
            :help="
              'Insira um período em dias para a duração da matricula dos alunos, se houver, na turma. Exemplo: 15' +
              (maxEnrolPeriod
                ? ` O valor máximo permitido é de ${maxEnrolPeriod} dias, que corresponde ao período entre as datas de início e fim da turma.`
                : '')
            "
          />
          <CustomInput
            v-model="localOfferClass.optional_fields.enrolperiod"
            type="number"
            id="enrolperiod"
            :disabled="!localOfferClass.optional_fields.enableenrolperiod"
            required
            :has-error="formErrors.enrolperiod.hasError"
            :error-message="formErrors.enrolperiod.message"
            :max="maxEnrolPeriod"
            @validate="isValidField('enrolperiod')"
          />
        </div>
      </div>
      <div class="col-3 ml-n3">
        <div class="form-group">
          <CustomLabel text="&nbsp" />
          <CustomCheckbox
            v-model="localOfferClass.optional_fields.enableenrolperiod"
            id="enableEnrolPeriod"
            label="Habilitar duração da matrícula"
            class="inline-checkbox"
            :disabled="shouldDisableEnrolPeriod"
            v-tooltip="
              shouldDisableEnrolPeriod
                ? 'Não é possível habilitar duração da matrícula para turmas de um dia (data início = data fim)'
                : ''
            "
          />
        </div>
      </div>
    </div>

    <!-- Prorrogação -->
    <!-- Sétima linha: Habilitar prorrogação -->
    <div class="form-row mb-n3">
      <div
        class="form-group"
        :class="{
          disabled: !localOfferClass.optional_fields.enableenrolperiod,
          'dependent-field': true,
        }"
      >
        <CustomCheckbox
          v-model="localOfferClass.optional_fields.enableextension"
          id="enableextension"
          label="Habilitar prorrogação de matrícula"
          :disabled="!localOfferClass.optional_fields.enableenrolperiod"
          help="A prorrogação estende a duração da matrícula do usuário, permitindo sua permanência na turma enquanto ela estiver ativa. No entanto, não redefine seu progresso, garantindo que ele retome o curso de onde parou.<br><br> Nota: A prorrogação só pode ser habilitada quando o Prazo de conclusão da turma estiver habilitado."
          v-tooltip="
            !localOfferClass.optional_fields.enableenrolperiod
              ? 'É necessário habilitar o duração da matrícula primeiro'
              : ''
          "
        />
      </div>
    </div>

    <!-- Oitava linha: Dias de prorrogação -->
    <div class="form-row mb-3">
      <div
        class="form-group"
        :class="{
          disabled:
            !localOfferClass.optional_fields.enableextension ||
            !localOfferClass.optional_fields.enableenrolperiod,
          'dependent-field': true,
        }"
      >
        <CustomLabel
          required
          text="Quantos dias serão acrescentados para prorrogação?"
          help="Insira um período em dias para prorrogar a matricula dos alunos da turma. Exemplo: 3."
        />
        <CustomInput
          v-model="localOfferClass.optional_fields.extensionperiod"
          type="number"
          :width="180"
          :disabled="
            !localOfferClass.optional_fields.enableextension ||
            !localOfferClass.optional_fields.enableenrolperiod
          "
          required
          :has-error="formErrors.extensionperiod.hasError"
          :error-message="formErrors.extensionperiod.message"
          @validate="isValidField('extensionperiod')"
        />
      </div>
    </div>

    <!-- Décima linha: Dias antes do término -->
    <div class="form-row mb-3">
      <div
        class="form-group"
        :class="{
          disabled:
            !localOfferClass.optional_fields.enableextension ||
            !localOfferClass.optional_fields.enableenrolperiod,
          'dependent-field': true,
        }"
      >
        <CustomLabel
          required
          text="Quantos dias antes do término do prazo de matrícula o botão de
            prorrogação deve ser exibido?"
        />
        <div class="limited-width-input">
          <CustomInput
            v-model="localOfferClass.optional_fields.extensiondaysavailable"
            type="number"
            :width="180"
            :disabled="
              !localOfferClass.optional_fields.enableextension ||
              !localOfferClass.optional_fields.enableenrolperiod
            "
            required
            :has-error="formErrors.extensiondaysavailable.hasError"
            :error-message="formErrors.extensiondaysavailable.message"
            @validate="isValidField('extensiondaysavailable')"
          />
        </div>
      </div>
    </div>

    <!-- Décima primeira linha: Máximo de solicitações -->
    <div class="form-row mb-3">
      <div
        class="form-group"
        :class="{
          disabled:
            !localOfferClass.optional_fields.enableextension ||
            !localOfferClass.optional_fields.enableenrolperiod,
          'dependent-field': true,
        }"
      >
        <CustomLabel
          required
          text="Quantas vezes o usuário pode pedir prorrogação?"
        />
        <div class="limited-width-input">
          <CustomInput
            v-model="localOfferClass.optional_fields.extensionmaxrequests"
            type="number"
            :width="180"
            :disabled="
              !localOfferClass.optional_fields.enableextension ||
              !localOfferClass.optional_fields.enableenrolperiod
            "
            required
            :has-error="formErrors.extensionmaxrequests.hasError"
            :error-message="formErrors.extensionmaxrequests.message"
            @validate="isValidField('extensionmaxrequests')"
          />
        </div>
      </div>
    </div>

    <!-- Rematrícula -->
    <div v-if="localOfferClass.enrol == 'offer_self'">
      <!-- Décima segunda linha: Habilitar rematrícula -->
      <div class="form-row mb-3">
        <div class="form-group">
          <CustomLabel
            text="Habilitar rematrícula"
            help="Permite que usuários se matriculem novamente na turma após concluírem ou saírem dela."
          />
          <CustomCheckbox
            v-model="localOfferClass.optional_fields.enablereenrol"
            id="enableReenrol"
            label="Habilitar rematrícula"
            :disabled="false"
          />
        </div>
      </div>

      <!-- Décima terceira linha: Situações permitidas -->
      <div
        class="form-group mb-3"
        :class="{ disabled: !localOfferClass.optional_fields.enablereenrol }"
      >
        <CustomLabel
          text="Quais situações de matrícula permitem rematrícula?"
        />
        <div class="limited-width-select">
          <Autocomplete
            v-model="reenrolSituations"
            :items="reenrolSituationList"
            placeholder="Selecione as situações..."
            :disabled="!localOfferClass.optional_fields.enablereenrol"
            :width="280"
            :show-all-option="true"
            :auto-open="false"
            @select-all="handleSelectAllReenrolSituations"
          />
        </div>
      </div>
    </div>

    <!-- Corpo Docente -->
    <!-- Décima quarta linha: Atribuir corpo docente -->
    <div class="form-group mb-3">
      <CustomLabel
        text="Atribuir Instrutor"
        help="Ao selecionar usuários para a composição do corpo docente, ele será matriculado na turma com o papel “Professor”."
      />
      <div class="limited-width-select">
        <div class="position-relative" ref="teacherSearchContainer">
          <div class="input-wrapper with-icon">
            <input
              type="text"
              v-model="teacherSearchTerm"
              placeholder="Pesquisar ..."
              class="form-control custom-input"
              @input="handleTeacherInput"
              @focus="handleTeacherInputFocus"
              @keydown="handleKeydown"
              ref="teacherSearchInput"
            />
          </div>
          <!-- Dropdown de professores -->
          <div
            v-if="showTeacherDropdown && teacherList.length > 0"
            class="dropdown-menu show position-absolute w-100 border rounded-bottom shadow-sm"
            style="
              top: 100%;
              left: 0;
              right: 0;
              max-height: 200px;
              overflow-y: auto;
              z-index: 1000;
              border-top: none;
              cursor: pointer;
              border-radius: 0 0 0.375rem 0.375rem;
            "
            ref="teacherDropdown"
          >
            <div
              v-for="(teacher, index) in teacherList"
              :key="teacher.id"
              class="dropdown-item"
              :class="{ active: highlightedIndex === index }"
              @click="selectTeacher(teacher)"
              @mouseenter="highlightedIndex = index"
            >
              <div>
                <div>{{ teacher.fullname }}</div>
                <div class="text-muted small" v-if="teacher.email">
                  {{ teacher.email }}
                </div>
              </div>
            </div>
          </div>
          <!-- Mensagem quando não há resultados -->
          <div
            v-if="
              showTeacherDropdown &&
              teacherSearchTerm.length >= 3 &&
              teacherList.length === 0
            "
            class="dropdown-menu show position-absolute w-100 border rounded-bottom shadow-sm"
            style="
              top: 100%;
              left: 0;
              right: 0;
              z-index: 1000;
              border-top: none;
              border-radius: 0 0 0.375rem 0.375rem;
            "
          >
            <div class="dropdown-item-text text-center fst-italic">
              Nenhum professor encontrado
            </div>
          </div>
        </div>
        <div class="my-4">
          <a
            v-for="teacher in selectedTeachers"
            :key="teacher.id"
            class="tag badge bg-primary text-white p-2 cursor-pointer mr-2"
            @click="removeTeacher(teacher.id)"
            style="color: white !important; cursor: pointer"
          >
            <i class="fas fa-times mr-1"></i>
            {{ teacher.fullname }}
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getSituationList,
  getCourseRoles,
  getHierarchyDivisions,
  getHierarchySectors,
  getHierarchyGroups,
  getHierarchyDealerships,
} from "@/services/offer";
import { getPotentialTeachers } from "@/services/offer";
import CustomLabel from "@/components/CustomLabel.vue";
import CustomInput from "@/components/CustomInput.vue";
import CustomSelect from "@/components/CustomSelect.vue";
import CustomButton from "@/components/CustomButton.vue";
import PageHeader from "@/components/PageHeader.vue";
import BackButton from "@/components/BackButton.vue";
import Autocomplete from "@/components/Autocomplete.vue";
import TextEditor from "@/components/TextEditor.vue";
import CustomCheckbox from "@/components/CustomCheckbox.vue";
import FilterRow from "@/components/FilterRow.vue";
import FilterGroup from "@/components/FilterGroup.vue";
import Toast from "@/components/Toast.vue";
import HelpIcon from "@/components/HelpIcon.vue";
import FilterTag from "@/components/FilterTag.vue";
import FilterTags from "@/components/FilterTags.vue";
import { isEqual } from "lodash";

export default {
  name: "Form",

  components: {
    CustomLabel,
    CustomInput,
    CustomSelect,
    CustomButton,
    PageHeader,
    BackButton,
    Autocomplete,
    TextEditor,
    CustomCheckbox,
    FilterRow,
    FilterGroup,
    Toast,
    HelpIcon,
    FilterTag,
    FilterTags,
  },

  props: {
    offerCourse: {
      type: Object,
      required: true,
    },

    offerClass: {
      type: Object,
      required: true,
    },
    isEditing: {
      type: Boolean,
      required: true,
    },
  },

  emits: ["update:offerClass"],

  data() {
    return {
      loading: false,
      showToast: false,
      toastMessage: "",
      toastType: "success",
      toastTimeout: null,

      // Dados da turma
      localOfferClass: {},

      divisionsOptions: [],
      sectorsOptions: [],
      groupsOptions: [],
      dealershipsOptions: [],

      // Professores selecionados (para o Autocomplete)
      selectedTeachers: [],
      teacherSearchTerm: "",
      debounceTimer: null,
      teacherList: [],

      // Controle do dropdown de professores
      showTeacherDropdown: false,
      highlightedIndex: -1,

      // Situações selecionadas para prorrogação e rematrícula
      extensionSituations: [],
      reenrolSituations: [],

      roleOptions: [],
      modalityOptions: [],
      situationOptions: [],

      // Controle de validação de campos
      formErrors: {
        enrol: {
          hasError: false,
          message: "Método de inscrição é obrigatório",
        },
        classname: { hasError: false, message: "Nome da turma é obrigatório" },
        startdate: { hasError: false, message: "Data de início é obrigatória" },
        roleid: { hasError: false, message: "Perfil padrão é obrigatório" },
        modality: { hasError: false, message: "Modalidade é obrigatório" },
        enddate: {
          hasError: false,
          message: "Data fim da turma é obrigatória quando habilitada",
        },
        preenrolmentstartdate: {
          hasError: false,
          message:
            "Data início de pré-inscrição é obrigatória quando habilitada",
        },
        preenrolmentenddate: {
          hasError: false,
          message: "Data fim de pré-inscrição é obrigatória quando habilitada",
        },
        enrolperiod: {
          hasError: false,
          message:
            "Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma",
        },
        extensionperiod: {
          hasError: false,
          message: "Dias para prorrogação é obrigatório quando habilitada",
        },
        extensiondaysavailable: {
          hasError: false,
          message: "Dias antes do término é obrigatório quando habilitada",
        },
        extensionmaxrequests: {
          hasError: false,
          message: "Máximo de solicitações é obrigatório quando habilitada",
        },
        extensionsituations: {
          hasError: false,
          message:
            "É necessário selecionar pelo menos uma situação de matrícula para prorrogação",
        },
        minusers: {
          hasError: false,
          message: "Mínimo de usuários deve ser maior ou igual a zero",
        },
        maxusers: {
          hasError: false,
          message: "Máximo de usuários deve ser maior ou igual a zero",
        },
      },

      // Alerta de validação
      validationAlert: {
        show: false,
        message:
          "Há campos obrigatórios a serem preenchidos. Por favor, verifique os campos destacados.",
      },
    };
  },

  async created() {
    await this.getInitialData();
  },

  mounted() {
    // Garantir que a página role para o topo quando for carregada
    window.scrollTo(0, 0);

    // Adicionar listener para cliques fora do dropdown de professores
    document.addEventListener("click", this.handleClickOutside);
  },

  beforeUnmount() {
    // Remover listener para cliques fora do dropdown
    document.removeEventListener("click", this.handleClickOutside);

    // Limpar timer de debounce se existir
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
  },

  directives: {
    tooltip: {
      mounted(el, binding) {
        el.setAttribute("title", binding.value);
      },
      updated(el, binding) {
        el.setAttribute("title", binding.value);
      },
    },
  },

  computed: {
    extensionSituationList() {
      let allowedSituations = [0, 1];
      return this.situationOptions.filter((situation) =>
        allowedSituations.includes(situation.value)
      );
    },

    reenrolSituationList() {
      let allowedSituations = [6, 7, 8, 4, 5];
      return this.situationOptions.filter((situation) =>
        allowedSituations.includes(situation.value)
      );
    },

    // Calcula o valor máximo permitido para o prazo de conclusão da turma
    maxEnrolPeriod() {
      // Só calcula se ambas as datas estiverem preenchidas e a data fim estiver habilitada
      if (
        this.localOfferClass.startdate &&
        this.localOfferClass.optional_fields.enddate &&
        this.localOfferClass.optional_fields.enableenddate
      ) {
        // Calcula a diferença em dias entre as datas
        const daysDifference = this.calculateDaysDifference(
          this.localOfferClass.startdate,
          this.localOfferClass.optional_fields.enddate
        );

        // Retorna a diferença em dias como valor máximo (mínimo 1 dia)
        return daysDifference >= 1 ? daysDifference : 1;
      }

      // Se alguma das condições não for atendida, retorna null (sem limite)
      return null;
    },

    // Verifica se as datas de início e fim são iguais (turma de um dia)
    isOneDayClass() {
      // Só considera turma de um dia se:
      // 1. Data início estiver preenchida
      // 2. Data fim estiver habilitada E preenchida
      // 3. As duas datas forem iguais
      return (
        this.localOfferClass.startdate &&
        this.localOfferClass.optional_fields.enableenddate &&
        this.localOfferClass.optional_fields.enddate &&
        this.localOfferClass.startdate ===
          this.localOfferClass.optional_fields.enddate
      );
    },

    // Verifica se o prazo de conclusão deve estar desabilitado
    shouldDisableEnrolPeriod() {
      // Só desabilita se for realmente uma turma de um dia
      // (ambas as datas preenchidas e iguais)
      return this.isOneDayClass;
    },
  },

  watch: {
    offerClass: {
      handler(newVal) {
        if (!isEqual(newVal, this.localOfferClass)) {
          this.localOfferClass = {
            ...newVal,
          };
        }
      },
      deep: true,
      immediate: true,
    },

    localOfferClass: {
      handler(newVal) {
        if (!isEqual(newVal, this.offerClass)) {
          this.$emit("update:offerClass", newVal);
        }
      },
      deep: true,
    },

    "localOfferClass.optional_fields.enablehirearchyrestriction": function (
      newValue,
      oldValue
    ) {
      if (isEqual(newValue, oldValue)) {
        return;
      }

      if (!newValue) {
        this.localOfferClass.optional_fields = {
          ...this.localOfferClass.optional_fields,
          hirearchyrestrictiondivisions: [],
          hirearchyrestrictionsectors: [],
          hirearchyrestrictiongroups: [],
          hirearchyrestrictiondealerships: [],
        };
      }
    },

    "localOfferClass.optional_fields.hirearchyrestrictiondivisions": function (
      newValue,
      oldValue
    ) {
      if (isEqual(newValue, oldValue)) {
        return;
      }

      if (!newValue.length) {
        this.localOfferClass.optional_fields.hirearchyrestrictionsectors = [];
        this.localOfferClass.optional_fields.hirearchyrestrictiongroups = [];
        this.localOfferClass.optional_fields.hirearchyrestrictiondealerships =
          [];
        return;
      }

      const divisionids = newValue.map((item) => item.value);

      this.getSectors(divisionids);
    },

    "localOfferClass.optional_fields.hirearchyrestrictionsectors": function (
      newValue,
      oldValue
    ) {
      if (isEqual(newValue, oldValue)) {
        return;
      }

      if (!newValue.length) {
        this.localOfferClass.optional_fields.hirearchyrestrictiongroups = [];
        this.localOfferClass.optional_fields.hirearchyrestrictiondealerships =
          [];
        return;
      }

      const sectorids = newValue.map((item) => item.value);

      this.getGroups(sectorids);
    },

    "localOfferClass.optional_fields.hirearchyrestrictiongroups": function (
      newValue,
      oldValue
    ) {
      if (isEqual(newValue, oldValue)) {
        return;
      }

      if (!newValue.length) {
        this.localOfferClass.optional_fields.hirearchyrestrictiondealerships =
          [];
        return;
      }

      const groupids = newValue.map((item) => item.value);

      this.getDealerships(groupids);
    },

    // Observa mudanças nas situações selecionadas para rematrícula
    reenrolSituations: {
      handler(newValue) {
        this.localOfferClass.optional_fields.reenrolmentsituations =
          newValue.map((item) => item.value);
      },
      deep: true,
    },

    // Observa mudanças no estado de habilitação do prazo de conclusão
    "localOfferClass.optional_fields.enableenrolperiod": function (newValue) {
      if (!newValue && this.localOfferClass.optional_fields.enableextension) {
        this.localOfferClass.optional_fields.enableextension = false;

        this.showWarningMessage(
          "Prorrogação de matrícula foi desabilitada automaticamente porque o Prazo de conclusão da turma foi desabilitado."
        );
      }

      this.isValidField("extensionsituations");
    },

    // Observa mudanças na data de início da turma
    "localOfferClass.startdate": function () {
      // Valida a data fim quando a data início mudar (para verificar se fim >= início)
      if (
        this.localOfferClass.optional_fields.enableenddate &&
        this.localOfferClass.optional_fields.enddate
      ) {
        this.isValidField("enddate");
      }

      // Se o prazo de conclusão estiver habilitado e tiver um valor, valida-o
      if (
        this.localOfferClass.optional_fields.enableenrolperiod &&
        this.localOfferClass.optional_fields.enrolperiod
      ) {
        this.isValidField("enrolperiod");
      }

      // Valida pré-inscrição se estiver habilitada
      if (this.localOfferClass.optional_fields.enablepreenrolment) {
        this.validatePreenrolmentDates();
      }

      // Verifica se deve desabilitar o prazo de conclusão (turma de um dia)
      this.checkAndDisableEnrolPeriodForOneDayClass();
    },

    // Observa mudanças na data fim da turma
    "localOfferClass.optional_fields.enddate": function () {
      // Valida a data fim quando ela mudar
      if (this.localOfferClass.optional_fields.enableenddate) {
        this.isValidField("enddate");
      }

      // Se o prazo de conclusão estiver habilitado e tiver um valor, valida-o
      if (
        this.localOfferClass.optional_fields.enableenrolperiod &&
        this.localOfferClass.optional_fields.enrolperiod
      ) {
        this.isValidField("enrolperiod");
      }

      // Valida pré-inscrição se estiver habilitada
      if (
        this.localOfferClass.optional_fields.enablepreenrolment &&
        this.localOfferClass.optional_fields.enableenddate
      ) {
        this.validatePreenrolmentDates();
      }

      // Verifica se deve desabilitar o prazo de conclusão (turma de um dia)
      this.checkAndDisableEnrolPeriodForOneDayClass();
    },

    // Observa mudanças na habilitação da data fim da turma
    "localOfferClass.optional_fields.enableenddate": function (newValue) {
      // Se a data fim for habilitada, valida-a imediatamente
      if (newValue && this.localOfferClass.optional_fields.enddate) {
        this.isValidField("enddate");
      }

      // Se a data fim for desabilitada e o prazo de conclusão estiver habilitado, valida-o
      if (
        !newValue &&
        this.localOfferClass.optional_fields.enableenrolperiod &&
        this.localOfferClass.optional_fields.enrolperiod
      ) {
        this.isValidField("enrolperiod");
      }

      // Valida pré-inscrição se estiver habilitada
      if (this.localOfferClass.optional_fields.enablepreenrolment) {
        this.validatePreenrolmentDates();
      }

      // Verifica se deve desabilitar o prazo de conclusão (turma de um dia)
      this.checkAndDisableEnrolPeriodForOneDayClass();
    },

    // Observa mudanças no valor do prazo de conclusão
    "localOfferClass.optional_fields.enrolperiod": function (newValue) {
      // Se o prazo de conclusão estiver habilitado, valida o campo
      if (this.localOfferClass.optional_fields.enableenrolperiod && newValue) {
        this.isValidField("enrolperiod");
      }
    },

    "localOfferClass.optional_fields.preenrolmentstartdate": function () {
      if (this.localOfferClass.optional_fields.enablepreenrolment) {
        this.validatePreenrolmentDates();
      }
    },
    "localOfferClass.optional_fields.preenrolmentenddate": function () {
      if (this.localOfferClass.optional_fields.enablepreenrolment) {
        this.validatePreenrolmentDates();
      }
    },

    // Observa mudanças no mínimo de usuários
    "localOfferClass.optional_fields.minusers": function () {
      this.isValidField("minusers");
      // Também valida maxusers para verificar a relação entre eles
      if (
        this.localOfferClass.optional_fields.maxusers !== null &&
        this.localOfferClass.optional_fields.maxusers !== undefined
      ) {
        this.isValidField("maxusers");
      }
    },

    // Observa mudanças no máximo de usuários
    "localOfferClass.optional_fields.maxusers": function () {
      this.isValidField("maxusers");
      // Também valida minusers para verificar a relação entre eles
      if (
        this.localOfferClass.optional_fields.minusers !== null &&
        this.localOfferClass.optional_fields.minusers !== undefined
      ) {
        this.isValidField("minusers");
      }
    },
  },

  methods: {
    /**
     * Carrega todos os dados iniciais necessários
     */
    async getInitialData() {
      try {
        this.loading = true;

        await this.setEnrolmentMethod();

        await this.getRoles();

        await this.getModalities();

        await this.getSituations();

        await this.getHierarchyRestrictionData();
      } catch (error) {
        this.showErrorMessage("Alguns dados não puderam ser carregados.");
      } finally {
        this.loading = false;
      }
    },

    /**
     * Carrega papéis disponíveis
     */
    async getRoles() {
      const response = await getCourseRoles(this.offerCourse.id);

      this.roleOptions = response
        .map((role) => ({
          value: role.id,
          label: role.name,
        }))
        .sort((a, b) => a.label.localeCompare(b.label));

      if (!this.localOfferClass.optional_fields.roleid) {
        const studentRole = this.roleOptions.find((role) => role.value === 5);

        this.localOfferClass.optional_fields.roleid =
          studentRole?.value ?? this.roleOptions[0].value;
      }
    },

    /**
     * Loads available modalities for the class and sets the default modality.
     */
    async getModalities() {
      this.modalityOptions = [
        { value: "presencial", label: "Presencial" },
        { value: "web", label: "WEB" },
        { value: "virtual", label: "Virtual" },
        { value: "blended", label: "Blended" },
      ];

      if (!this.localOfferClass.optional_fields.modality) {
        this.localOfferClass.optional_fields.modality =
          this.modalityOptions[0].value;
      }
    },

    /**
     * Carrega situações de matrícula
     */
    async getSituations() {
      const response = await getSituationList();

      this.situationOptions = response.map((situation) => ({
        value: situation.id,
        label: situation.name,
      }));
    },

    /**
     * Sets the enrolment method for the class on create
     */
    async setEnrolmentMethod() {
      try {
        if (this.isEditing) {
          return;
        }

        const enrol = this.route.query.enrol_type;

        if (!enrol) throw new Error("Método de inscrição não informado");

        this.localOfferClass.enrol = enrolTypeFromUrl;
      } catch (error) {
        console.log(error);
      }
    },

    async getHierarchyRestrictionData() {
      await this.getDivisions();

      if (!this.localOfferClass.optional_fields.enablehirearchyrestriction) {
        return;
      }
      console.log(
        this.localOfferClass.optional_fields.hirearchyrestrictiondivisions
      );
      if (
        this.localOfferClass.optional_fields.hirearchyrestrictiondivisions
          .length
      ) {
        await this.getSectors();
      }

      if (
        this.localOfferClass.optional_fields.hirearchyrestrictionsectors.length
      ) {
        await this.getGroups();
      }

      if (
        this.localOfferClass.optional_fields.hirearchyrestrictiongroups.length
      ) {
        await this.getDealerships();
      }
    },

    /**
     * Loads available divisions
     *
     * @returns {Promise<void>}
     */
    async getDivisions() {
      try {
        const response = await getHierarchyDivisions();

        this.divisionsOptions = response.map((division) => ({
          value: division.id,
          label: division.name,
        }));
      } catch (error) {
        //
        console.log(error);
      }
    },

    /**
     * Loads available sectors
     * @param {Array<number>} divisionIds
     * @returns {Promise<void>}
     */
    async getSectors(divisionIds) {
      try {
        console.log(divisionIds);
        if (!divisionIds) {
          divisionIds =
            this.localOfferClass.optional_fields.hirearchyrestrictiondivisions.map(
              (item) => item.value
            );

          console.log(divisionIds);
        }

        const response = await getHierarchySectors(divisionIds);

        this.sectorsOptions = response.map((sector) => ({
          value: sector.id,
          label: sector.name,
        }));
      } catch (error) {
        //
        console.log(error);
      }
    },

    /**
     * Loads available groups
     * @param {Array<number>} sectorIds
     * @returns {Promise<void>}
     */

    async getGroups(sectorIds) {
      try {
        if (!sectorIds) {
          sectorIds =
            this.localOfferClass.optional_fields.hirearchyrestrictionsectors.map(
              (item) => item.value
            );
        }

        const response = await getHierarchyGroups(sectorIds);

        this.groupsOptions = response.map((group) => ({
          value: group.id,
          label: group.name,
        }));
      } catch (error) {
        //
        console.log(error);
      }
    },

    /**
     * Loads available dealerships
     * @param {Array<number>} groupIds
     * @returns {Promise<void>}
     */
    async getDealerships(groupIds) {
      try {
        if (!groupIds) {
          groupIds =
            this.localOfferClass.optional_fields.hirearchyrestrictiongroups.map(
              (item) => item.value
            );
        }

        const response = await getHierarchyDealerships(groupIds);

        this.dealershipsOptions = response.map((dealership) => ({
          value: dealership.id,
          label: dealership.name,
        }));
      } catch (error) {
        //
        console.log(error);
      }
    },

    /**
     * Validates all form fields and emits a validation event.
     * @returns {boolean} If the form is valid.
     */
    isValidForm() {
      let isValid = true;

      Object.keys(this.formErrors).forEach((field) => {
        if (!this.isValidField(field)) {
          isValid = false;
        }
      });

      this.$emit("validate", isValid);

      return isValid;
    },

    /**
     * Valida um campo específico
     * @param {string} field Nome do campo a ser validado
     */
    isValidField(field) {
      switch (field) {
        case "enrol":
          // O campo enrol não é mais validado, pois é definido no modal e não é editável
          this.formErrors.enrol.hasError = false;
          break;
        case "classname":
          this.formErrors.classname.hasError = !this.localOfferClass.classname;
          break;
        case "startdate":
          // Verifica se o campo está preenchido
          const startdateHasValue = this.localOfferClass.startdate;

          // Verifica se a data início é maior que a data fim (quando data fim estiver habilitada)
          const startdateAfterEnddate =
            startdateHasValue &&
            this.localOfferClass.optional_fields.enableenddate &&
            this.localOfferClass.optional_fields.enddate &&
            new Date(this.localOfferClass.startdate) >
              new Date(this.localOfferClass.optional_fields.enddate);

          if (!startdateHasValue) {
            this.formErrors.startdate.message = "Data de início é obrigatória";
            this.formErrors.startdate.hasError = true;
          } else if (startdateAfterEnddate) {
            this.formErrors.startdate.message =
              "Data de início deve ser igual ou anterior à data fim da turma";
            this.formErrors.startdate.hasError = true;
          } else {
            this.formErrors.startdate.message = "Data de início é obrigatória";
            this.formErrors.startdate.hasError = false;
          }
          break;
        case "roleid":
          this.formErrors.roleid.hasError =
            !this.localOfferClass.optional_fields.roleid;
          break;
        case "enddate":
          // Verifica se o campo está habilitado e se tem valor
          const enddateEnabled =
            this.localOfferClass.optional_fields.enableenddate;
          const enddateHasValue = this.localOfferClass.optional_fields.enddate;

          // Verifica se a data fim é menor que a data início
          const enddateBeforeStartdate =
            enddateEnabled &&
            enddateHasValue &&
            this.localOfferClass.startdate &&
            new Date(this.localOfferClass.optional_fields.enddate) <
              new Date(this.localOfferClass.startdate);

          if (enddateEnabled && !enddateHasValue) {
            this.formErrors.enddate.message =
              "Data fim da turma é obrigatória quando habilitada";
            this.formErrors.enddate.hasError = true;
          } else if (enddateBeforeStartdate) {
            this.formErrors.enddate.message =
              "Data fim da turma deve ser igual ou posterior à data de início";
            this.formErrors.enddate.hasError = true;
          } else {
            this.formErrors.enddate.message =
              "Data fim da turma é obrigatória quando habilitada";
            this.formErrors.enddate.hasError = false;
          }
          break;
        case "preenrolmentstartdate":
          this.formErrors.preenrolmentstartdate.hasError =
            this.localOfferClass.optional_fields.enablepreenrolment &&
            !this.localOfferClass.optional_fields.preenrolmentstartdate;
          this.validatePreenrolmentDates();
          break;
        case "preenrolmentenddate":
          this.formErrors.preenrolmentenddate.hasError =
            this.localOfferClass.optional_fields.enablepreenrolment &&
            !this.localOfferClass.optional_fields.preenrolmentenddate;
          this.validatePreenrolmentDates();
          break;
        case "enrolperiod":
          // Verifica se o campo está habilitado e se tem valor
          const isEnabled =
            this.localOfferClass.optional_fields.enableenrolperiod;
          const hasValue =
            this.localOfferClass.optional_fields.enrolperiod !== null &&
            this.localOfferClass.optional_fields.enrolperiod !== undefined &&
            this.localOfferClass.optional_fields.enrolperiod !== "";

          // Verifica se o valor excede o máximo permitido
          const exceedsMaximum =
            this.maxEnrolPeriod !== null &&
            hasValue &&
            parseInt(this.localOfferClass.optional_fields.enrolperiod) >
              this.maxEnrolPeriod;

          // Define a mensagem de erro apropriada
          if (isEnabled && !hasValue) {
            this.formErrors.enrolperiod.message =
              "Prazo de conclusão é obrigatório quando habilitado";
            this.formErrors.enrolperiod.hasError = true;
          } else if (isEnabled && exceedsMaximum) {
            this.formErrors.enrolperiod.message = `Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`;
            this.formErrors.enrolperiod.hasError = true;
          } else {
            this.formErrors.enrolperiod.message =
              "Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma";
            this.formErrors.enrolperiod.hasError = false;
          }
          break;
        case "extensionperiod":
          this.formErrors.extensionperiod.hasError =
            this.localOfferClass.optional_fields.enableextension &&
            this.localOfferClass.optional_fields.enableenrolperiod &&
            !this.localOfferClass.optional_fields.extensionperiod;
          break;
        case "extensiondaysavailable":
          this.formErrors.extensiondaysavailable.hasError =
            this.localOfferClass.optional_fields.enableextension &&
            this.localOfferClass.optional_fields.enableenrolperiod &&
            !this.localOfferClass.optional_fields.extensiondaysavailable;
          break;
        case "extensionmaxrequests":
          this.formErrors.extensionmaxrequests.hasError =
            this.localOfferClass.optional_fields.enableextension &&
            this.localOfferClass.optional_fields.enableenrolperiod &&
            !this.localOfferClass.optional_fields.extensionmaxrequests;
          break;
        case "extensionsituations":
          this.formErrors.extensionsituations.hasError =
            this.localOfferClass.optional_fields.enableextension &&
            this.localOfferClass.optional_fields.enableenrolperiod &&
            (!this.extensionSituations ||
              this.extensionSituations.length === 0);
          break;
        case "minusers":
          this.validateMinUsers();
          break;
        case "maxusers":
          this.validateMaxUsers();
          break;
      }

      return !this.formErrors[field].hasError;
    },

    mapToOptions(array) {
      return array.map((item) => ({ value: item, label: "" }));
    },

    mapToValues(array) {
      return array.map((item) => item.value);
    },

    /**
     * Calcula a diferença em dias entre duas datas
     * @param {string} startDate - Data de início no formato YYYY-MM-DD
     * @param {string} endDate - Data de fim no formato YYYY-MM-DD
     * @returns {number} - Diferença em dias entre as datas
     */
    calculateDaysDifference(startDate, endDate) {
      // Verifica se as datas são válidas
      if (!startDate || !endDate) {
        return 0;
      }

      // Converte as strings de data para objetos Date
      const start = new Date(startDate);
      const end = new Date(endDate);

      // Verifica se as datas são válidas
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return 0;
      }

      // Se as datas são iguais, retorna 1 (um dia de prazo)
      if (start.getTime() === end.getTime()) {
        return 1;
      }

      // Calcula a diferença em milissegundos
      const diffTime = Math.abs(end - start);

      // Converte para dias e arredonda para cima
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      return diffDays;
    },

    /**
     * Verifica se deve desabilitar o prazo de conclusão para turmas de um dia
     */
    checkAndDisableEnrolPeriodForOneDayClass() {
      // Se as datas são iguais (turma de um dia), desabilita o prazo de conclusão
      if (
        this.isOneDayClass &&
        this.localOfferClass.optional_fields.enableenrolperiod
      ) {
        this.localOfferClass.optional_fields.enableenrolperiod = false;
        this.localOfferClass.optional_fields.enrolperiod = null;

        // Também desabilita a prorrogação se estiver habilitada
        if (this.localOfferClass.optional_fields.enableextension) {
          this.localOfferClass.optional_fields.enableextension = false;
          this.localOfferClass.optional_fields.extensionperiod = null;
          this.localOfferClass.optional_fields.extensiondaysavailable = null;
          this.localOfferClass.optional_fields.extensionmaxrequests = null;
          this.extensionSituations = [];
        }

        // Exibe mensagem informativa
        this.showWarningMessage(
          "Prazo de conclusão foi desabilitado automaticamente porque a turma tem duração de apenas um dia (data início = data fim)."
        );
      }
    },

    validatePreenrolmentDates() {
      let isValid = true;

      // Resetar erros
      this.formErrors.preenrolmentstartdate.hasError = false;
      this.formErrors.preenrolmentenddate.hasError = false;

      // Só validar se pré-inscrição estiver habilitada
      if (this.localOfferClass.optional_fields.enablepreenrolment) {
        const startDate = this.localOfferClass.startdate;
        const endDate = this.localOfferClass.optional_fields.enableenddate
          ? this.localOfferClass.optional_fields.enddate
          : null;
        const preStart =
          this.localOfferClass.optional_fields.preenrolmentstartdate;
        const preEnd = this.localOfferClass.optional_fields.preenrolmentenddate;

        const courseStartDate = this.offerCourse.startdate;
        const courseEndDate = this.offerCourse.enddate;

        // Validar se as datas foram preenchidas
        if (!preStart) {
          this.formErrors.preenrolmentstartdate.hasError = true;
          this.formErrors.preenrolmentstartdate.message =
            "Data início de pré-inscrição é obrigatória";
          isValid = false;
        }

        if (!preEnd) {
          this.formErrors.preenrolmentenddate.hasError = true;
          this.formErrors.preenrolmentenddate.message =
            "Data fim de pré-inscrição é obrigatória";
          isValid = false;
        }

        // Validar que preenrolmentenddate > preenrolmentstartdate
        if (new Date(preEnd) < new Date(preStart)) {
          this.formErrors.preenrolmentenddate.hasError = true;
          this.formErrors.preenrolmentenddate.message =
            "Data fim deve ser posterior à data início";
          isValid = false;
        }

        // Validar que preenrolmentstartdate > startdate
        if (new Date(preStart) > new Date(startDate)) {
          this.formErrors.preenrolmentstartdate.hasError = true;
          this.formErrors.preenrolmentstartdate.message =
            "Data início deve ser igual ou anterior à data início da turma";
          isValid = false;
        }

        // Validar que preenrolmentstartdate < courseStartdate
        if (new Date(preStart) < new Date(courseStartDate)) {
          this.formErrors.preenrolmentstartdate.hasError = true;
          this.formErrors.preenrolmentstartdate.message =
            "Data início deve ser igual ou posterior à data início do curso";
          isValid = false;
        }

        // Validar que preenrolmentstartdate > courseEnddate
        if (courseEndDate && new Date(preStart) > new Date(courseEndDate)) {
          this.formErrors.preenrolmentstartdate.hasError = true;
          this.formErrors.preenrolmentstartdate.message =
            "Data de início deve ser igual ou anterior à data fim do curso";
          isValid = false;
        }
        // Se enddate estiver habilitada, validar que preenrolmentenddate <= enddate
        if (endDate && new Date(preEnd) >= new Date(endDate)) {
          this.formErrors.preenrolmentenddate.hasError = true;
          this.formErrors.preenrolmentenddate.message =
            "Data fim deve ser anterior à data fim da turma";
          isValid = false;
        }

        // Validar que preenrolmentenddate > courseEnddate
        if (courseEndDate && new Date(preEnd) > new Date(courseEndDate)) {
          this.formErrors.preenrolmentenddate.hasError = true;
          this.formErrors.preenrolmentenddate.message =
            "Data fim deve ser igual ou anterior à data fim do curso";
          isValid = false;
        }
      }
      return isValid;
    },

    /**
     * Valida o campo mínimo de usuários
     */
    validateMinUsers() {
      const minUsers = this.localOfferClass.optional_fields.minusers;
      const maxUsers = this.localOfferClass.optional_fields.maxusers;
      const minUsersNum = parseInt(minUsers);
      const maxUsersNum = parseInt(maxUsers);

      this.formErrors.minusers.hasError = false;

      if (minUsersNum === 0) {
        return true;
      }

      if (maxUsersNum > 0) {
        if (minUsersNum > maxUsersNum) {
          this.formErrors.minusers.message =
            "Mínimo de usuários inscritos deve ser menor que o máximo de usuários inscritos";
          this.formErrors.minusers.hasError = true;
          return false;
        }
      }

      return true;
    },

    /**
     * Valida o campo máximo de usuários
     */
    validateMaxUsers() {
      const minUsers = this.localOfferClass.optional_fields.minusers;
      const maxUsers = this.localOfferClass.optional_fields.maxusers;
      const maxUsersNum = parseInt(maxUsers);
      const minUsersNum = parseInt(minUsers);

      this.formErrors.maxusers.hasError = false;

      if (maxUsersNum === 0) {
        return true;
      }

      if (minUsersNum > 0) {
        if (maxUsersNum < minUsersNum) {
          this.formErrors.maxusers.message =
            "Máximo de usuários inscritos deve ser maior que o mínimo de usuários inscritos";
          this.formErrors.maxusers.hasError = true;
          return false;
        }
      }

      return true;
    },

    handleTeacherInput() {
      const term = this.teacherSearchTerm.trim();

      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      if (term.length >= 3) {
        this.showTeacherDropdown = true;
        this.highlightedIndex = -1;
        this.debounceTimer = setTimeout(async () => {
          await this.fetchPotentialTeachers(term);
        }, 500);
      } else {
        this.showTeacherDropdown = false;
        this.teacherList = [];
      }
    },

    /**
     * Recupera lista de usuários potenciais para adicionar como professor
     */
    async fetchPotentialTeachers(searchString) {
      let selectedTeacherIds =
        this.selectedTeachers.map((teacher) => teacher.id || teacher.value) ??
        [];

      this.teacherList = await getPotentialTeachers(
        this.offerCourse.id,
        this.classId,
        searchString,
        selectedTeacherIds
      );
    },

    /**
     * Remove usuário da lista de professor
     */
    removeTeacher(teacherId) {
      this.selectedTeachers = this.selectedTeachers.filter(
        (teacher) => teacher.id !== teacherId
      );
    },

    /**
     * Manipula o foco no campo de busca de professores
     */
    handleTeacherInputFocus() {
      if (this.teacherSearchTerm.length >= 3 && this.teacherList.length > 0) {
        this.showTeacherDropdown = true;
      }
    },

    /**
     * Seleciona um professor do dropdown
     */
    selectTeacher(teacher) {
      // Adiciona o professor à lista de selecionados
      this.selectedTeachers.push({
        id: teacher.id,
        value: teacher.id,
        fullname: teacher.fullname,
        email: teacher.email,
      });

      // Limpa o campo de busca e esconde o dropdown
      this.teacherSearchTerm = "";
      this.showTeacherDropdown = false;
      this.highlightedIndex = -1;
      this.teacherList = [];

      // Foca novamente no campo de busca para facilitar a adição de mais professores
      this.$nextTick(() => {
        if (this.$refs.teacherSearchInput) {
          this.$refs.teacherSearchInput.focus();
        }
      });
    },

    /**
     * Manipula navegação por teclado no dropdown
     */
    handleKeydown(event) {
      if (!this.showTeacherDropdown || this.teacherList.length === 0) {
        return;
      }

      switch (event.key) {
        case "ArrowDown":
          event.preventDefault();
          this.highlightedIndex = Math.min(
            this.highlightedIndex + 1,
            this.teacherList.length - 1
          );
          break;
        case "ArrowUp":
          event.preventDefault();
          this.highlightedIndex = Math.max(this.highlightedIndex - 1, 0);
          break;
        case "Enter":
          event.preventDefault();
          if (
            this.highlightedIndex >= 0 &&
            this.highlightedIndex < this.teacherList.length
          ) {
            this.selectTeacher(this.teacherList[this.highlightedIndex]);
          }
          break;
        case "Escape":
          event.preventDefault();
          this.showTeacherDropdown = false;
          this.highlightedIndex = -1;
          break;
      }
    },

    /**
     * Manipula cliques fora do dropdown para escondê-lo
     */
    handleClickOutside(event) {
      if (
        this.$refs.teacherSearchContainer &&
        !this.$refs.teacherSearchContainer.contains(event.target)
      ) {
        this.showTeacherDropdown = false;
        this.highlightedIndex = -1;
      }
    },

    /**
     * Atualiza a UI após o carregamento dos dados
     */
    updateUIAfterLoading() {
      this.$nextTick(() => {
        this.updateFormFields();
        this.$forceUpdate();

        if (!this.localOfferClass.classname || !this.localOfferClass.enrol) {
          this.showErrorMessage("Dados incompletos após carregamento.");
        }
      });
    },

    /**
     * Atualiza os campos do formulário manualmente
     */
    updateFormFields() {
      this.updateSelectField("enrolSelect", this.localOfferClass.enrol);
      this.updateInputField("classnameInput", this.localOfferClass.classname);
      this.updateInputField("startdateInput", this.localOfferClass.startdate);
    },

    /**
     * Atualiza um campo de seleção
     */
    updateSelectField(ref, value) {
      if (this.$refs[ref]) {
        this.$refs[ref].value = value;
        const event = new Event("change");
        this.$refs[ref].$el.dispatchEvent(event);
      }
    },

    /**
     * Atualiza um campo de input
     */
    updateInputField(ref, value) {
      if (this.$refs[ref] && value) {
        this.$refs[ref].value = value;
        const event = new Event("input");
        this.$refs[ref].$el.dispatchEvent(event);
      }
    },

    /**
     * Volta para tela da oferta
     */
    goBack() {
      if (this.offerCourse.offerid) {
        this.router.push({
          name: "offer.edit",
          params: { id: this.offerCourse.offerid },
        });
      } else {
        this.router.push({ name: "offer.index" });
      }
    },

    /**
     * Exibe mensagem de erro
     * @param {string} message Mensagem a ser exibida
     */
    showErrorMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "error";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000);
      });
    },

    /**
     * Exibe mensagem de sucesso
     * @param {string} message Mensagem a ser exibida
     */
    showSuccessMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "success";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000);
      });
    },

    /**
     * Exibe mensagem de aviso
     * @param {string} message Mensagem a ser exibida
     */
    showWarningMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "warning";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000);
      });
    },

    /**
     * Método para atualizar manualmente os campos do formulário
     * Este método é chamado após o carregamento dos dados da turma
     */
    updateFormFields() {
      // Atualizar campo de método de inscrição
      if (this.$refs.enrolSelect && this.localOfferClass.enrol) {
        // Tentar diferentes abordagens para atualizar o campo
        try {
          // Abordagem 1: Atualizar diretamente o valor do componente
          this.$refs.enrolSelect.value = this.localOfferClass.enrol;

          // Abordagem 2: Disparar evento de mudança
          const changeEvent = new Event("change");
          this.$refs.enrolSelect.$el.dispatchEvent(changeEvent);

          // Abordagem 3: Atualizar o modelo e forçar atualização
          this.$refs.enrolSelect.$emit("input", this.localOfferClass.enrol);
          this.$refs.enrolSelect.$forceUpdate();
        } catch (error) {
          // Erro ao atualizar campo de método de inscrição
        }
      }

      // Atualizar campo de nome da turma
      if (this.$refs.classnameInput && this.localOfferClass.classname) {
        try {
          // Atualizar diretamente o valor do componente
          this.$refs.classnameInput.value = this.localOfferClass.classname;

          // Disparar evento de input
          const inputEvent = new Event("input");
          this.$refs.classnameInput.$el.dispatchEvent(inputEvent);

          // Atualizar o modelo e forçar atualização
          this.$refs.classnameInput.$emit(
            "input",
            this.localOfferClass.classname
          );
          this.$refs.classnameInput.$forceUpdate();
        } catch (error) {
          // Erro ao atualizar campo de nome da turma
        }
      }

      // Atualizar campo de data de início
      if (this.$refs.startdateInput && this.localOfferClass.startdate) {
        try {
          // Atualizar diretamente o valor do componente
          this.$refs.startdateInput.value = this.localOfferClass.startdate;

          // Disparar evento de input
          const inputEvent = new Event("input");
          this.$refs.startdateInput.$el.dispatchEvent(inputEvent);

          // Atualizar o modelo e forçar atualização
          this.$refs.startdateInput.$emit(
            "input",
            this.localOfferClass.startdate
          );
          this.$refs.startdateInput.$forceUpdate();
        } catch (error) {
          // Erro ao atualizar campo de data de início
        }
      }

      // Forçar atualização do componente principal
      this.$forceUpdate();
    },

    /**
     * Seleciona todas as situações para rematrícula
     */
    handleSelectAllReenrolSituations() {
      const allSelected = this.reenrolSituationList.every((option) =>
        this.reenrolSituations.some(
          (selected) => selected.value === option.value
        )
      );

      this.reenrolSituations = allSelected
        ? []
        : [...this.reenrolSituationList];

      this.localOfferClass.optional_fields.reenrolmentsituations =
        this.reenrolSituations.map((item) => item.value);
    },

    /**
     * Método para reiniciar o componente
     * Este método é chamado após o carregamento dos dados da turma
     * para garantir que todos os campos sejam atualizados corretamente
     */
    restartComponent() {
      // Garantir que a página role para o topo
      window.scrollTo(0, 0);

      // Atualizar manualmente os campos do formulário
      this.updateFormFields();

      // Forçar atualização do componente
      this.$forceUpdate();

      // Adicionar um pequeno atraso e forçar nova atualização
      setTimeout(() => {
        this.updateFormFields();
        this.$forceUpdate();

        // Garantir novamente que a página role para o topo após a atualização
        window.scrollTo(0, 0);
      }, 500);
    },
  },
};
</script>

<style lang="scss" scoped>
.validation-alert {
  background-color: #332701;
  border: 1px solid #997404;
  color: #ffda6a;
  padding: 1rem;
  margin: 0 1rem 1rem 1rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 0.75rem;

  i {
    font-size: 1.25rem;
  }

  span {
    flex: 1;
  }
}

.section-container {
  margin: 0;
  background-color: #212529;
  border-radius: 4px;
  padding: 0 1rem;
}

.section-title {
  color: var(--primary);
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-transform: uppercase;
}

.form-group {
  margin-bottom: 1rem;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  margin: 0;
}

.form-label {
  display: inline-block;
  color: #fff;
  font-size: 14px;
  margin-bottom: 0;
  margin-right: 5px;
}

.label-with-help {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;

  .form-label {
    margin-right: 2px;
  }
}

.input-with-help {
  display: flex;
  align-items: center;
}

.checkbox-container {
  display: flex;
  align-items: center;
}

.input-with-checkbox {
  display: flex;
  align-items: center;
  gap: 10px;
}

.inline-checkbox {
  margin-left: 10px;
}

.text-editor-container {
  max-width: 700px;
}

.actions-container {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.fa-exclamation-circle {
  margin-right: 0;
}

.required-fields-message {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.form-info {
  background-color: #212529;
  color: #fff;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.limited-width-select {
  max-width: 500px;
}

.limited-width-input {
  max-width: 180px;
}

.limited-width-editor {
  max-width: 700px;
}

.date-input {
  :deep(input[type="date"]) {
    color-scheme: dark;

    /* Esconde o ícone nativo do calendário */
    &::-webkit-calendar-picker-indicator {
      opacity: 0;
    }
  }

  :deep(.calendar-icon) {
    color: #fff;
    z-index: 1;
  }
}

/* Estilos para campos desabilitados */
.form-group {
  &.disabled {
    .form-label,
    .checkbox-label {
      opacity: 0.65;
    }

    img.help-icon {
      opacity: 0.65;
    }

    .custom-input,
    .custom-select,
    .editor-container,
    .form-control {
      opacity: 0.65;
      cursor: not-allowed;
    }
  }

  /* Estilo para campos dependentes quando o campo principal está desabilitado */
  &.dependent-field {
    &.disabled {
      position: relative;
    }
  }
}
</style>
